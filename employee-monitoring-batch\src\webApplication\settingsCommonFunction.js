// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require table alias
const { ehrTables,appManagerTables } = require('../common/tableAlias');

// function to update the organization settings based on input action
async function updateOrganizationSettings(organizationDb,settingsArray,loggedInEmpId,employeeTimeZone,action)
{
    let params;
    try{
        return(
            organizationDb
            .transaction(function(trx){
                switch (action) {
                    // if function is called from frequency function then update in corresponding table
                    case 'frequency':
                        let frequencyInput=settingsArray[0];
                        params=   
                        // update the screenshot frequency in employee monitor settings table               
                        organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            Capture_Screenshot: frequencyInput.captureScreenshot,
                            Screenshot_Frequency: frequencyInput.screenshotFrequency ? frequencyInput.screenshotFrequency : 10, // Default value
                            No_Of_Screenshots_Per_Frequency: frequencyInput.numberOfScreenshots ? frequencyInput.numberOfScreenshots : 0, // Default value
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(async(updateDetails) => {
                            return 'success';                                 
                        })
                    break;
                    case 'delete':
                        let deleteInput=settingsArray[0];
                        // update the delete screenshot flag in employee monitor settings table
                        params= organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            Delete_Screenshots: deleteInput.deleteScreenshot,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(async(updateDetails) => {
                            return 'success';                                 
                        })
                    break;
                    case 'blur':
                        let blurInput=settingsArray[0];
                        // update the screenshot blur flag in employee monitor settings table
                         params=organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            Screenshot_Blur: blurInput.isScreenshotBlur,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(async(updateDetails) => {
                            return 'success';                                 
                        })
                    break;
                    case 'workHours':
                        let workHoursInput=settingsArray[0];
                        // update the work hours goal in employee monitor settings table
                        params=organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            Work_Hours_Goal_Per_Week: workHoursInput.workHoursGoal,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(async(updateDetails) => {
                            return 'success';                                 
                        })
                    break;
                    case 'activityGoal':
                        let activityInput=settingsArray[0];
                        // update the activity goal in employee monitor settings table
                        params=organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            Low_Activity_Min_Level: activityInput.lowActivityMinimum,
                            Low_Activity_Max_Level:activityInput.lowActivityMaximum,
                            Moderate_Activity_Min_Level:activityInput.moderateActivityMinimum,
                            Moderate_Activity_Max_Level:activityInput.moderateActivityMaximum,
                            High_Activity_Min_Level:activityInput.highActivityMinimum,
                            High_Activity_Max_Level:activityInput.highActivityMaximum,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(async(updateDetails) => {
                            return 'success';                                 
                        })
                    break;
                    case 'appUrl':
                        let appUrlInput=settingsArray[0];
                        // update the track app and url in employee monitor settings table
                        params=organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            Track_App_Url: appUrlInput.trackAppUrl,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(async(updateSettings) => {
                            return 'success';                                 
                        })
                    break;
                    case 'idleTime':
                        let idleTimeInput=settingsArray[0];
                        // update the idle time in employee monitor settings table
                        params=organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            Idle_Time: idleTimeInput.idleTime,
                            Consider_Idle_NotActive_Time:idleTimeInput.considerIdleNotActiveTime,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(() => {
                            return 'success';                                 
                        })
                    break;
                    case 'workSchedule':
                        let workScheduleInput=settingsArray[0];
                        // update the work schedule in employee monitor settings table
                        params=organizationDb(ehrTables.employeeMonitorSettings)
                        .update({
                            WorkSchedule_Id: workScheduleInput.workScheduleId,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .transacting(trx)
                        .then(() => {
                            return 'success';                                 
                        })
                    break;
                }
            // execute the update function and return response
            return(params)
            })  
            //return the success result to user
            .then(function (result) {
                return result;
            })                    
            .catch(function (updateError) {
                console.log('Error while updateOrganizationSettings function .catch block',updateError);
                return 'error';                                 
            })   
        );                     
    }
    catch(errorInUpdating)
    {
        console.log('Error in updateOrganizationSettings function catch block',errorInUpdating);
        return 'error';                                 
    }
}

// Function to update employee level screenshot settings based on action
async function updateEmployeeSettings(organizationDb,settingsArray,loggedInEmpId,employeeTimeZone,action)
{
    try{
        const queries = [];
        // since multiple input so batch update is done 
        // update the value based on employee id
        return (organizationDb.transaction(trx => {
            switch (action) {
                // if the input action is frequency then update the employee_level_screenshot_frequency_settings table
                case 'frequency':
                    settingsArray.forEach(empSettings => {
                        const query=organizationDb(ehrTables.employeeLevelScreenshotFrequencySettings)
                        .update({
                            Capture_Screenshot:empSettings.captureScreenshot,
                            Screenshot_Frequency: empSettings.screenshotFrequency ? empSettings.screenshotFrequency : 10, //default values
                            No_Of_Screenshots_Per_Frequency: empSettings.numberOfScreenshots ? empSettings.numberOfScreenshots : 0, // default values
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .where('Employee_Id',empSettings.employeeId)
                        .transacting(trx); // This makes every update be in the same transaction
                        queries.push(query);
                    });
                    Promise.all(queries) // Once every query is written
                        .then(trx.commit) // We try to execute all of them
                        .catch(trx.rollback); // And rollback in case any of them goes wrong
                break;
                // if the input action is delete then update the employee_level_delete_screenshot_settings table
                case 'delete':
                    settingsArray.forEach(inputUser => {
                        const query=organizationDb(ehrTables.employeeLevelDeleteScreenshotSettings)
                        .update({
                            Delete_Screenshots:inputUser.deleteScreenshot,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .where('Employee_Id',inputUser.employeeId)
                        .transacting(trx); // This makes every update be in the same transaction
                        queries.push(query);
                    });
                    Promise.all(queries) // Once every query is written
                        .then(trx.commit) // We try to execute all of them
                        .catch(trx.rollback); // And rollback in case any of them goes wrong
                break;
                case 'blur':
                    // update the screenshot blur flag for all the employees in emp settings array
                    settingsArray.forEach(user => {
                        const query=organizationDb(ehrTables.employeeLevelScreenshotBlurSettings)
                        .update({
                            Screenshot_Blur:user.isScreenshotBlur,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .where('Employee_Id',user.employeeId)
                        .transacting(trx); // This makes every update be in the same transaction
                        queries.push(query);
                    });
                    Promise.all(queries) // Once every query is written
                        .then(trx.commit) // We try to execute all of them
                        .catch(trx.rollback); // And rollback in case any of them goes wrong                
                break;
                // if the action is work hours then update in employee_level_work_hours_goals_settings table
                case 'workHours':
                    settingsArray.forEach(inputUser => {
                        const query=organizationDb(ehrTables.employeeLevelWorkHoursGoalsSettings)
                        .update({
                            Work_Hours_Goal_Per_Week: inputUser.workHoursGoal,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .where('Employee_Id',inputUser.employeeId)
                        .transacting(trx); // This makes every update be in the same transaction
                        queries.push(query);
                    });
                    Promise.all(queries) // Once every query is written
                        .then(trx.commit) // We try to execute all of them
                        .catch(trx.rollback); // And rollback in case any of them goes wrong
                break;
                // if the action is activityGoal then update in employee_level_activity_goal_settings table
                case 'activityGoal':
                    settingsArray.forEach(inputUser => {
                    const query=organizationDb(ehrTables.employeeLevelActivityGoalSettings)
                    .update({
                        Low_Activity_Min_Level: inputUser.lowActivityMinimum,
                        Low_Activity_Max_Level:inputUser.lowActivityMaximum,
                        Moderate_Activity_Min_Level:inputUser.moderateActivityMinimum,
                        Moderate_Activity_Max_Level:inputUser.moderateActivityMaximum,
                        High_Activity_Min_Level:inputUser.highActivityMinimum,
                        High_Activity_Max_Level:inputUser.highActivityMaximum,
                        Updated_On:employeeTimeZone,
                        Updated_By:loggedInEmpId
                    })
                    .where('Employee_Id',inputUser.employeeId)
                    .transacting(trx); // This makes every update be in the same transaction
                    queries.push(query);
                });
                Promise.all(queries) // Once every query is written
                    .then(trx.commit) // We try to execute all of them
                    .catch(trx.rollback); // And rollback in case any of them goes wrong
                break; 
                case 'appUrl':
                    // update the app and url settings for the employee
                    settingsArray.forEach(user => {
                        const query=organizationDb(ehrTables.employeeAppUrlSettings)
                        .update({
                            Track_App_Url:user.trackAppUrl,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .where('Employee_Id',user.employeeId)
                        .transacting(trx); // This makes every update be in the same transaction
                        queries.push(query);
                    });
                    Promise.all(queries) // Once every query is written
                        .then(trx.commit) // We try to execute all of them
                        .catch(trx.rollback); // And rollback in case any of them goes wrong                
                break;  
                case 'idleTime':
                    // update the idle time settings for the employee
                    settingsArray.forEach(user => {
                        const query=organizationDb(ehrTables.employeeLevelIdleTimeSettings)
                        .update({
                            Idle_Time: user.idleTime,
                            Consider_Idle_NotActive_Time:user.considerIdleNotActiveTime,
                            Updated_On:employeeTimeZone,
                            Updated_By:loggedInEmpId
                        })
                        .where('Employee_Id',user.employeeId)
                        .transacting(trx); // This makes every update be in the same transaction
                        queries.push(query);
                    });
                    Promise.all(queries) // Once every query is written
                        .then(trx.commit) // We try to execute all of them
                        .catch(trx.rollback); // And rollback in case any of them goes wrong                
                break;
                case 'workSchedule':
                    // update the work schedule Id for the input employees
                    settingsArray.forEach(user => {
                        const query=organizationDb(ehrTables.empJob)
                        .update({
                            Work_Schedule: user.workScheduleId
                        })
                        .where('Employee_Id',user.employeeId)
                        .transacting(trx); // This makes every update be in the same transaction
                        queries.push(query);
                    });
                    Promise.all(queries) // Once every query is written
                        .then(trx.commit) // We try to execute all of them
                        .catch(trx.rollback); // And rollback in case any of them goes wrong                
                break;
            }
            })
            .then(function (result) {
                return result;
            }) 
            .catch(function (updateError) {
                console.log('Error while updateEmployeeSettings function .catch block',updateError);
                return 'error';                                 
            })                      
        );
    }
    catch(errorInUpdateFunction)
    {
        console.log('Error in updateEmployeeSettings function catch block',errorInUpdateFunction);
        return 'error';                                 
    }
}

// function to add system logs
async function addingSystemLogs(userIp,logEmployeeId,organizationDb,operation,formName,idArray,action,input)
{
    let functionName;
    try{
        let errorCodeArray=['EM0018','EM0030'];
        // form function name based on action
        switch (action) {
            case 'frequency':
                functionName='Screenshot frequency settings';
            break;
            case 'delete':
                functionName='Delete screenshot settings';
            break;
            case 'blur':
                functionName='Screenshot blur settings';
            break;
            case 'workHours':
                functionName='Work hours goal settings';
            break;
            case 'activityGoal':
                functionName='Activity goal settings';
            break;
            case 'appUrl':
                functionName='Application and Url settings';
            break;
            case 'idleTime':
                functionName='Idle time settings';
            break;
            case 'workSchedule':
                functionName='Work schedule settings';
            break;
        }

        //formation of system log params
        let systemLogParams = {
            action: operation,
            userIp: userIp,
            employeeId: logEmployeeId,
            formName: formName,
            trackingColumn: functionName,
            organizationDbConnection: organizationDb,
            uniqueId: ''
        }
        // formation of system log message based on condition
        if(input==='orgEmployeeSettings')
        {
            systemLogParams.message =  operation +' '+ formName + ' '+ functionName + ' both organization and for the employee Id(s)- '+idArray;
        }
        else if(input==='empSettings')
        {
            systemLogParams.message = operation +' '+ formName + ' '+ functionName +' for the employee Id(s)- '+idArray;
        }
        else if(errorCodeArray.includes(input)){
            systemLogParams.message = operation +' '+ formName + ' '+ functionName +' for organization level but error while updating employee level settings.';
        }
        else{
            systemLogParams.message = operation +' '+ formName + ' '+ functionName + ' for organization level.'
        }

        // function to create system logs
        let createLog=  await commonLib.func.createSystemLogActivities(systemLogParams);
        return createLog;
    }
    catch(error){
        console.log("Error in addingSystemLogs function main catch block",error);
        return '';
    }
};
// function to get employee monitor settings for both organization and employee level
async function getEmployeeMonitorSettings(type,transaction,organizationDbConnection,logInEmpId=null) {
    // variable declrations
    let organizationLevelArray = [];
    let employeeLevelArray = [];
    let empLevelTableName = '';
    let employeeSettingsQuery;
    try{        
        // check the type based on that form select query for both organization level and employee level
        switch(type){
            case 'Screenshots Blur': // if the type is Screenshots Blur
                orgLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .select('Screenshot_Blur as screenshot_blur');
                empLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeLevelScreenshotBlurSettings)
                                        .select('EMS.Employee_Id as employee_id', 'EMS.Screenshot_Blur as screenshot_blur');
                empLevelTableName = ehrTables.employeeLevelScreenshotBlurSettings;
                break;
            case 'Screenshots Delete': // if the type is Screenshots Delete
                orgLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .select('Delete_Screenshots as delete_screenshots');
                empLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeLevelDeleteScreenshotSettings)
                                        .select('EMS.Employee_Id as employee_id', 'EMS.Delete_Screenshots as delete_screenshots');
                empLevelTableName = ehrTables.employeeLevelDeleteScreenshotSettings;
                break;
            case 'Screenshots Frequency': // if the type is Screenshots Frequency
                orgLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .select('Capture_Screenshot as capture_screenshot','Screenshot_Frequency as screenshot_frequency', 
                                        'No_Of_Screenshots_Per_Frequency as no_of_screenshots_per_frequency');
                empLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeLevelScreenshotFrequencySettings)
                                        .select('EMS.Employee_Id as employee_id', 'EMS.Capture_Screenshot as capture_screenshot', 'EMS.Screenshot_Frequency as screenshot_frequency',
                                        'EMS.No_Of_Screenshots_Per_Frequency as no_of_screenshots_per_frequency');
                empLevelTableName = ehrTables.employeeLevelScreenshotFrequencySettings;
                
                break;
            case 'Work Hours Goal': // If the type is Work Hours Goal
                orgLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .select('Work_Hours_Goal_Per_Week as work_hours_goal_per_week');
                empLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeLevelWorkHoursGoalsSettings)
                                        .select('EMS.Employee_Id as employee_id', 'EMS.Work_Hours_Goal_Per_Week as work_hours_goal_per_week');
                empLevelTableName = ehrTables.employeeLevelWorkHoursGoalsSettings;

                break;
            case 'Work Activity Goal': // If the type is Work Activity Goal
                orgLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .select('Low_Activity_Min_Level as lowActivityMinimum','Low_Activity_Max_Level as lowActivityMaximum',
                                        'Moderate_Activity_Min_Level as moderateActivityMinimum','Moderate_Activity_Max_Level as moderateActivityMaximum',
                                        'High_Activity_Min_Level as highActivityMinimum','High_Activity_Max_Level as highActivityMaximum');
                empLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeLevelActivityGoalSettings)
                                        .select('EMS.Employee_Id as employee_id', 'EMS.Low_Activity_Min_Level as lowActivityMinimum', 'EMS.Low_Activity_Max_Level as lowActivityMaximum',
                                        'EMS.Moderate_Activity_Min_Level as moderateActivityMinimum', 'EMS.Moderate_Activity_Max_Level as moderateActivityMaximum',
                                        'EMS.High_Activity_Min_Level as highActivityMinimum', 'EMS.High_Activity_Max_Level as highActivityMaximum');
                empLevelTableName = ehrTables.employeeLevelActivityGoalSettings;
                break;
            case 'App and Url': // If the type is App and Url
                orgLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .select('Track_App_Url as track_app_url');
                empLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeAppUrlSettings)
                                        .select('EMS.Employee_Id as employee_id','Track_App_Url as track_app_url');
                empLevelTableName = ehrTables.employeeAppUrlSettings;
                break;
            case 'Idle Time': // If the type is Idle Time
                orgLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeMonitorSettings)
                                        .select('Idle_Time as idleTime','Consider_Idle_NotActive_Time as considerIdleNotActiveTime');
                empLevelSelectSubQuery = organizationDbConnection(ehrTables.employeeLevelIdleTimeSettings)
                                        .select('EMS.Employee_Id as employeeId','Idle_Time as idleTime','Consider_Idle_NotActive_Time as considerIdleNotActiveTime');
                empLevelTableName = ehrTables.employeeLevelIdleTimeSettings;
                break;
            default :
                orgLevelSelectSubQuery = '';
                empLevelSelectSubQuery = '';
                break;
        }
        // get the employee monitor setting organization level
        return(
            orgLevelSelectSubQuery
            .transacting(transaction)
            .then(orgLevelSettings=>{
                // get and assign the response
                organizationLevelArray = orgLevelSettings;
                let subQuery=
                empLevelSelectSubQuery
                .from(empLevelTableName + ' as EMS')
                .innerJoin(ehrTables.teamMembers + ' as EMTM', 'EMS.Employee_Id','EMTM.Employee_Id')
                .where('EMTM.Member_Status', 'Active')
                // if loggedIn employeeid available in input then get only that record else all the employee
                if(!logInEmpId){
                    employeeSettingsQuery=subQuery
                }
                else{
                    employeeSettingsQuery=subQuery
                    .where('EMS.Employee_Id',logInEmpId)
                }
                // get the employee monitor settings employee level
                return(
                    employeeSettingsQuery
                    .transacting(transaction)
                    .then(employeeLevelSettings=>{
                        employeeLevelArray = employeeLevelSettings;
                        // return response back
                        return { empLevelSettings : employeeLevelArray , orgLevelSettings :  organizationLevelArray }
                    })
                )
            })
            .catch(getEmployeeMonitorSettingsInsideCatchError=>{
                console.log('Error in getEmployeeMonitorSettings() function  .catch block.', getEmployeeMonitorSettingsInsideCatchError);
                // throw error
                throw (getEmployeeMonitorSettingsInsideCatchError);
            })
        )
    }catch(getEmployeeMonitorSettingsCatchError){
        console.log('Error in getEmployeeMonitorSettings() function catch block.', getEmployeeMonitorSettingsCatchError);
        // throw error
        throw (getEmployeeMonitorSettingsCatchError);
    }
};

// function to get log settings
async function getLogSettings(organizationDbConnection,employeeId){
    try{
        return(
            organizationDbConnection(ehrTables.employeeLevelIdleTimeSettings)
            .select('Logging_Levels')
            .where('Employee_Id', employeeId)
            .then(getData=>{
                if(getData.length>0){
                    return getData[0].Logging_Levels;
                }
                else{
                    return 'Info';
                }
            })
            .catch(error => {
                console.log('Error in getLogSettings function .catch block.',error);
                return 'Info';
            })
        );
    }
    catch (catchError) {
        console.log('Error in getLogSettings function main catch block', catchError);
        return 'Info';
    }
};

// function to get organization activity goals settings
async function getOrganizationModerateGoalSettings(organizationDbConnection){
    try{
        // get the moderate activity goal settings
        let orgLevelModerateGoalSettings = await organizationDbConnection(ehrTables.employeeMonitorSettings).pluck('Moderate_Activity_Min_Level');            
        orgLevelModerateGoalSettings=parseInt(orgLevelModerateGoalSettings[0]);
        return orgLevelModerateGoalSettings;
    }
    catch (catchError) {
        console.log('Error in getOrganizationModerateGoalSettings function main catch block', catchError);
        return '';
    }
};

// get organization level consider idle and not active time settings
async function getOrgLevelIdleTimeSettings(organizationDbConnection){
    try{
        return(
            organizationDbConnection
            .select('Consider_Idle_NotActive_Time')
            .from(ehrTables.employeeMonitorSettings)
            .then(getDetails=>{
                return (getDetails.length>0)?(getDetails[0].Consider_Idle_NotActive_Time):'';
            })
            .catch(error => {
                console.log('Error in getOrgLevelIdleTimeSettings function .catch block.',error);
                return '';
            })
        );
    }
    catch(error){
        console.log('Error in getOrgLevelIdleTimeSettings function catch block',error);
        return '';
    }
};

// function to get the organization level associated work scheduleId
async function getOrgLevelWorkScheduleId(organizationDbConnection){
    try{
        return(
            organizationDbConnection(ehrTables.employeeMonitorSettings)
            .select('WorkSchedule_Id')
            .then(getId => {
                if(getId.length>0){
                    if(getId[0].WorkSchedule_Id){
                        return getId[0].WorkSchedule_Id;
                    }
                    else{
                        throw 'EM0194';
                    }
                }
                else{
                    throw 'EM0194';
                }
            })
            .catch(error => {
                console.log('Error in getOrgLevelWorkScheduleId function .catch block.',error);
                throw (error==='EM0194')?'EM0194':'EM0193';
            })
        )
    }
    catch(error){
        console.log('Error in getOrgLevelWorkScheduleId function main catch block.',error);
        throw (error==='EM0194')?'EM0194':'EM0193';
    }
};

exports.updateOrganizationSettings = updateOrganizationSettings;
exports.updateEmployeeSettings = updateEmployeeSettings;
exports.addingSystemLogs = addingSystemLogs;
exports.getEmployeeMonitorSettings = getEmployeeMonitorSettings;
exports.getLogSettings=getLogSettings;
exports.getOrganizationModerateGoalSettings=getOrganizationModerateGoalSettings;
exports.getOrgLevelIdleTimeSettings=getOrgLevelIdleTimeSettings;
exports.getOrgLevelWorkScheduleId=getOrgLevelWorkScheduleId;