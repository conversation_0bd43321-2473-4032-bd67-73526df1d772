'use strict';
// require common function
const { updateStatusInOrganizationStatusTable, updateSummaryDataInMasterTable }=require('./commonFunctions');
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');

// variable declarations
let summarizationStatusTable;
let masterTable;
let inputSource;
let inputStatus;
let orgCode;
let reason;

// function to handle the failures from step1 and step2
module.exports.dailySummaryErrorState  = async(event, context) =>{
    try{
        console.log('Inside dailySummaryErrorState function',event);
        // get input data
        let inputData=event.input;
        inputSource=inputData.process;
        inputStatus=inputData.status;
        orgCode=(inputData.orgCode)?inputData.orgCode:'';
        reason=(inputData.reason)?inputData.reason:null;
        let updateStatus={};

        if(orgCode){
            // Same logic is used for different input source so define the tables
            if(inputSource==='app'){
                summarizationStatusTable=ehrTables.appActivitySummarizationStatus;
                masterTable=appManagerTables.appActivitySummarizationManager;
            }
            else if(inputSource==='appTitle')
            {
                summarizationStatusTable=ehrTables.appTitleActivitySummarizationStatus;
                masterTable=appManagerTables.appTitleActivitySummarizationManager;
            }
            else if(inputSource==='url'){
                summarizationStatusTable=ehrTables.urlActivitySummarizationStatus;
                masterTable=appManagerTables.urlActivitySummarizationManager;
            }
            else if(inputSource==='productivity'){
                summarizationStatusTable=ehrTables.appurlActivitySummarizationStatus;
                masterTable=appManagerTables.appurlActivitySummarizationManager;
            }
            else{
                summarizationStatusTable=ehrTables.employeeActivitySummarizationStatus;
                masterTable=appManagerTables.employeeActivitySummarizationManager;
            };
            if(inputSource==='productivity'){
                // formation of params to update the failure
                let updateOrgParams={
                    Transaction_Status : 'Failed',
                    Summarization_Time:new Date(),
                    Reason:reason                
                }
                // function to update failed status in organization status table
                updateStatus=await updateStatusInOrganizationStatusTable(updateOrgParams,summarizationStatusTable,orgCode,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,['Started','Executing']);
                console.log('Failed status updated in '+orgCode+' instance',updateStatus);
            }
            else{
                // formation of params to update the failure
                let updateOrgParams={
                    Transaction_Status : 'Failed',
                    Execution_Status:'Completed',
                    Summarization_Time:new Date(),
                    Reason:reason                 
                }
                // function to update failed status in organization status table
                updateStatus=await updateStatusInOrganizationStatusTable(updateOrgParams,summarizationStatusTable,orgCode,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,['Started','Executing']);
                console.log('Failed status updated in '+orgCode+' instance',updateStatus);
            }
            if(updateStatus){
                let updateParams={};
                // if input status is failed status then it will be event to retry the failed records so we will be updating it as unsuccessful(after retrying once)
                if(inputStatus==='Failed')
                {
                    updateParams.Status='Unsuccessful';
                }
                else{
                    updateParams.Status='Failed';
                }
                // updated failed status in manager table
                let updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,masterTable,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                console.log('Failed status updated in manager table for '+orgCode+' instance',updateMasterTableStatus);
                if(updateMasterTableStatus){
                    let response = 
                    {
                        nextStep:'End',
                        input:{'process':inputSource,'status':inputStatus},
                        message:'Summarization failed for '+inputSource+ ' process in ' +inputSource+' instance'
                    };
                    return response;
                }
                else{
                    console.log('Error while updating failed status in app manager database for '+orgCode+' instance');
                }
            }
            else{
                console.log('Error while updating failed status in organization database for '+orgCode+' instance');
            }
        }
        else{
            console.log('No orgcode found to update failure status so quit the process');
            let response = 
            {
                nextStep:'End',
                input:{'process':inputSource,'status':inputStatus},
                message:'Summarization process failed for some records.'
            };
            return response;
        }
    } catch (mainCatchError){
        console.log('Error in dailySummaryErrorState function main catch block.', mainCatchError);
        let response = 
        {
            nextStep:'End',
            input:{'process':inputSource,'status':inputStatus},
            message:'Summarization process failed for some records.'
        };
        return response;
    }
};
