'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const commonFunction = require('../common/initiateStepFunction');

// Function to initiate daily summary step function
module.exports.initiateDailySummaryStepFunction  = async(event, context) =>{
    try{
        console.log('Inside initiateDailySummaryStepFunction function',event);
        // based on event define the status
        // We will be triggering the step function in 2 different cases.
        // We will trigger daily summary routine and separate event for retrying the failed instance so based on input differentiate the parms
        let status=(!event.status)?'':(event.status==='Open')?'Open':'Failed';
        // Separate function trigger for 3 different process. app/url/employee activity
        // based on which process will be differentiate
        // Incase of failure we need to trigger remaining function so we log only the message 
        let triggerAppActivity= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'app',status);
        console.log('Response after triggering app daily summary step function',triggerAppActivity);

        let triggerAppTitleActivity= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'appTitle',status);
        console.log('Response after triggering app title daily summary step function',triggerAppTitleActivity);

        let triggerUrlActivity= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'url',status);
        console.log('Response after triggering url daily summary step function',triggerUrlActivity);

        let triggerActivity= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'activity',status);
        console.log('Response after triggering activity daily summary step function',triggerActivity);

        return {errorCode:'',message: 'Daily summary step function initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateDailySummaryStepFunction function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EM0061');
        return {errorCode:errResult.code,message: errResult.message};
    }
};

// function to initiate productivity daily summary step function
module.exports.initiateProductivitySummary  = async(event, context) =>{
    try{
        console.log('Inside initiateProductivitySummary function',event);
        /** We will trigger daily productivity summary routine so based on input differentiate the params */
        let status=(!event.status)?'':'Open';
        let triggerProductivityActivity= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'productivity',status);
        console.log('Response after triggering productivity daily summary step function',triggerProductivityActivity);
        return { errorCode:'',message: 'Productivity summary step function initiated successfully.' };
    }
    catch(mainCatchError){
        console.log('Error in initiateProductivitySummary function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EM0061');
        return { errorCode:errResult.code,message: errResult.message };
    }
};
