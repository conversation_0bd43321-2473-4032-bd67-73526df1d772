/** Alias name for ehr tables */
module.exports.ehrTables = {
    empUser: 'emp_user',
    ipAddressWhitelist: 'ip_address_whitelisting',
    whitelistedIps: 'whitelisted_ip_addresses',
    location: 'location',
    orgDetails: 'org_details',
    taxconfiguration: 'tax_configuration',
    employeeMonitorSettings: 'employee_monitor_settings',
    domainSettings: "domain_settings",
    hrappRegisterUser: "hrapp_registeruser",
    hrappLogin: "hrapp_login",
    teamMembers: 'em_members',
    department: 'department',
    empJob: 'emp_job',
    leavetype: 'leave_types',
    attendanceSettings: 'attendance_settings',
    attendance: 'emp_attendance',
    empLeaves: 'emp_leaves',
    empPersonalInfo: 'emp_personal_info',
    billingRate: 'billing_rate',
    hrappPlanForm: 'hrapp_plan_form',
    leaveClosureConfiguration: 'leave_closure_configuration',
    salaryPayslip: 'salary_payslip',
    monthlyLeaveBalance: 'monthly_leave_balance',
    modules: 'modules',
    customizationForms: 'customization_forms',
    forms: 'forms',
    designation: 'designation',
    empActivityDetails: 'employee_activity_details',
    empActivityScreenshots: 'employee_activity_screenshots',
    empAccessRights: 'emp_accessrights',
    announcements: 'announcements',
    empType: 'employee_type',
    workSchedule: 'work_schedule',
    workScheduleWeekoff: 'workschedule_weekoff',
    shiftEmpMapping: 'shift_emp_mapping',
    empShiftType: 'emp_shift_type',
    contractEmployeeTdsConfiguration: 'contract_employee_tds_configuration',
    contractorTaxRates: 'contractor_tax_rates',
    contractorTaxSection: 'contractor_tax_section',
    employeeLevelScreenshotBlurSettings: 'employee_level_screenshot_blur_settings',
    employeeLevelDeleteScreenshotSettings: 'employee_level_delete_screenshot_settings',
    employeeLevelIdleTimeSettings: 'employee_level_idle_time_settings',
    employeeLevelScreenshotFrequencySettings: 'employee_level_screenshot_frequency_settings',
    employeeLevelWorkHoursGoalsSettings: 'employee_level_work_hours_goals_settings',
    employeeLevelActivityGoalSettings: 'employee_level_activity_goal_settings',
    empMonitoringRoles: 'employee_monitoring_roles',
    empMonitoringOnboardProperties: 'employee_monitoring_onboard_properties',
    timezone: 'timezone',
    empActivityApps: 'employee_activity_apps',
    empActivityUrls: 'employee_activity_urls',
    activityAppMaster: 'activity_tracker_app_master',
    activityDomainMaster: 'activity_tracker_domain_master',
    employeeAppUrlSettings: 'employee_level_app_url_settings',
    organizationLevelAppSettings: 'organization_level_app_settings',
    employeeActivityDailySummary: 'employee_activity_daily_summary',
    organizationLevelUrlSettings: 'organization_level_url_settings',
    appActivityDailySummary: 'app_activity_daily_summary',
    urlActivityDailySummary: 'url_activity_daily_summary',
    appActivitySummarizationStatus: 'app_activity_summarization_status',
    urlActivitySummarizationStatus: 'url_activity_summarization_status',
    employeeActivitySummarizationStatus: 'employee_activity_summarization_status',
    assetManagement: 'asset_management',
    ehrForms: 'ehr_forms',
    ehrRoles: 'ehr_roles',
    insightsNotificationStatusLog: 'insights_notification_status_log',
    employeeLevelInsightsNotificationSettings: 'employee_level_insights_notification_settings',
    dataIntegrationLog: 'data_integration_log',
    empAttendanceImport: 'emp_attendance_import',
    serviceProvider: 'service_provider',
    contactDetails: 'contact_details',
    empInsurancePolicyNo: 'emp_insurancepolicyno',
    insuranceType: 'insurance_type',
    country: 'country',
    employeeInfoTimestampLog: 'employee_info_timestamp_log',
    customGroupAppSettings: 'custom_group_app_settings',
    customGroupUrlSettings: 'custom_group_url_settings',
    customGroupAssociatedForms: 'custom_group_associated_forms',
    customEmployeeGroupEmployees: 'custom_employee_group_employees',
    empResignation: 'emp_resignation',
    appurlActivitySummarizationStatus: 'appurl_activity_summarization_status',
    appurlActivityDailySummary: 'appurl_activity_daily_summary',
    weekOffDates: 'weekoff_dates',
    uiRouterLog: 'ui_router_log',
    userExclusionList: 'AutoOnboard_User_Exclusion_List',
    organizationLevelFileTransferSettings: "organization_level_file_transfer_settings",
    employeeLevelFileTransferSettings: "employee_level_file_transfer_settings",
    customEmployeeGroup: "custom_employee_group",
    employeeFileTransferDetails: 'employee_file_transfer_details',
    customEmployeeGroupModuleForms: 'custom_employee_group_module_forms',
    employeeLevelFileTransferSummarizationManager: 'employee_level_file_transfer_summarization_manager',
    fileExtensionSummary: 'file_extension_summary',
    employeeFileTransferDetailsAsPerWs: 'employee_file_transfer_details_asper_ws',
    fileAndWebEventSummary: 'file_and_web_event_summary',
    webEventSummary: 'web_event_summary',
    customGroupRefreshStatus: 'custom_group_refresh_status',
    appTitleActivityDailySummary: "app_title_activity_daily_summary",
    appTitleActivitySummarizationStatus: "app_title_activity_summarization_status",
    customReportHeadersConfiguration: "custom_report_headers_configuration",
    customGroupUrlDomainWhiteListing: "custom_group_url_domain_white_listing",
    organizationLevelUrlDomainWhiteListing: "organization_level_url_domain_white_listing",
    rolesBasedAccessControl: 'rolesbased_access_control',
    employeeLevelDlpSettings: 'employee_level_dlp_settings',
    employeeLevelDomainsTobeBlocked: 'employee_level_domains_tobe_blocked',
    organizationLevelDlpSettings: 'organization_level_dlp_settings',
    organizationLevelDomainsTobeBlocked: 'organization_level_domains_tobe_blocked',
    organizationLevelWcBlockedDomains: 'organization_level_wc_blocked_domains',
    employeeLevelWcBlockedDomains: 'employee_level_wc_blocked_domains',

    jobPost: 'job_post',
    organizationGroup: 'organization_group',
    atsStatusTable: 'ats_status_table',
    SFWPOrganizationStructure: 'SFWP_Organization_Structure',
    city: 'city',
    state: 'state',
    currency: 'currency',
    jobPostQualifications: 'job_post_qualifications',
    courseDetails: 'course_details',
    jobPostRounds: 'job_post_rounds',
    rounds: 'rounds',
    workflows: 'workflows',
    jobPostRecruiters: 'job_post_recruiters',
    jobPostPanelMembers: 'job_post_panel_members',
    jobPostHiringManagers: 'job_post_hiring_managers',
    jobPostOnboardSpecialist: 'job_post_onboard_specialist',
    candidatePersonalInfo: 'candidate_personal_info',
    candidateRecruitmentInfo: 'candidate_recruitment_info',
    candidateContactDetails: 'candidate_contact_details',
    candidateLanguage: 'candidate_language',
    languages: 'languages',
    candidatePreferedJobLocation: 'candidate_prefered_job_location',
    nationality: 'nationality',
    maritalStatus: 'marital_status',
    candidateReference: 'candidate_reference',
    candidateEducation: 'candidate_education',
    candidateExperience: 'candidate_experience',
    candidateCertifications: 'candidate_certifications',
    roundSkills: 'round_skills',
    interviews: 'interviews',
    interviewRounds: 'interview_rounds',
    interviewCandidates: 'interview_candidates',
    interviewCandidatesRoundsScore: 'interview_candidates_rounds_score',
    interviewCandidatesSkillsScore: 'interview_candidates_skills_score',
    mppHiringForecast: 'mpp_hiring_forecast',
    mppPositionRequest: 'mpp_position_request',
    mppRecruitmentRequest: 'mpp_recruitment_request',
    positionLevel: 'position_level',
    mppWorkingConditions: 'mpp_working_conditions',
    mppDutiesResponsibilities: 'mpp_duties_responsibilities',
    mppExperienceRequirements: 'mpp_experience_requirements',
    mppEducationRequirements: 'mpp_education_requirements',
    mppEducationRequirementsDescriptions: 'mpp_education_requirements_descriptions',
    dataIntegrationFailureLogs: 'data_integration_failure_logs',
    businessUnit: 'business_unit',
    empPassport: 'emp_passport',
    empBankDetails: 'emp_bankdetails',
    employeeType: 'employee_type',
    empGrade: 'emp_grade',
    jobPostLocation: 'job_post_location',
    airTicketSettlementSummary: 'air_ticket_settlement_summary',
    empAirTicketPolicy: 'emp_air_ticket_policy',
    airTicketSettings: 'air_ticket_settings',
    empdependent: 'emp_dependent',
};

module.exports.appManagerTables = {
    orgRateChoice: 'org_rate_choice',
    activityTrackerUpdates: 'activity_tracker_release_updates',
    appActivitySummarizationManager: 'app_activity_summarization_manager',
    urlActivitySummarizationManager: 'url_activity_summarization_manager',
    appurlActivitySummarizationManager: 'appurl_activity_summarization_manager',
    employeeActivitySummarizationManager: 'employee_activity_summarization_manager',
    stealthActivityTrackerReleaseUpdates: 'stealth_activity_tracker_release_updates',
    insightsNotificationManager: 'insights_notification_manager',
    dataIntegrationSchedule: 'data_integration_schedule',
    orgSecretDetails: 'org_secret_details',
    hrappPlanDetails: 'hrapp_plan_details',
    domainPurgeManager: 'domain_purge_manager',
    appPurgeManager: 'app_purge_manager',
    weekOffDatesManager: 'week_off_dates_manager',
    fileTransferSummarizationManager: "file_transfer_summarization_manager",
    appTitleActivitySummarizationManager: "app_title_activity_summarization_manager"
}