//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { getConnection } = require("./commonFunctions");
//Require knex to make DB connection
const knex = require('knex');
//Require table alias
const { ehrTables } = require('../common/tableAlias');
const moment = require('moment-timezone');

// Main function to initiate air ticket summary
module.exports.initiateAirTicketSummary = async(event) => {
    let appmanagerDbConnection, organizationDbConnection;
    try {
        console.log('Inside initiateAirTicketSummary event value => ', event);
        // Get trigger type from event or use default "AirTicket"
        const triggerType = event?.input?.source || event?.source || "AirTicket";
        // Get database connection
        const databaseConnection = await commonLib.stepFunctions.getConnection(
            process.env.stageName,
            process.env.dbPrefix,
            process.env.dbSecretName,
            process.env.region,
            '',
            1
        );

        if(!databaseConnection || !Object.keys(databaseConnection).length) {
            console.log('Failed to establish database connection');
            return;
        }

        // Create AppManager database connection
        appmanagerDbConnection = knex(databaseConnection.AppManagerDb);
        // Get active organization instances
        const orgCodeActiveInsances = await getOrgActiveInsances(appmanagerDbConnection, triggerType);

        if(!orgCodeActiveInsances || orgCodeActiveInsances.length === 0) {
            console.log('No active organization instances found for trigger type:', triggerType);
            return;
        }

        // Process each organization instance
        for(let i = 0; i < orgCodeActiveInsances.length; i++) {
            try {
                const orgCode = orgCodeActiveInsances[i]['Org_Code'];
                const scheduleId = orgCodeActiveInsances[i]['Schedule_Id'];

                if(!orgCode) {
                    console.log('Missing organization code for instance:', orgCodeActiveInsances[i]);
                    continue;
                }

                // Update API schedule status to InProgress
                await updateAPISchedule(appmanagerDbConnection, {Status: 'InProgress'}, scheduleId);

                // Get organization database connection
                const connection = await getConnection(
                    process.env.stageName,
                    process.env.dbPrefix,
                    process.env.dbSecretName,
                    process.env.region,
                    orgCode
                );

                if(!connection || !Object.keys(connection).length || !connection.OrganizationDb) {
                    console.log(`Failed to establish connection for organization: ${orgCode}`);
                    await updateAPISchedule(appmanagerDbConnection,
                        {Status: 'Failed', Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')},
                        scheduleId
                    );
                    continue;
                }

                organizationDbConnection = knex(connection.OrganizationDb);

                // Get current month and year for payroll
                const payrollMonthYear = moment().utc().format('YYYY-MM');
                const currentDate = moment().utc().format('YYYY-MM-DD');

                // Query to get eligible employees for air ticket
                const employeeIds = await organizationDbConnection(ehrTables.empAirTicketPolicy + ' as EATP')
                    .pluck("EATP.Employee_Id")
                    .innerJoin(ehrTables.airTicketSettings + " as ATS", "ATS.Air_Ticket_Setting_Id", "EATP.Air_Ticket_Setting_Id")
                    .innerJoin(ehrTables.empJob + " as EJ", "EJ.Employee_Id", "EATP.Employee_Id")
                    .where('ATS.Status', 'Active').where('EATP.Status', 'Active').where('EJ.Emp_Status', 'Active')
                    .where(function() {
                        this.whereRaw("DATE_FORMAT(DATE_ADD(EATP.Last_Availed_Date, INTERVAL EATP.Eligibility_Of_Ticket_Claim_Months MONTH), '%Y-%m') = ?", [payrollMonthYear])
                            .orWhereRaw("DATE_FORMAT(DATE_ADD(EATP.Effective_Date, INTERVAL EATP.Eligibility_Of_Ticket_Claim_Months MONTH), '%Y-%m') = ?", [payrollMonthYear]);
                    })
                    .andWhere(function () {
                        this.whereNull('EATP.Last_Availed_Date')
                            .orWhere('EATP.Last_Availed_Date', '<=', currentDate);
                    })
                    .andWhere('EATP.Effective_Date', '<=', currentDate);

                // Trigger step function if eligible employees found
                if(employeeIds && employeeIds.length > 0) {
                    console.log(`Found ${employeeIds.length} eligible employees for air ticket in organization: ${orgCode}`);
                    const params = {
                        orgCode: orgCode,
                        employeeIds: employeeIds,
                        employeeIsInActiveAction: false
                    };
                    await commonLib.stepFunctions.triggerStepFunction(
                        process.env.processAirTicketSummary,
                        'processAirTicketSummary',
                        null,
                        params
                    );
                } else {
                    console.log(`No eligible employees found for air ticket in organization: ${orgCode}`);
                }

                // Close organization database connection
                if(organizationDbConnection) {
                    await organizationDbConnection.destroy();
                    organizationDbConnection = null;
                }

                // Update API schedule status to Success
                await updateAPISchedule(
                    appmanagerDbConnection,
                    {Status: 'Success', Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')},
                    scheduleId
                );

            } catch (error) {
                console.error(`Error processing organization ${orgCode}: `, error);

                // Close organization database connection if open
                if(organizationDbConnection) {
                    await organizationDbConnection.destroy();
                    organizationDbConnection = null;
                }

                // Update API schedule status to Failed
                await updateAPISchedule(
                    appmanagerDbConnection,
                    {Status: 'Failed', Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')},
                    scheduleId
                );
            }
        }
    } catch (error) {
        console.error("Error occurred while initiateAirTicketSummary main catch block => ", error);
    } finally {
        // Clean up database connections
        if(appmanagerDbConnection) {
            await appmanagerDbConnection.destroy();
        }
        if(organizationDbConnection) {
            await organizationDbConnection.destroy();
        }
    }
}


/**
 * Get active organization instances for the given trigger type
 * @param {Object} appmanagerDbConnection - AppManager database connection
 * @param {String} triggerType - Type of trigger (e.g., "AirTicket")
 * @returns {Promise<Array>} - Array of active organization instances
 */
async function getOrgActiveInsances(appmanagerDbConnection, triggerType) {
    try {
        if(!appmanagerDbConnection) {
            console.log('Invalid database connection provided to getOrgActiveInsances');
            return [];
        }

        // Update status to Open for matching records
        await appmanagerDbConnection('api_integration_schedule as AIS')
            .update({
                Status: 'Open',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
            })
            .where('Data_Push_Pull', 'Push')
            .andWhere('Integration_Type', 'Syntrum')
            .andWhere('Trigger_Type', triggerType);

        // Get distinct organization codes and schedule IDs
        const data = await appmanagerDbConnection('api_integration_schedule')
            .distinct('Org_Code', 'Schedule_Id')
            .where('Data_Push_Pull', 'Push')
            .andWhere('Integration_Type', 'Syntrum')
            .whereIn('Status', ['Open'])
            .andWhere('Trigger_Type', triggerType);

        return data || [];
    }
    catch(error) {
        console.error("Error in getOrgActiveInsances function: ", error);
        return [];
    }
}


/**
 * Update API integration schedule status
 * @param {Object} appmanagerDbConnection - AppManager database connection
 * @param {Object} updateparams - Parameters to update
 * @param {Number} schedulerId - Schedule ID to update
 * @returns {Promise<void>}
 */
async function updateAPISchedule(appmanagerDbConnection, updateparams, schedulerId) {
    try {
        if(!appmanagerDbConnection) {
            console.error('Invalid database connection provided to updateAPISchedule');
            return;
        }

        if(!schedulerId) {
            console.error('Invalid scheduler ID provided to updateAPISchedule');
            return;
        }

        await appmanagerDbConnection('api_integration_schedule')
            .update(updateparams)
            .where('Schedule_Id', schedulerId);
    } catch (error) {
        console.error("Error in updateAPISchedule function: ", error);
        throw error;
    }
}
