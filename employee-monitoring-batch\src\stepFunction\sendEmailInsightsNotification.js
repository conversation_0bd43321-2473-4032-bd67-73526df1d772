'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables } = require('../common/tableAlias');
// require file to access constant values
const {defaultValues,awsSesTemplates} = require('../common/appConstants');
// require common function
const commonFunction=require('./commonFunctions');
const {sendEmailNotifications}=require('../webApplication/employeeMonitoringCommonFunction');
const {getOrgLevelIdleTimeSettings }=require('../webApplication/settingsCommonFunction');
const reportCommonFunction = require('../common/reportCommonFunction');
// require xlsx package
const reader = require('xlsx');
const handlebars = require('handlebars');
// require fs package
const fs = require('fs');
// require package for sending the email
const nodemailer = require('nodemailer');
// require aws-sdk
const AWS = require('aws-sdk');
const path = require('path');
const { s3FileUpload } = require('../common/appConstants');

// variable declarations
let orgDbConnection='';
let appManagerDbConnection='';
let exportEMPUSRFileName = '/tmp/DailyEMPUSR.xlsx';
let exportURLFileName = '/tmp/DailyEMASR.xlsx';
let exportAppFileName = '/tmp/DailyEMUSR.xlsx';
let exportAppTitleFileName = '/tmp/DailyEMATSR.xlsx';

// function to get email notification based on input template data
module.exports.sendEmailInsightsNotification  = async(event, context) =>{
    let source='';
    try{          
        // variable declarations
        let redirectionURL;
        let mailListData;
        let employeeIdArray=[];
        let mailIdArray=[];
        let orgName='';
        let orgLogo='';
        let insightsQuery;
        source=(event.input)?(event.input.process):'';
        let templateData=(event.input && event.input.templateData)?(JSON.parse(event.input.templateData)):'';
        let orgCode=(event.input)?(event.input.orgCode):'';
        let employeeId=(event.input)?(event.input.employeeId):'';
        let inputActivityDate=templateData.activityDate;
        let employeeIdsBasedOnAccess=templateData.activeEmpIdArray;
        let exportFileName = 'DailyEMPUSR.xlsx';

        console.log("Inside sendEmailInsightsNotification ", "source: ", source, "orgCode: ",orgCode, "employeeId: ", employeeId ); 

        let getDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appManagerDbConnection=knex(getDbConnection.AppManagerDb);
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection,orgCode);
        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
            
            // form database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
            if(Object.keys(databaseConnection).length){
                orgDbConnection=knex(databaseConnection.OrganizationDb);
                if(templateData){
                    let subQuery=orgDbConnection
                    .select('Emp_Email','Employee_Id')
                    .from(ehrTables.insightsNotificationStatusLog);
                    // If source is admin then check open record exist or not
                    if(source==='admin'){
                        insightsQuery=subQuery
                        .where('Status','Open')
                    }
                    /** when source is either manager or employee */
                    else{
                        insightsQuery=subQuery
                        .where('Employee_Id',employeeId)
                    }
                    // get the email address and employeeId
                    return(
                        insightsQuery
                        .then(async(getData)=>{
                            if(getData.length>0)
                            {
                                // update the inprogress status in manager table
                                let updateParams={
                                    Status:'Inprogress'
                                }
                                let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,orgCode);
                                
                                // form the redirection url
                                redirectionURL = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress +'/in'+defaultValues.activityTrackerUrl;
                                // get organization name and logo
                                let orgDetails = await commonLib.func.getOrgDetails(orgCode, orgDbConnection);
                                if(Object.keys(orgDetails).length>0){
                                    orgName=orgDetails.orgName;
                                    orgLogo=orgDetails.logoPath;
                                }
                                // form admin we will send same email content to all the admins so we used bulk template here
                                if(source==='admin')
                                {
                                    try{
                                        // form the employeeId and mail address
                                        for(let key of getData){
                                            employeeIdArray.push(key.Employee_Id);
                                            mailIdArray.push(key.Emp_Email);
                                        }
                                        // update the inprogress status in status log table
                                        let updateParams={
                                            Status:'Inprogress',
                                            Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
                                        }
                                        let updateStatus=await commonFunction.updateNotificationStatusLog(orgDbConnection,updateParams,employeeIdArray);
                                        
                                        // If report data exist then form the report
                                        let getReport=await formReport(orgDbConnection,appManagerDbConnection,employeeIdsBasedOnAccess,inputActivityDate,inputActivityDate,orgCode);
                                        
                                        // form the input values for email process
                                        let inputParams={
                                            orgName:orgName,
                                            orgLogo:orgLogo,
                                            activityDate:(templateData.activityDate)?(moment(templateData.activityDate).format("ddd, MMM D, YYYY")):'',
                                            workedEmployeeCount:(templateData.workedEmployeeCount)?(templateData.workedEmployeeCount):0,
                                            notWorkedEmployeeCount:(templateData.notWorkedEmployeeCount)?(templateData.notWorkedEmployeeCount):0,
                                            totalTimeSpent:(templateData.activityData.length>0)?(templateData.activityData[0].totalTimeSpent):'-',
                                            activePercentage:(templateData.activityData.length>0)?(templateData.activityData[0].activePercentage)+"%":'-',
                                            notActivePercentage:(templateData.activityData.length>0)?(templateData.activityData[0].notActivePercentage)+"%":'-',
                                            idlePercentage:(templateData.activityData.length>0)?(templateData.activityData[0].idlePercentage)+"%":'-',
                                            efficiencyHtmlContent:await formHTMLDesign(templateData.highActivity,redirectionURL,'high'),
                                            lowActivityHtmlContent:await formHTMLDesign(templateData.lowActivity,redirectionURL,'low'),
                                            topAppsHtmlContent:await formHTMLDesign(templateData.appData,redirectionURL,'app'),
                                            topUrlsHtmlContent:await formHTMLDesign(templateData.urlData,redirectionURL,'url'),
                                            emailFrom:process.env.emailFrom,
                                            emailSubject:orgName,
                                            emailIdList:mailIdArray
                                        }
                                        do
                                        {
                                            // function to send the bulk emails
                                            mailListData=await sendEmailWithAttachment(inputParams,source, orgCode);
                                        }
                                        // iterate the list till all the records are processed
                                        while(Object.keys(mailListData).length>0)
                                        {
                                            // update the success status in manager table
                                            let updateParams={
                                                Status:'Success',
                                            }

                                            await deleteAllTempFolderFiles();

                                            let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,orgCode);
                                            
                                            updateParams.Updated_On=moment().format('YYYY-MM-DD HH:mm:ss');
                                            let updateStatusLog=await commonFunction.updateNotificationStatusLog(orgDbConnection,updateParams,employeeIdArray);
                                            
                                            // destory dbconnection
                                            orgDbConnection?orgDbConnection.destroy():null;
                                            appManagerDbConnection?appManagerDbConnection.destroy():null;
                                            let response = 
                                            {
                                                nextStep:'Step2',
                                                input:{'process':source},
                                                message:'Email send successfully so process the next record.'
                                            };
                                            return response;                    
                                        }
                                    }
                                    catch(error){
                                        console.log('Error in generating daily summary report',error);
                                        let errorMessages=['Error in reading the report','Error in reading the template','Error in sending the email'];
                                        let failureReason=errorMessages.includes(error)?error:'Error in sending the insights email';

                                        // delete all temp folder files
                                        await deleteAllTempFolderFiles();

                                        // update the failure status in status log table
                                        let failureParams={
                                            status:'Failure',
                                            Reason:failureReason,
                                            Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                        }
                                        let updateStatusLog=await commonFunction.updateNotificationStatusLog(orgDbConnection,failureParams,employeeIdArray);
                                        
                                        // update the failure status in manager table
                                        let updateFailureParams={
                                            Status:'Failure',
                                        }
                                        let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateFailureParams,orgCode);
                                        
                                        // destory dbconnection
                                        orgDbConnection?orgDbConnection.destroy():null;
                                        appManagerDbConnection?appManagerDbConnection.destroy():null;
                                        let response = 
                                        {
                                            nextStep:'Step2',
                                            input:{'process':source},
                                            message:'Error in step3 function .catch block.'
                                        };
                                        return response;
                                    }
                                }
                                else{
                                    // update the inprogress status in status log table
                                    let updateParams={
                                        Status:'Inprogress',
                                        Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
                                    }
                                    let updateStatus=await commonFunction.updateNotificationStatusLog(orgDbConnection,updateParams,[employeeId]);
                                    
                                    /** When source is manager then based on which form notification params */
                                    if(source==='manager'){
                                        // If report data exist then form the report
                                        let getReport=await formReport(orgDbConnection,appManagerDbConnection,employeeIdsBasedOnAccess,inputActivityDate,inputActivityDate,orgCode);
                                        
                                        let inputParams={
                                            orgName:orgName,
                                            orgLogo:orgLogo,
                                            date:(templateData.activityDate)?(moment(templateData.activityDate).format("ddd, MMM D, YYYY")):'',
                                            totalTimeSpent:(templateData.activityData.length>0)?(templateData.activityData[0].totalTimeSpent):'-',
                                            workedEmployeeCount:(templateData.workedEmployeeCount)?(templateData.workedEmployeeCount):0,
                                            notWorkedEmployeeCount:(templateData.notWorkedEmployeeCount)?(templateData.notWorkedEmployeeCount):0,
                                            activePercentage:(templateData.activityData.length>0)?(templateData.activityData[0].activePercentage)+"%":'-',
                                            notActivePercentage:(templateData.activityData.length>0)?(templateData.activityData[0].notActivePercentage)+"%":'-',
                                            idlePercentage:(templateData.activityData.length>0)?(templateData.activityData[0].idlePercentage)+"%":'-',
                                            efficiencyHtmlContent:await formHTMLDesign(templateData.highActivity,redirectionURL,'high'),
                                            lowActivityHtmlContent:await formHTMLDesign(templateData.lowActivity,redirectionURL,'low'),
                                            topAppsHtmlContent:await formHTMLDesign(templateData.appData,redirectionURL,'app'),
                                            topUrlsHtmlContent:await formHTMLDesign(templateData.urlData,redirectionURL,'url'),
                                            emailSubject:orgName,
                                            emailIdList:[getData[0].Emp_Email]
                                        }
                                        try{
                                            let sendEmail=await sendEmailWithAttachment(inputParams,source, orgCode);
                                            
                                            // delete all temp folder files
                                            await deleteAllTempFolderFiles();

                                            // update the success status in manager table
                                            let updateParams={
                                                Status:'Success',
                                            }
                                            
                                            let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,orgCode);
                                            
                                            updateParams.Updated_On=moment().format('YYYY-MM-DD HH:mm:ss');
                                            let updateStatusLog=await commonFunction.updateNotificationStatusLog(orgDbConnection,updateParams,employeeIdArray);
                                            
                                            // destory dbconnection
                                            orgDbConnection?orgDbConnection.destroy():null;
                                            appManagerDbConnection?appManagerDbConnection.destroy():null;
                                            let response = 
                                            {
                                                nextStep:'Step4',
                                                input:{'process':source,'orgCode':orgCode,'activityDate':inputActivityDate},
                                                message:'Email send successfully so process the next record.'
                                            };
                                            return response;
                                        }
                                        catch(error){
                                            console.log('Error in sending the email for managers',error);
                                            let errorMessages=['Error in sending the email'];
                                            let failureReason=errorMessages.includes(error)?error:'Error in sending the insights email';
                                    
                                            // delete all temp folder files
                                            await deleteAllTempFolderFiles();

                                            // update the failure status in status log table
                                            let failureParams={
                                                status:'Failure',
                                                Reason:failureReason,
                                                Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                            }
                                            let updateStatusLog=await commonFunction.updateNotificationStatusLog(orgDbConnection,failureParams,employeeIdArray);
                                            
                                            // update the failure status in manager table
                                            let updateFailureParams={
                                                Status:'Failure',
                                            }
                                            let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateFailureParams,orgCode);
                                            
                                            // destory dbconnection
                                            orgDbConnection?orgDbConnection.destroy():null;
                                            appManagerDbConnection?appManagerDbConnection.destroy():null;
                                            let response = 
                                            {
                                                nextStep:'Step2',
                                                input:{'process':source},
                                                message:'Error in step3 function .catch block.'
                                            };
                                            return response;    
                                        }
                                    }
                                    else{

                                        let considerNotActiveIdleTime=await getOrgLevelIdleTimeSettings(orgDbConnection);
                                        // In case of error we will consider it as Never by default
                                        (considerNotActiveIdleTime)?(considerNotActiveIdleTime):'Never';
                                        
                                        let systemProductivityPercentage = '-';
                                        let userProductivity = '-';
                                        /** if settings is never then consider percentage without idle time else consider the percentage with idle time */
                                        if(considerNotActiveIdleTime==='Never'){
                                            systemProductivityPercentage = (templateData.activityData.length>0)?(templateData.activityData[0].systemProductivityPercentageWithoutIdleTime):'-';
                                            userProductivity = (templateData.activityData.length>0)?(templateData.activityData[0].User_Productivity_Percentage):'-';
                                        }
                                        else if(considerNotActiveIdleTime==='Always'){
                                            systemProductivityPercentage = (templateData.activityData.length>0)?(templateData.activityData[0].systemProductivityPercentageWithIdleTime):'-';
                                            userProductivity = (templateData.activityData.length>0)?(templateData.activityData[0].User_Productivity_Percentage):'-';
                                        }
                                        else{
                                            systemProductivityPercentage = (templateData.activityData.length>0)?(templateData.activityData[0].systemProductivityPercentageBasedOnFDWH):'-';
                                            userProductivity = (templateData.activityData.length>0)?(templateData.activityData[0].User_Productivity_Percentage_FDWH):'-';
                                        }

                                        //Needs to be updated
                                        let notificationParams = {
                                            'Source': process.env.emailFrom,
                                            'Template': awsSesTemplates.employeeInsightsNotification,
                                            'Destination': {
                                                'ToAddresses': [getData[0].Emp_Email]
                                            },
                                            'TemplateData': JSON.stringify({
                                                orgName:orgName,
                                                orgLogo:orgLogo,
                                                date:(templateData.activityDate)?(moment(templateData.activityDate).format("ddd, MMM D, YYYY")):'',
                                                sytemUpTime: (templateData.activityData.length>0)?(templateData.activityData[0].Total_Activity_Duration):'-',
                                                userActiveTime: (templateData.activityData.length>0)?(templateData.activityData[0].Active_Duration):'-',
                                                idleTime: (templateData.activityData.length>0)?(templateData.activityData[0].timeSpentOnIdleAndNotActive):'-',
                                                computerActivityTime: (templateData.activityData.length>0)?(templateData.activityData[0].Productive_Activity_Duration):'-',
                                                systemProductivityPercentage: systemProductivityPercentage,
                                                checkIn: (templateData.activityData.length>0)?(templateData.activityData[0].Activity_Start_Hour):'',
                                                checkOut: (templateData.activityData.length>0)?(templateData.activityData[0].Activity_End_Hour):'',
                                                userProductivity: userProductivity,
                                                productiveDuration: (templateData.activityData.length>0)?(templateData.activityData[0].Productive_Duration_In_HHMM):'-',
                                                unProductiveDuration: (templateData.activityData.length>0)?(templateData.activityData[0].Unproductive_Duration_In_HHMM):'-',
                                                neutralDuration: (templateData.activityData.length>0)?(templateData.activityData[0].Neutral_Duration_In_HHMM):'-',
                                                efficiencyHtmlContent:await highEfficiencyHTMLDesign(templateData.highActivity),
                                                topAppsHtmlContent:await formHTMLDesign(templateData.appData,redirectionURL,'app'),
                                                topUrlsHtmlContent:await formHTMLDesign(templateData.urlData,redirectionURL,'url'),
                                            })
                                        };

                                        //call the sendEmailNotifications function to send the email
                                        let emailResponse = await sendEmailNotifications(notificationParams);

                                        let params={};
                                        if(emailResponse===true)
                                        {
                                            params={
                                                status:'Success',
                                                Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                            }
                                        }
                                        else{
                                            params={
                                                status:'Failure',
                                                Reason:'Error in sending the email.',
                                                Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                            }
                                        }
                                        // function to update the status in status log table
                                        let updateStatusLog=await commonFunction.updateNotificationStatusLog(orgDbConnection,params,[employeeId]);
                                        orgDbConnection?orgDbConnection.destroy():null;
                                        appManagerDbConnection?appManagerDbConnection.destroy():null;    
                                        let response = 
                                        {
                                            nextStep:'Step4',
                                            input:{'process':source,'orgCode':orgCode,'activityDate':inputActivityDate},
                                            message:'Email send successfully so process the next record.'
                                        };
                                        return response;
                                    }
                                }
                            }
                            else{
                                console.log('Email triggered for all the employees. No open status record found in status log table');
                                orgDbConnection?orgDbConnection.destroy():null;
                                appManagerDbConnection?appManagerDbConnection.destroy():null;
                                let response = 
                                {
                                    nextStep:'Step2',
                                    input:{'process':source},
                                    message:'No open records.Process the next record.'
                                };
                                return response;
                            }
                        })
                        .catch(catchError=>{
                            console.log('Error in sendEmailInsightsNotification function .catch block.', catchError);
                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                            orgDbConnection?orgDbConnection.destroy():null;
                            let response = 
                            {
                                nextStep:'Step2',
                                input:{'process':source},
                                message:'Error in step3 function .catch block.'
                            };
                            return response;
                        })
                    );
                }
                else{
                    console.log('Template data does not exist for '+orgCode+' instance so update as failure');
                    let updateParams={
                        Status:'Failure'
                    }
                    // update the failure status in manager table
                    let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,orgCode);
                    // destory dbconnection
                    orgDbConnection?orgDbConnection.destroy():null;
                    appManagerDbConnection?appManagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'Step2',
                        input:{'process':source},
                        message:'Template data not exist.So process the next record.'
                    };
                    return response;
                }    
            }
            else{
                console.log('Error while making database connection');
                let response = 
                {
                    nextStep:'Step2',
                    input:{'process':source},
                    message:'Error while making database connection in step3.'
                };
                return response;    
            }
        }
        else{
            console.log('Error while getting the data region');
            let response = 
            {
                nextStep:'Step2',
                input:{'process':source},
                message:'Error while getting the data region in step3.'
            };
            return response;    
        }
    }
    catch (mainCatchError){
        console.log('Error in sendEmailInsightsNotification function main catch block.', mainCatchError);
        orgDbConnection?orgDbConnection.destroy():null;
        appManagerDbConnection?appManagerDbConnection.destroy():null;
        let response = 
        {
            nextStep:'Error',
            input:{'process':source},
            message:'Error while sending email notification catch block.'
        };
        return response;
    }
};

// function to form the html design for low/high activity and app and url data.
async function formHTMLDesign(insightsData,redirectionURL,activityInput,isAdminTemplate){
    try{
        /** template design will be same for the high and low activity card. And for app and url separate design */
        if(activityInput==='high' || activityInput==='low'){
            let activityRowHtmlDesign = ""; // row wise html block
            let activityHtmlDesign = "";
            // for admin present 5 activity data and 10 for managers
            let noOfRecordToBePresented = (isAdminTemplate) ? (defaultValues.numberOfActivityDataForAdmin):(defaultValues.numberOfActivityDataForManager);
            let empListCount = (insightsData.length <= noOfRecordToBePresented) ? (insightsData.length) : (noOfRecordToBePresented);
            // check insights data exist or not
            if(insightsData && insightsData.length > 0) {
                // form html design for each row
                for (var i = 0; i < empListCount; i++) {
                    activityRowHtmlDesign += `
                    <tr style="border-top:1px solid #e2e7ee">
                        <td style="color: #EC407E;font-weight: bold;">
                        <div style="width: 120px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            <img style="vertical-align: middle;" width="25" src="https://hrapp-email-images.s3.amazonaws.com/Avatar.png" alt="" />
                            <span style="padding-left:5px;"> ${insightsData[i].employeeName}</span>
                        </div>
                        </td>
                        <td style="color: #23AA17;font-weight: bold;">${insightsData[i].checkIn}</td>
                        <td style="color: #FE3E49;font-weight: bold;">${insightsData[i].checkOut}</td>
                        <td style="color: #2468EB;font-weight: bold;">${insightsData[i].totalActivityDuration}</td>
                        <td align="center" style="color: #260134;font-weight: bold;">${insightsData[i].activityPercentage ? insightsData[i].activityPercentage + '%' : '-'}</td>
                    </tr>
                    `;
                }
                // form final html for table design
                activityHtmlDesign = `
                    <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
                    <tbody>
                        <tr>
                            <td></td>
                        </tr>
                        <tr align="left" style="border-top:1px solid #e2e7ee">
                            <th style="color: #595C68;">Employee(s)</th>
                            <th style="color: #595C68;">Check In</th>
                            <th style="color: #595C68;">Check Out</th>
                            <th style="color: #595C68;">Time Spent</th>
                            <th style="color: #595C68;" align="center">System Productivity Percentage</th>
                        </tr>
                        ${activityRowHtmlDesign}
                    </tbody>
                    </table>
                    <table border="0" cellpadding="0" cellspacing="0" width="95%" style="margin: 5px;border-top:1px solid #e2e7ee">
                    <tbody>
                        <tr align="center">
                            <td align="center">
                            <a href=${redirectionURL} style="color: #1385FF; font-weight: bold; font-size: 12px;">View all ></a>
                            </td>
                        </tr>
                    </tbody>
                    </table>
                `;
            } else {
                // if activity data does not exist then, present empty scenario
                activityHtmlDesign = `
                    <table border="0" width="95%" style="margin: 5px;padding: 15px;border-top:1px solid #e2e7ee;color: #7F6C8A;">
                    <tbody>
                        <tr align="center">
                            <td align="center" style="margin: 20px;">${(activityInput==='high')?("No activity data for efficiency pro"):("No low activity data")}</td>
                        </tr>
                    </tbody>
                    </table>
                `;
            }
            return activityHtmlDesign;
        }
        // if the activityInput is either app or url this block will be executed
        else{
            let appUrlRowHtml = "";
            let appUrlHtmlDesign = "";
            if(insightsData && insightsData.length > 0) {
                let noOfRecord = (insightsData.length <= 5) ? (insightsData.length) : (5);
                for (var i = 0; i < noOfRecord; i++) {
                    appUrlRowHtml += `
                        <tr style="border-top:1px solid #e2e7ee">
                        <td width="60%" style="color: #260029;font-weight: bold;">
                            <div style="width: 250px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                                <span style="vertical-align: middle; width: 11px; height: 11px; background: ${defaultValues.appUrlTemplateColorCodes[i]}; border-radius: 50%;display: inline-block;"></span>
                                <span style="padding-left:5px;">${(activityInput==='app')?(insightsData[i].Application_Name):(insightsData[i].Domain_Name)}</span>
                            </div>
                        </td>
                        <td align="right" style="color: #260029;font-weight: bold;">${insightsData[i].totalActivityDuration}</td>
                        </tr>
                    `;
                }
                appUrlHtmlDesign = `
                <table cellpadding="10" cellspacing="0" style="border-collapse:collapse;color:#260029;font-size:12px" width="95%">
                    <tbody>
                    <tr><td></td></tr>
                    <tr align="left" style="border-top:1px solid #e2e7ee">
                        <th style="color: #595C68;">${(activityInput==='app')?('App Name(s)'):('Domain')}</th>
                        <th align="right" style="color: #595C68;" width="100%">Time Spent</th>
                    </tr>
                    ${appUrlRowHtml}
                    </tbody>
                </table>
                <table border="0" cellpadding="0" cellspacing="0" width="95%" style="margin: 5px;border-top:1px solid #e2e7ee">
                    <tbody>
                        <tr align="center">
                            <td align="center">
                            <a href=${redirectionURL} style="color: #1385FF; font-weight: bold; font-size: 12px;">${(activityInput==='app')?("View all Apps"):("View all URLs")}></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                `;
            } else {
                appUrlHtmlDesign = `
                <table border="0" width="95%" style="margin: 5px;padding: 15px;border-top:1px solid #e2e7ee;color: #7F6C8A;">
                    <tbody>
                    <tr align="center">
                        <td align="center" style="margin: 20px;">${(activityInput==='app')?"No application usage":"No internet usage"}</td>
                    </tr>
                    </tbody>
                </table>
                `;
            }
            return appUrlHtmlDesign;
        }
    }
    catch(error){
        console.log('Error in formHTMLDesign function main catch block.', error);
        return '';
    }
}

/** function to form high efficiency employee design */
async function highEfficiencyHTMLDesign(empList){
    try{
        let empListHtml = "";
        let empListCount = empList.length <= 5 ? (empList.length) : (5);
        if(empListCount>0){
            for (var i = 0; i < empListCount; i++) {
                empListHtml += `
                <th width="20%" align="center" style="color: #595C68;">
                <div>
                 <img style="width:25px;height:25px;border-radius:12px" src="https://hrapp-email-images.s3.amazonaws.com/Avatar.png" class="CToWUd">
                </div>
                ${empList[i].employeeName}
              </th>
              `;
            }
            return  `
                <table border="0" width="95%" style="margin: 5px;padding: 15px;border-top:1px solid #E2E7EE;color: #7F6C8A;">
                <tbody>
                    <tr align="center">
                    ${empListHtml}
                    </tr>
                </tbody>
                </table>
            `
        }
        else {
            // if activity data does not exist then, present empty scenario
            return `
            <table border="0" width="95%" style="margin: 5px;padding: 15px;border-top:1px solid #e2e7ee;color: #7F6C8A;">
            <tbody>
                <tr align="center">
                <td align="center" style="margin: 20px;">No activity data for efficiency pro</td>
                </tr>
            </tbody>
            </table>
            `;
        }
    }
    catch(error){
        console.log('Error in highEfficiencyHTMLDesign function main catch block.', error);
        return '';
    }
};

// function to send the email with report attachment
async function sendEmailWithAttachment(inputParams,source, orgCode){
    try{
        console.log('Inside sendEmailWithAttachment function');
        let ses = new AWS.SES({region: process.env.sesTemplatesRegion });

        // get the html content with inputs
        let emailTemplateData=await getTemplateData(inputParams, orgCode);
        let mailList=inputParams.emailIdList; // send the emailId list for which mail need to be send
        // console.log('mailList',mailList);
        // if template data exist then send the email with attachments else send without template
        if(Object.keys(emailTemplateData).length>0){
            let numberOfMailToBeTriggered;
            
            // check whether the email address list exist or not
            if(Object.keys(mailList).length>0)
            {
                let destinationArray=[];
    
                /** define the number of mail to be triggered at a particular time. Since 50 mail can be triggered for each call. So we will limit the count */
                numberOfMailToBeTriggered=((Object.keys(mailList).length)>(50))?(50):(Object.keys(mailList).length);
                
                // form destination array
                for(let i=0;i<numberOfMailToBeTriggered;i++)
                {
                    if(mailList[i]){
                        destinationArray.push(mailList[i]);
                    }
                }
    
                // form the params
                let mailOptions = {
                    from: process.env.emailFrom,
                    html: emailTemplateData.htmlToSend,
                    bcc: (destinationArray.length>0)?destinationArray.toString():'',
                    attachments: emailTemplateData.attachmentsDetails
                };

                if(inputParams.emailSubject){
                    mailOptions.subject='Daily Team Work Summary for '+inputParams.emailSubject
                }
                else{
                    mailOptions.subject='Daily Team Work Summary'
                }
                
                // create Nodemailer SES transporter
                let transporter = nodemailer.createTransport({
                    SES: ses
                });

                let sendEmail=await sendEmailWithReportAttachment(transporter,mailOptions);
                console.log('Response from sendEmailWithReportAttachment',sendEmail);

                // If the source is admin then return the pending emailIds
                if(source==='admin'){
                    mailList=(Object.keys(mailList).length>0)?mailList.splice(0,numberOfMailToBeTriggered):{};
                    return (Object.keys(mailList).length>0)?mailList:{};
                }
                else{
                    return 'success';
                }
            }
            else{
                // If the source is admin then return the pending emailIds
                if(source==='admin'){
                    mailList=(Object.keys(mailList).length>0)?mailList.splice(0,numberOfMailToBeTriggered):{};
                    return (Object.keys(mailList).length>0)?mailList:{};
                }
                else{
                    return 'success';
                }             
            }
        }
        // send email without attaching the summary report
        else{
            // if source is admin then we can send the email template since data is same for all the users
            if(source==='admin'){
                let mailListData=await commonLib.func.sendBulkMail(process.env.sesTemplatesRegion,inputParams,awsSesTemplates.adminInsightsNotification);
                console.log('Response after calling sendBulkMail function when source is admin',mailListData);
                mailList=(Object.keys(mailList).length>0)?mailList.splice(0,numberOfMailToBeTriggered):{};
                return (Object.keys(mailList).length>0)?mailList:{};
            }
            else{
                // form notification params
                let notificationParams = {
                    'Source': process.env.emailFrom,
                    'Template': awsSesTemplates.adminInsightsNotification,
                    'Destination': {
                        'ToAddresses': inputParams.emailIdList
                    },
                    'TemplateData': JSON.stringify({
                        orgName:inputParams.orgName,
                        orgLogo:inputParams.orgLogo,
                        date:(inputParams.date)?(inputParams.date):'-',
                        totalTimeSpent:(inputParams.totalTimeSpent)?(inputParams.totalTimeSpent):'-',
                        workedEmployeeCount:(inputParams.workedEmployeeCount)?(inputParams.workedEmployeeCount):0,
                        notWorkedEmployeeCount:(inputParams.notWorkedEmployeeCount)?(inputParams.notWorkedEmployeeCount):0,
                        activePercentage:(inputParams.activePercentage)?(inputParams.activePercentage):'-',
                        notActivePercentage:(inputParams.notActivePercentage)?(inputParams.notActivePercentage):'-',
                        idlePercentage:(inputParams.idlePercentage)?(inputParams.idlePercentage)+"%":'-',
                        efficiencyHtmlContent:inputParams.efficiencyHtmlContent,
                        lowActivityHtmlContent:inputParams.lowActivityHtmlContent,
                        topAppsHtmlContent:inputParams.topAppsHtmlContent,
                        topUrlsHtmlContent:inputParams.topUrlsHtmlContent,
                        emailSubject:inputParams.orgName
                    })
                };
                let emailResponse = await sendEmailNotifications(notificationParams);
                console.log('Response after calling sendEmailNotifications function',emailResponse);
                return emailResponse;
            }
        }
    }
    catch(error){
        console.log('Error in sendEmailWithAttachment function main catch block.', error);
        throw 'Error in sending the email';
    }
}

// function to form the summary report in tmp folder
async function formReport(orgDbConnection,appManagerDbConnection,employeeIdsBasedOnAccess,startDate,endDate,orgCode){
    try{
        // get the daily summary productive and unproductive report data
        let reportDetailsArgs = {
            employeeIdsArray:employeeIdsBasedOnAccess,
            startDate:startDate,
            endDate:endDate,
            source:'insights'
        };

        let workBook = reader.utils.book_new();                        

        // form the summary report data
        let reportData=await reportCommonFunction.getproductiveUnproductiveSummaryReport(orgDbConnection,appManagerDbConnection,reportDetailsArgs,orgCode);
        // If report data exist then form the report
        if(reportData && reportData.length>0)
        {
            let workSheet = reader.utils.json_to_sheet(reportData, {
                skipHeader: 1,
            });
            let workBook = reader.utils.book_new();
            reader.utils.book_append_sheet(workBook, workSheet, `EMPUSR`);
            reader.writeFile(workBook, exportEMPUSRFileName);
            // return 'success';
        }
        else{
            console.log('productiveUnproductiveSummaryReport data does not exist for the date - ',startDate, endDate);
            // return 'success';
        }

        let appReportArgs = {
            employeeIdsArray:employeeIdsBasedOnAccess,
            startDate:startDate,
            endDate:endDate,
            source:'insights',
            orgCode, orgCode,
            groupByTitle : 0,
            reportType: "EMASR"
        };
        // form the summary report data
        let appsReportData=await reportCommonFunction.getAppsReport(orgDbConnection,appManagerDbConnection,appReportArgs);
        
        // If report data exist then form the report
        if(appsReportData && appsReportData.length>0)
        {
            let appsWorkSheet = reader.utils.json_to_sheet(appsReportData, {
                skipHeader: 1,
            });
            let workBook = reader.utils.book_new();
            reader.utils.book_append_sheet(workBook, appsWorkSheet, `EMASR`);
            reader.writeFile(workBook, exportAppFileName);
        }
        else{
            console.log('appsSummaryReport data does not exist for the date - ',startDate, endDate);
        }

        let appTitleReportArgs = {
            employeeIdsArray:employeeIdsBasedOnAccess,
            startDate:startDate,
            endDate:endDate,
            source:'insights',
            orgCode, orgCode,
            groupByTitle : 1,
            reportType: "EMATSR"
        };

        // form the summary report data
        let appsTitleReportData=await reportCommonFunction.getAppsReport(orgDbConnection,appManagerDbConnection,appTitleReportArgs);
        
        // If report data exist then form the report
        if(appsTitleReportData && appsTitleReportData.length>0)
        {
            let appsWorkSheet = reader.utils.json_to_sheet(appsTitleReportData, {
                skipHeader: 1,
            });
            let workBook = reader.utils.book_new();
            reader.utils.book_append_sheet(workBook, appsWorkSheet, `EMATSR`);
            reader.writeFile(workBook, exportAppTitleFileName);
        }
        else{
            console.log('appsTitleSummaryReport data does not exist for the date - ',startDate, endDate);
        }

        let urlReportArgs = {
            employeeIdsArray:employeeIdsBasedOnAccess,
            startDate:startDate,
            endDate:endDate,
            source:'insights',
            orgCode, orgCode
        };

        // form the summary report data
        let urlsReportData=await reportCommonFunction.getUrlsReport(orgDbConnection,appManagerDbConnection,urlReportArgs);
        
        // If report data exist then form the report
        if(urlsReportData && urlsReportData.length>0)
        {
            let urlsWorkSheet = reader.utils.json_to_sheet(urlsReportData, {
                skipHeader: 1,
            });
            let workBook = reader.utils.book_new();
            reader.utils.book_append_sheet(workBook, urlsWorkSheet, `EMUSR`);
            reader.writeFile(workBook, exportURLFileName);
        }
        else{
            console.log('urlSummaryReport data does not exist for the date - ',startDate, endDate);
        }
        return 'success';
    }
    catch(error){
        console.log('Error in formReport function main catch block.', error);
        return 'error'; 
    } 
}

// function to form template data
async function getTemplateData(inputParams, orgCode){
    return new Promise(async function (resolve, reject) {
        try{
            // require plugin to decode html entities
            let { decode }=require('html-entities');

               // Create client for secrets manager
            let client = new AWS.SecretsManager({ region: process.env.region });
            // Get secrets from aws secrets manager
            let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
            secretKeys = secretKeys.SecretString ? JSON.parse(secretKeys.SecretString) : {};

            let attachmentsDetails = [];
            const s3FileUploadPath = `${orgCode}/EmployeeMonitoringInsights/${new Date().getTime()}/`;
            if(fs.existsSync(exportEMPUSRFileName)){
                // get the report data
              //  const getEMPUSRReportContent = await fs.readFileSync(exportEMPUSRFileName);
                const s3FileURL = await uploadFileInS3(secretKeys, exportEMPUSRFileName, s3FileUploadPath+'Daily Productivity Unproductivity Summary Report.xlsx', 'Daily Productivity Unproductivity Summary Report.xlsx')
                if(s3FileURL){
                    attachmentsDetails.push({
                        filename: 'Daily Productivity Unproductivity Summary Report.xlsx',
                        path: s3FileURL // Direct S3 URL as attachment
                    });
                }
                // attachmentsDetails.push({
                //     filename:'Daily Productivity Unproductivity Summary Report.xlsx',
                //     content: getEMPUSRReportContent
                // });
            }
            if(fs.existsSync(exportAppFileName)){
                // const getEMASRReportContent = await fs.readFileSync(exportAppFileName);
                // attachmentsDetails.push({
                //     filename:'Daily Application Summary Report.xlsx',
                //     content: getEMASRReportContent
                // });
                const s3FileURL = await uploadFileInS3(secretKeys, exportAppFileName, s3FileUploadPath+'Daily Application Summary Report.xlsx', 'Daily Application Summary Report.xlsx')
                if(s3FileURL){
                    attachmentsDetails.push({
                        filename: 'Daily Application Summary Report.xlsx',
                        path: s3FileURL // Direct S3 URL as attachment
                    });
                }
            }
            if(fs.existsSync(exportAppTitleFileName)){
                // const getEMATSRReportContent = await fs.readFileSync(exportAppTitleFileName);
                // attachmentsDetails.push({
                //     filename:'Daily Application Title Summary Report.xlsx',
                //     content: getEMATSRReportContent
                // });
                const s3FileURL = await uploadFileInS3(secretKeys, exportAppTitleFileName, s3FileUploadPath+'Daily Application Title Summary Report.xlsx', 'Daily Application Title Summary Report.xlsx')
                if(s3FileURL){
                    attachmentsDetails.push({
                        filename: 'Daily Application Title Summary Report.xlsx',
                        path: s3FileURL // Direct S3 URL as attachment
                    });
                }
            }
            if(fs.existsSync(exportURLFileName)){
                // const getEMUSRReportContent = await fs.readFileSync(exportURLFileName);
                // attachmentsDetails.push({
                //     filename:'Daily Url Summary Report.xlsx',
                //     content: getEMUSRReportContent
                // });
                const s3FileURL = await uploadFileInS3(secretKeys, exportURLFileName, s3FileUploadPath+'Daily Url Summary Report.xlsx', 'Daily Url Summary Report.xlsx')
                if(s3FileURL){
                    attachmentsDetails.push({
                        filename: 'Daily Url Summary Report.xlsx',
                        path: s3FileURL // Direct S3 URL as attachment
                    });
                }
            }

            let attachmentContent = '';
            if(attachmentsDetails.length){
                attachmentContent = `<div style="margin-top: 10px;margin-bottom:20px"></div>
                    <table border="0" cellpadding="0" cellspacing="0" height="0" width="95%"><tbody>
                    <div style=" text-align: left;"><p style="margin: 0 0 10px; font-weight: bold;">Daily Summary Reports:</p>
                    <p>You can download them by clicking the links below:</p>`;
                                    
                attachmentsDetails.forEach((attachFile)=> {
                    attachmentContent = attachmentContent + `<a href="${attachFile.path}" style="background-color: #f6faff; color:rgb(10, 10, 10); 
                    padding: 8px 12px; text-decoration: none; border-radius: 5px; display: inline-block; margin-bottom: 5px;">
                    📥 ${attachFile.filename}</a><br>`;
                })
                attachmentContent = attachmentContent +`</div></tbody></table>`
            }
            inputParams.attachmentList = attachmentContent
            attachmentsDetails = []
            
            // get the template data
            let templateFilePath=path.join(__dirname, '/adminInsightsNotification.html');
            
            const templatedata = await fs.readFileSync(templateFilePath, 'utf8');
            // convert the template data to string
            let template = handlebars.compile(templatedata.toString('utf8'));
            // replace the values in template with these params
            let htmlToSend = template(inputParams);
            // decode the template data
            htmlToSend=decode(htmlToSend);
            resolve(
            {   htmlToSend,
                attachmentsDetails
            });
        }
        catch(error){
            console.log('Error in getTemplateData function main catch block.', error);
            resolve({}); 
        }
    })
}

async function sendEmailWithReportAttachment(transporter,mailOptions){
    try{
        return new Promise(async function (resolve, reject) {
            await transporter.sendMail(mailOptions, function (err, info) {
                if (err) {
                    console.log('Error sending email',err);
                    reject('Error in sending the email');
                } else {
                    resolve ('success');
                }
            })
        })
    }
    catch(error){
        console.log('Error in sendEmailWithReportAttachment function catch block.', error);
        throw 'Error in sending the email';
    }
}

async function deleteAllTempFolderFiles(){
    try{
        // delete the file which is generated in temp folder
        if(fs.existsSync(exportEMPUSRFileName)){
            await commonFunction.deleteTempFolderFiles(exportEMPUSRFileName);
        }
        if(fs.existsSync(exportAppFileName)){ 
            await commonFunction.deleteTempFolderFiles(exportAppFileName);
        } 
        if (fs.existsSync(exportURLFileName)){
            await commonFunction.deleteTempFolderFiles(exportURLFileName);
        } if(fs.existsSync(exportAppTitleFileName)){ 
            await commonFunction.deleteTempFolderFiles(exportAppTitleFileName);
        }
    }
    catch(error){
        console.log('Error in deleteAllTempFolderFiles function catch block', error);
    }    
}


async function uploadFileInS3(secretKeys, exportFileName,filePath,fileName){
    try{

        let awsConfig = {
            region: process.env.region,
            ...(secretKeys && secretKeys.accesskey && {accessKeyId: secretKeys.accesskey}),
            ...(secretKeys && secretKeys.secretkey && {secretAccessKey: secretKeys.secretkey}),
        };

        console.log('awsConfig for sendEmailInsightsNotification => ', awsConfig);

        let s3 = new AWS.S3(awsConfig);

        let data=fs.createReadStream(exportFileName);
        let params = {
            Bucket: process.env.offlineReportBucket,
            Key: filePath,
            Body: data,
            ContentType: s3FileUpload.binaryFile,
            ServerSideEncryption:s3FileUpload.defaultEncryption,
            ContentDisposition: 'attachment; filename="'+fileName+'"'
        };
        let uploadResponse = await s3.upload(params).promise();
        
        // const signedParams = {
        //     Bucket: process.env.offlineReportBucket,
        //     Key: filePath,
        //     Expires:  60 * 60 * 24 * 7, 
        // };
        // const url = await s3.getSignedUrlPromise('getObject', signedParams);
        // console.log(url)

        let { privateKey, cloudFrontDomain, keyPairId } = await formCloudFrontSigner(secretKeys)

        const url = generateCloudFrontSignedUrl(
            keyPairId,
            privateKey,
            cloudFrontDomain,
            filePath
        )

        console.log('url for sendEmailInsightsNotification => ', url);
        return url;
    }
    catch(error){
        console.log('Error in uploadFileInS3 function catch block.', error);
        return ''
    }
};

/**
 * This function is used to generate the CloudFront signer, which is used to
 * generate signed URLs for accessing CloudFront resources.
 *
 * @returns {Object} - An object containing the privateKey, cloudFrontDomain, and
 * keyPairId, which are used to generate the signed URL.
 */
function formCloudFrontSigner(secretKeys) {
    try {
        // CloudFront
        const privateKey = secretKeys.cfpk1_private
            .replace(/\\n/g, '\n')
            .replace(/(-----BEGIN RSA PRIVATE KEY-----)\s+/, '$1\n')
            .replace(/\s+(-----END RSA PRIVATE KEY-----)/, '\n$1')

        const cloudFrontDomain = 'https://'+process.env.offlineReportBucket
        const keyPairId = secretKeys.emscreenskeypairid

        return {
            privateKey,
            cloudFrontDomain,
            keyPairId
        }
    } catch (err) {
        console.log('Error in formCloudFrontSigner function', err)
        throw err
    }
}

/**
 * Generates a signed URL for accessing CloudFront resources using the provided
 * keyPairId, privateKey, cloudFrontDomain, and fileName.
 *
 * @param {string} keyPairId - The ID of the CloudFront key pair.
 * @param {string} privateKey - The private key associated with the CloudFront
 * key pair.
 * @param {string} cloudFrontDomain - The domain name of the CloudFront
 * distribution.
 * @param {string} fileName - The name of the file to access.
 *

 * @returns {string} - A signed URL that can be used to access the specified
 * CloudFront resource.
 */
function generateCloudFrontSignedUrl(
    keyPairId,
    privateKey,
    cloudFrontDomain,
    fileName
) {
    try {
        const AWS = require('aws-sdk')
        
        const signedPath = fileName.replace(/ /g, '+');
      
        const signer = new AWS.CloudFront.Signer(keyPairId, privateKey)
        const signedUrl = signer.getSignedUrl({
            url: cloudFrontDomain + '/' + signedPath,
            expires: Math.floor(Date.now() / 1000) + 90 * 24 * 60 * 60
        })

        return signedUrl
    } catch (err) {
        console.log('Error in generateCloudFrontSignedUrl function', err)
        throw err
    }
}