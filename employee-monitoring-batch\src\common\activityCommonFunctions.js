// require table alias
const { ehrTables } = require('./tableAlias');
// require moment
const moment = require('moment-timezone');

// function to get the start and end hour based on zone
module.exports = {
    getActivityStartAndEndHour:async(timeZone,organizationDbConnection,employeeId,inputDate,workScheduleId) =>{
        try{
            const { formMinMaxTimeByZone } = require('../webApplication/employeeMonitoringCommonFunction');
            let startTime,endTime;
            let minDateTime, maxDateTime,minUtcTime;
            // require moment package
            const moment = require('moment-timezone');
            let zone = timeZone;
            // find the offset for the zone
            let offSet = moment.tz(moment.utc(), zone).utcOffset();
            // find the minimum and maximum UTC time for the given date(based on offset)
            let givenDateMinMaxUtcTime = formMinMaxTimeByZone(inputDate, offSet);
            minUtcTime=givenDateMinMaxUtcTime.minTime;

            if(workScheduleId){
                // find the minimum and maximum UTC time for the given date based on the timezone and the work schedule
                let minMaxDateTime= await formMinAndMaxTimeBasedOnWorkSchedule(organizationDbConnection,workScheduleId,inputDate,zone);
                minDateTime=minMaxDateTime.minDateTime;
                maxDateTime=minMaxDateTime.maxDateTime;
            }
            else{
                minDateTime=minUtcTime;
                maxDateTime=givenDateMinMaxUtcTime.maxTime;
            }
            // activity start time
            startTime = await organizationDbConnection(ehrTables.empActivityDetails).min('Activity_Start_Date_Time as minTime')
                            .where('Employee_Id',employeeId)
                            .andWhere('Activity_Start_Date_Time','>=',minDateTime)
                            .andWhere(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"),'<=',maxDateTime)
                            .then((res)=>{
                                return res[0] ? res[0]['minTime'] : null;
                            })
                            .catch(function (catchError) {
                                console.log('Error while calculating start time',catchError);
                                return null;
                            });
            // activity end time
            endTime = await organizationDbConnection(ehrTables.empActivityDetails).max('Activity_Start_Date_Time as maxTime')
                        .where('Employee_Id',employeeId)
                        .andWhere('Activity_Start_Date_Time','>=',minDateTime)
                        .andWhere(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"),'<=',maxDateTime)
                        .then((res)=>{
                            return res[0] ? res[0]['maxTime'] : null;
                        })
                        .catch(function (catchError) {
                            console.log('Error while calculating endtime',catchError);
                            return null;
                        });
            return { startTime,endTime,minDateTime,maxDateTime,offSet,zone,minUtcTime};
        } catch (catchError) {
            console.log('Error in getActivityStartAndEndHour function main catch block', catchError);
            return {};
        }        
    },
    getEmployeeActivityId:async(organizationDbConnection,employeeIdArray,startHour, endHour) =>{
        try{
            let activitySubQuery;
            if(startHour)
            {
                let subQuery=organizationDbConnection(ehrTables.empActivityDetails)
                .pluck('Employee_Activity_Id')
                .whereBetween('Activity_Start_Date_Time', [startHour, endHour])
                .orderBy('Activity_Start_Date_Time', 'asc') 

                // based on employeeIdArray input form query
                if(employeeIdArray.length>0){
                    activitySubQuery= subQuery
                    .whereIn('Employee_Id', employeeIdArray)
                }
                // retrieve the activity id as a array
                    let activity=await activitySubQuery
                    .then((activityResponse)=>{
                        return activityResponse;
                    })
                    .catch(getIdError => {
                        console.log('Error in getEmployeeActivityId function .catch block', getIdError);
                        return [];
                    })
                return activity;
            }
            else{
                console.log('Start hour is empty in getEmployeeActivityId function');
                return [];
            }
        } catch (catchError) {
            console.log('Error in getEmployeeActivityId function main catch block', catchError);
            return [];
        }        
    },
    getTotalAppUrlActivities:async(activityIdArray,organizationDbConnection,source) =>{
        let totalDuration=0,tableName;
        try{
            tableName=(source==='apps')?ehrTables.empActivityApps:ehrTables.empActivityUrls;
            // We need to get the total duration for an employee
            // We will send activity id array as a array for which duration need to be sum
            totalDuration = await organizationDbConnection(tableName)
            .sum('Time_Spent as duration')
            .whereIn('Employee_Activity_Id',activityIdArray)
            .then((result) => {
                return result[0] ? result[0]['duration'] : 0;
            })
            .catch(function (catchError) {
                console.log('Error in getTotalAppUrlActivities .catch block',catchError);
                return 0;
            })
            return {totalDuration};
        } catch (catchError) {
            console.log('Error in getTotalAppUrlActivities function main catch block', catchError);
            return {totalDuration};
        }        
    },
    // function to get apps activity duration based on category wise
    getDurationBasedOnCategory:async(activityIdArray,organizationDbConnection,source,getEmployeeCustomGroup=null) =>{
        let productiveDuration=0;
        let unproductiveDuration=0;
        try{
            // calculate the duration based on input source 
            let categoryBasedDuration=(source==='apps')?
                organizationDbConnection(ehrTables.empActivityApps+ ' as EAA')
                .select('OAS.Category',organizationDbConnection.raw('SUM(EAA.Time_Spent)as duration'))
                .whereIn('EAA.Employee_Activity_Id',activityIdArray)
                .groupBy('OAS.Category'):
                organizationDbConnection(ehrTables.empActivityUrls+ ' as EAU')
                .select('OUS.Category',organizationDbConnection.raw('SUM(EAU.Time_Spent)as duration'))
                .whereIn('EAU.Employee_Activity_Id',activityIdArray)
                .groupBy('OUS.Category')

                if(getEmployeeCustomGroup){
                    if(source==='apps'){
                        categoryBasedDuration=categoryBasedDuration
                        .innerJoin(ehrTables.customGroupAppSettings + ' as OAS', function() {
                            this.on('EAA.Application_Id', '=', 'OAS.Application_Id')
                                .on('OAS.Group_Id', '=', getEmployeeCustomGroup)
                        })
                    }
                    else{
                        categoryBasedDuration=categoryBasedDuration
                        .innerJoin(ehrTables.customGroupUrlSettings + ' as OUS', function() {
                            this.on('EAU.Domain_Id', '=', 'OUS.Domain_Id')
                                .on('OUS.Group_Id', '=', getEmployeeCustomGroup)
                        })
                    }
                }
                else{
                    if(source==='apps'){
                        categoryBasedDuration=categoryBasedDuration
                        .innerJoin(ehrTables.organizationLevelAppSettings+ ' as OAS','EAA.Application_Id','OAS.Application_Id')
                    }
                    else{
                        categoryBasedDuration=categoryBasedDuration
                        .innerJoin(ehrTables.organizationLevelUrlSettings+ ' as OUS','EAU.Domain_Id','OUS.Domain_Id')
                    }
                }
                return(
                    categoryBasedDuration
                    .then(async(getDuration)=>{
                    if(getDuration.length>0){
                        for(let key of getDuration){
                            if(key.Category.toLowerCase()==='productive'){
                                productiveDuration=parseInt(key.duration);
                            }
                            if(key.Category.toLowerCase()==='unproductive'){
                                unproductiveDuration=parseInt(key.duration);
                            }
                        }
                    }
                    return {productiveDuration,unproductiveDuration};
                    })
                    .catch(function (catchError) {
                        console.log('Error in getDurationBasedOnCategory .catch block',catchError);
                        return {productiveDuration,unproductiveDuration}
                    })
                )
        } catch (mainCatch) {
            console.log('Error in getDurationBasedOnCategory function main catch block', mainCatch);
            return {productiveDuration,unproductiveDuration}
        }        
    },
    formHours: (time)=>{
        let startHour,endHour='';
        try{
            time = time.split(' ');
            let startHour = new Date(time[0]);
        
            let hour = time[1].split(':');
        
            startHour.setUTCHours(Number(hour[0]));
            startHour.setUTCMinutes(Number(hour[1]));
            startHour.setUTCMilliseconds(0);
        
            let endHour = new Date(time[0]);
        
            endHour.setUTCHours(Number(hour[0]) + 1);
            endHour.setUTCMinutes(Number(hour[1]));
            endHour.setUTCMilliseconds(0);
        
            startHour = startHour.toISOString().substring(0, 19).replace('T',' ');
            endHour = endHour.toISOString().substring(0, 19).replace('T',' ');
        
            return { 
                startHour,
                endHour
            }    
        } catch (catchError) {
            console.log('Error in formHours function main catch block', catchError);
            return {startHour,endHour};
        }
    },
    // get employeeId based on loggedIn employee access
    getEmployeeIdBasedOnRoles:(organizationDbConnection,logInEmpId,isAdmin,workScheduleId=0)=>{
        try{
            // check if the logged employee is not admin/superadmin/manager then return only their record and 
            // if they are manager then return their subordinate team members
            // if loggedIn employee is admin then return all the active members
            if(isAdmin === 1){
                empIdSubQuery = organizationDbConnection(ehrTables.teamMembers+' as EMTM')
                                .pluck('EMTM.Employee_Id')
                                .where('EMTM.Member_Status', 'Active');
                if(workScheduleId > 0){
                    empIdSubQuery = empIdSubQuery
                    .innerJoin(ehrTables.empJob + ' as EJ','EJ.Employee_Id','EMTM.Employee_Id')
                    .where('EJ.Work_Schedule',workScheduleId);
                }
            }else{
                // Here the loggedIn employee is may be a manager or employee . Based on that get the team members
                empIdSubQuery = organizationDbConnection(ehrTables.teamMembers+ ' as EMTM')
                                .pluck('EMTM.Employee_Id')
                                .innerJoin(ehrTables.empJob + ' as EJ','EJ.Employee_Id','EMTM.Employee_Id')
                                .where('EMTM.Member_Status', 'Active')
                                .where('EJ.Manager_Id', logInEmpId)
                                .orWhere('EMTM.Employee_Id', logInEmpId);
                if(workScheduleId > 0){
                    empIdSubQuery = empIdSubQuery.where('EJ.Work_Schedule',workScheduleId);
                }
            }
            
            return empIdSubQuery;
        }
        catch(catchError){
            console.log('Error in getEmployeeIdBasedOnRoles function main catch block', catchError);
            return '';
        }
    },
    /**
     * Function to retrieve the data retention period from organization settings
     * @param {Object} organizationDbConnection - Organization Database connection object
     * @returns {Object} - Return the rention period if value does not exist by default we return as 3 months
     * In case of error instead of returning error we return as 3 months
     */
    getDataRetentionSettings(organizationDbConnection){
        // require constant values
        const { defaultValues } = require('../common/appConstants');
        let defaultRetention=defaultValues.defaultRetentionPeriod;
        try{
            // get the data retention period from employee monitor settings table
            return(
                organizationDbConnection(ehrTables.employeeMonitorSettings)
                .select('Data_Retention_Period')
                .then(getMonths=>{
                    return (getMonths.length>0)? getMonths[0].Data_Retention_Period:defaultRetention;
                })
                .catch(error => {
                    console.log('Error in getDataRetentionSettings function .catch block.',error);
                    return defaultRetention;
                })
            );
        }
        catch(catchError){
            console.log('Error in getDataRetentionSettings function main catch block', catchError);
            return defaultRetention;
        }
    },
    // get my activity total worked hours and percentage for a single day and week
    getTotalActivities:async(employeeId,minDate, maxDate, organizationDbConnection)=>{
        // variable declaration
        let todayDuration='';
        let activePercentage='';
        let notActivePercentage='';
        let idlePercentage='';
        try {
            let subQuery=organizationDbConnection(ehrTables.empActivityDetails)
            .sum('Total_Activity_Duration as duration')
            .where('Employee_Id', employeeId)
            .where('Activity_Start_Date_Time', '>=', minDate)
            .where(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"), '<=', maxDate)
            // get total worked hours of the employee (Today)
            todayDuration = await subQuery
                        .then(async(res) => {
                            if(res.length>0 && res[0]['duration']){
                                activePercentage=await organizationDbConnection(ehrTables.empActivityDetails)
                                .sum('Total_Activity_Duration as duration')
                                .where('Employee_Id', employeeId)
                                .where('Activity_Start_Date_Time', '>=', minDate)
                                .where(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"), '<=', maxDate)
                                .where('Activity_Status','Active')
                                .then(activeData => {
                                    if(activeData.length>0 && activeData[0]['duration']){
                                        let percentage=Math.round((activeData[0]['duration']/res[0]['duration'])*100)
                                        return percentage;
                                    }
                                    else{
                                        return 0;
                                    }
                                });

                                notActivePercentage=await organizationDbConnection(ehrTables.empActivityDetails)
                                .sum('Total_Activity_Duration as duration')
                                .where('Employee_Id', employeeId)
                                .where('Activity_Start_Date_Time', '>=', minDate)
                                .where(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"), '<=', maxDate)
                                .where('Activity_Status','Not Active')
                                .then(notactiveData => {
                                    if(notactiveData.length>0 && notactiveData[0]['duration']){
                                        let percentage=Math.round((notactiveData[0]['duration']/res[0]['duration'])*100)
                                        return percentage;
                                    }
                                    else{
                                        return 0;
                                    }
                                });
                
                                idlePercentage=await organizationDbConnection(ehrTables.empActivityDetails)
                                .sum('Total_Activity_Duration as duration')
                                .where('Employee_Id', employeeId)
                                .where('Activity_Start_Date_Time', '>=', minDate)
                                .where(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"), '<=', maxDate)
                                .where('Activity_Status','Idle')
                                .then(idleData => {
                                    if(idleData.length>0 && idleData[0]['duration']){
                                        let percentage=Math.round((idleData[0]['duration']/res[0]['duration'])*100)
                                        return percentage;
                                    }
                                    else{
                                        return 0;
                                    }
                                })
                                return res[0]['duration'];
                            }
                            else{
                                console.log('Error in calculating total activity duration. So return empty response.');
                            }
                        })
                        .catch(error => {
                            console.log('Error in getTotalActivities function .catch block.',error);
                        })
                    return {todayDuration,activePercentage,notActivePercentage,idlePercentage};
        }
        catch(catchError){
            console.log('Error in getTotalActivities function main catch block', catchError);
            return { todayDuration,activePercentage,notActivePercentage,idlePercentage};
        }
    },
    /** function to get top 5 apps and urls */
    getTopFiveAppAndDomain:(orgDbConnection,empIdArray,date)=>{
        let response={
            appData:[],
            urlData:[]
        };
        try{
            /** get the top 5 app and url used in the input activity date. Convert the duration from seconds to HH:MM:SS format  */
            return(
                orgDbConnection
                .select('Application_Name',orgDbConnection.raw('CASE WHEN sum(Total_Activity_Duration) > 0 then TIME_FORMAT(SEC_TO_TIME(SUM(Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as totalActivityDuration'))
                .from(ehrTables.appActivityDailySummary)
                .whereIn('Employee_Id',empIdArray)
                .where('Activity_Date',date)
                .groupBy('Application_Id')
                .orderBy('totalActivityDuration', 'Desc')
                .limit(5)
                .then(async(appData)=>{
                    response.appData=appData;
                    return(
                        orgDbConnection
                        .select('Domain_Name',orgDbConnection.raw('CASE WHEN sum(Total_Activity_Duration) > 0 then TIME_FORMAT(SEC_TO_TIME(SUM(Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as totalActivityDuration'))
                        .from(ehrTables.urlActivityDailySummary)
                        .whereIn('Employee_Id',empIdArray)
                        .where('Activity_Date',date)
                        .groupBy('Domain_Id')
                        .orderBy('totalActivityDuration', 'Desc')
                        .limit(5)
                        .then(async(urlData)=>{
                            response.urlData=urlData;
                            return response;
                        })
                    );
                })
                .catch(error => {
                    console.log('Error in getTopFiveAppAndDomain function .catch block',error);
                    return response;
                })
            )
        }
        catch(error){
            console.log('Error in getTopFiveAppAndDomain function main catch block',error);
            return response;
        }
    }
};


/** Function to form min and max time based on work schedule and return the response in UTC format
 * @param {JSON} organizationDbConnection - Organization Database connection object
 * @param {Number} workScheduleId - workSchedule Id
 * @param {Date} givenDate - Activity date
 * @param {String} zone - time zone
 * @param {JSON} workScheduleData - work schedule details for the work schedule id
 * @returns {JSON} - Return min and max date time.
 */
async function formMinAndMaxTimeBasedOnWorkSchedule(organizationDbConnection,workScheduleId,givenDate,zone,workScheduleData={}){
    try{
        //Require common library
        const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
        let response= await commonLib.productivityMonitoring.formMinAndMaxTimeBasedOnWorkSchedule(organizationDbConnection,workScheduleId,givenDate,zone,workScheduleData);
        return response;
    }
    catch(catchError){
        console.log('Error in formMinAndMaxTimeBasedOnWorkSchedule function main catch block',catchError);
        throw 'EM0198';
    }
}

// function to get the total application time based on activityId
async function getApplicationDurationBasedOnActivityId(organizationDbConnection,activityIdArray){
    try{
        // get the app time based on category
        let appQuery=organizationDbConnection(ehrTables.empActivityApps+ ' as EAA')
        .select(organizationDbConnection.raw('SUM(EAA.Time_Spent)as totalAppTimeInSeconds'),
        organizationDbConnection.raw('CASE WHEN OAS.Category THEN OAS.Category ELSE "Uncategorized" END as Category'))
        .leftJoin(ehrTables.organizationLevelAppSettings+ ' as OAS','EAA.Application_Id','OAS.Application_Id')
        .whereIn('EAA.Employee_Activity_Id',activityIdArray)
        .groupBy('OAS.Category')

        return (
            await appQuery
            .then(async(appDetails) => {
                if(appDetails.length>0){
                    let output = await Promise.all(appDetails.map(async(field) => {
                        // If category is productive then update time spent as productive app time
                        if(field.Category==='Productive'){
                            field.totalProductiveAppTimeInSeconds = field.totalAppTimeInSeconds;
                        }
                        // If category is unproductive then update time spent as unproductive app time
                        else if(field.Category==='Unproductive'){
                            field.totalUnproductiveAppTimeInSeconds = field.totalAppTimeInSeconds;
                        }
                        // If category is uncategorized then update time spent as neutral app time
                        else if(field.Category==='Uncategorized'){
                            field.totalNeutralAppTimeInSeconds = field.totalAppTimeInSeconds;
                        }
                        delete field.totalAppTimeInSeconds;        
                        return field;
                    }))
                    return output;
                }
                else{
                    console.log('Application details does not exist.');
                    return [];
                }
            })
            .catch(catchError => {
                console.log('Error in getApplicationDurationBasedOnActivityId function .catch block',catchError);
                throw 'EM0174';
            })
        );
    }
    catch(error){
        console.log('Error in getApplicationDurationBasedOnActivityId function main catch block',error);
        throw 'EM0174';
    }
};

// function to get the total url time based on activityId
async function getUrlDurationBasedOnActivityId(organizationDbConnection,activityIdArray){
    try{
        let urlDetailsQuery=organizationDbConnection(ehrTables.empActivityUrls+ ' as EAU')
        .select(organizationDbConnection.raw('SUM(EAU.Time_Spent)as totalUrlTimeInSeconds'),
        organizationDbConnection.raw('CASE WHEN OUS.Category THEN OUS.Category ELSE "Uncategorized" END as Category'))
        .leftJoin(ehrTables.organizationLevelUrlSettings+ ' as OUS','EAU.Domain_Id','OUS.Domain_Id')
        .whereIn('EAU.Employee_Activity_Id',activityIdArray)
        .groupBy('OUS.Category')

        return (
            await urlDetailsQuery
            .then(async(urlDetails) => {
                if(urlDetails.length>0){
                    let output = await Promise.all(urlDetails.map(async(field) => {
                        // If category is productive then update time spent as productive url time
                        if(field.Category==='Productive'){
                            field.totalProductiveUrlTimeInSeconds = field.totalUrlTimeInSeconds;
                        }
                        // If category is unproductive then update time spent as unproductive url time
                        else if(field.Category==='Unproductive'){
                            field.totalUnproductiveUrlTimeInSeconds = field.totalUrlTimeInSeconds;
                        }
                        // If category is uncategorized then update time spent as neutral url time
                        else if(field.Category==='Uncategorized'){
                            field.totalNeutralUrlTimeInSeconds = field.totalUrlTimeInSeconds;
                        }
                        delete field.totalUrlTimeInSeconds;        
                        return field;
                    }))
                    return output;
                }
                else{
                    console.log('Url details does not exist.');
                    return [];
                }
            })
            .catch(catchError => {
                console.log('Error in getUrlDurationBasedOnActivityId function .catch block',catchError);
                throw 'EM0175';
            })
        );
    }
    catch(error){
        console.log('Error in getUrlDurationBasedOnActivityId function main catch block',error);
        throw 'EM0175';
    }
};

// function to get the total application browser time based on activityId
async function  getAppBrowserTimeBasedOnActivityId(organizationDbConnection,activityIdArray){
    try{
        // require constant values
        const { defaultValues } = require('../common/appConstants');

        let appQuery=organizationDbConnection(ehrTables.empActivityApps+ ' as EAA')
        .select(organizationDbConnection.raw('SUM(EAA.Time_Spent)as totalAppTimeInSeconds'),
        organizationDbConnection.raw('CASE WHEN OAS.Category THEN OAS.Category ELSE "Uncategorized" END as Category'))
        .leftJoin(ehrTables.organizationLevelAppSettings+ ' as OAS','EAA.Application_Id','OAS.Application_Id')
        .innerJoin(ehrTables.activityAppMaster+ ' as AAM','EAA.Application_Id','AAM.Application_Id')
        .whereIn('AAM.Application_Name',defaultValues.browserList)
        .whereIn('EAA.Employee_Activity_Id',activityIdArray)
        .groupBy('OAS.Category')

        return (
            await appQuery
            .then(async(appDetails) => {
                if(appDetails.length>0){
                    let output = await Promise.all(appDetails.map(async(field) => {
                        // If category is productive then update time spent as productive app browser time
                        if(field.Category==='Productive'){
                            field.totalProductiveAppBrowserTimeInSeconds = field.totalAppTimeInSeconds;
                        }
                        // If category is unproductive then update time spent as unproductive app browser time
                        else if(field.Category==='Unproductive'){
                            field.totalUnproductiveAppBrowserTimeInSeconds = field.totalAppTimeInSeconds;
                        }
                        // If category is uncategorized then update time spent as neutral app browser time
                        else if(field.Category==='Uncategorized'){
                            field.totalNeutralAppBrowserTimeInSeconds = field.totalAppTimeInSeconds;
                        }
                        delete field.totalAppTimeInSeconds;        
                        return field;
                    }))
                    return output;
                }
                else{
                    console.log('Application browser time does not exist.');
                    return [];
                }
            })
            .catch(catchError => {
                console.log('Error in getAppBrowserTimeBasedOnActivityId function .catch block',catchError);
                throw 'EM0204';
            })
        );
    }
    catch(error){
        console.log('Error in getAppBrowserTimeBasedOnActivityId function main catch block',error);
        throw 'EM0204';
    }
};

//Function to validate the start hour and end hour with the shift margin time
async function validateHoursWithShiftMargin(args){
    try{
        let {startHourTimestamp,endHourTimestamp,shiftMinDateTimestamp,shiftMaxDateTimestamp,minDateTime,maxDateTime,startHour,endHour} = args;
        
        let fetchActivityDetails = 0;
        let shiftMaxTimeExceeded = 0;

        /** Validate the start and end hour falls in the shift margin and retrieve the activity details
         * 1. If both the start hour and end hour fall in the shift margin we need to consider 
         * same hours for calculation.
         * 2. If start hour does not fall in the shift margin we need to consider shift margin
         * start time as start hour.
         * 3. If end hour does not fall in the shift margin we need to consider shift margin
         * end time as end hour.
         * 4. If both the start hour and end hour does not fall in the shift margin we do not need
         * to retrieve the activity details.
         */
        if((startHourTimestamp >= shiftMinDateTimestamp && startHourTimestamp <= shiftMaxDateTimestamp)
        && (endHourTimestamp >= shiftMinDateTimestamp && endHourTimestamp <= shiftMaxDateTimestamp)){
            fetchActivityDetails = 1;
        }else if(startHourTimestamp >= shiftMinDateTimestamp && startHourTimestamp <= shiftMaxDateTimestamp){
            fetchActivityDetails = 1;
            shiftMaxTimeExceeded = 1;
            endHour = maxDateTime;
        }
        else if(endHourTimestamp >= shiftMinDateTimestamp && endHourTimestamp <= shiftMaxDateTimestamp){
            fetchActivityDetails = 1;
            startHour = minDateTime;
        }else{
            fetchActivityDetails = 0;
        }
        return {fetchActivityDetails,shiftMaxTimeExceeded,activityStartHour:startHour,activityEndHour:endHour};
    }catch(catchError){
        console.log('Error in validateHoursWithShiftMargin function main catch block',catchError);
        throw catchError;
    }
}

// function to calculate the total time based on productive and neutral app and url 
async function calculateTimeBasedOnProductiveNeutralAppUrl(inputData){
    try{
        let productiveAppTime=0;
        let productiveUrlTime=0;
        let productiveBrowserTime=0;
        let neutralAppTime=0;
        let neutralUrlTime=0;
        let neutralBrowerTime=0;

        // productive app time
        if(inputData.totalProductiveAppTimeInSeconds){
            productiveAppTime=parseInt(inputData.totalProductiveAppTimeInSeconds)
        }
        // neutral app time
        if(inputData.totalNeutralAppTimeInSeconds){
            neutralAppTime=parseInt(inputData.totalNeutralAppTimeInSeconds);
        }
        // productive url time
        if(inputData.totalProductiveUrlTimeInSeconds){
            productiveUrlTime=parseInt(inputData.totalProductiveUrlTimeInSeconds);
        }
        // neutral url time
        if(inputData.totalNeutralUrlTimeInSeconds){
            neutralUrlTime=parseInt(inputData.totalNeutralUrlTimeInSeconds);
        }
        // productive app browser time
        if(inputData.totalProductiveAppBrowserTimeInSeconds){
            productiveBrowserTime=parseInt(inputData.totalProductiveAppBrowserTimeInSeconds);
        }
        // neutral app browser time
        if(inputData.totalNeutralAppBrowserTimeInSeconds){
            neutralBrowerTime=parseInt(inputData.totalNeutralAppBrowserTimeInSeconds);
        }
        // combine productive and neutral browser time
        let combinedBrowserTime=productiveBrowserTime+neutralBrowerTime;
        // combine productive and neutral url time
        let combinedUrlTime=productiveUrlTime+neutralUrlTime;
        // find the minimum of combined browser and url time
        let minimumOfBrowserUrlTime=Math.min(combinedBrowserTime,combinedUrlTime);
        // calculate the total app and url time
        let totalAppUrlTime=(productiveAppTime+productiveUrlTime+neutralAppTime+neutralUrlTime);
        // total time = (Total productive and neutral app url time) - minimumOfBrowserUrlTime
        totalAppUrlTime=totalAppUrlTime-minimumOfBrowserUrlTime;
        return totalAppUrlTime;
    }
    catch(catchError){
        console.log('Error in calculateTimeBasedOnProductiveNeutralAppUrl function main catch block',catchError);
        return 0;
    }
}


/** function to update key name based on category */
async function updatekeyBasedOnInputCategory(arrayJsondata,source){
    try{
        if(arrayJsondata.length>0){
            if(source==='app'){
                let output = await Promise.all(arrayJsondata.map(async(field) => {
                    // If category is productive then update time spent as productive time spent
                    if(field.appCategory==='Productive'){
                        field.timeSpentOnProductiveAppsInSeconds = field.appTimeSpent;
                    }
                    // If category is unproductive then update time spent as unproductive time spent
                    else if(field.appCategory==='Unproductive'){
                        field.timeSpentOnUnproductiveAppsInSeconds = field.appTimeSpent;
                    }
                    // If category is uncategorized then update time spent as neutral time spent
                    else if(field.appCategory==='Uncategorized'){
                        field.timeSpentOnNeutralAppsInSeconds = field.appTimeSpent;
                    }
                    // delete field.appTimeSpent;
                    return field;
                }))
                return output;
            }
            if(source==='url'){
                let output = await Promise.all(arrayJsondata.map(async(field) => {
                    // If category is productive then update time spent as productive time spent
                    if(field.urlCategory==='Productive'){
                        field.timeSpentOnProductiveUrlsInSeconds = field.urlTimeSpent;
                    }
                    // If category is unproductive then update time spent as unproductive time spent
                    else if(field.urlCategory==='Unproductive'){
                        field.timeSpentOnUnproductiveUrlsInSeconds = field.urlTimeSpent;
                    }
                    // If category is uncategorized then update time spent as neutral time spent
                    else if(field.urlCategory==='Uncategorized'){
                        field.timeSpentOnNeutralUrlsInSeconds = field.urlTimeSpent;
                    }
                    // delete field.urlTimeSpent;
                    return field;
                }))
                return output;
            }
        }
        else{
            console.log('Input is empty inside updatekeyBasedOnInputCategory function');
            return [];
        }
    }
    catch(catchError){
        console.log('Error in updatekeyBasedOnInputCategory function main catch block',catchError);
        return [];
    }
}

// get the active employeeIds associated with the work schedule
async function getEmployeeIdsBasedOnWorkSchedule(organizationDbConnection,workScheduleId,inputStatus){
    try{
        return (
            organizationDbConnection(ehrTables.teamMembers+' as EMTM')
            .pluck('EMTM.Employee_Id')
            .innerJoin(ehrTables.empJob + ' as EJ','EJ.Employee_Id','EMTM.Employee_Id')
            .where('EJ.Work_Schedule',workScheduleId)
            .whereIn('EMTM.Member_Status', inputStatus)
            .then(getIds => {
                if(getIds.length>0){
                    return getIds;
                }
                else{
                    console.log('No employees exist');
                    return [];
                }
            })
            .catch(catchError => {
                console.log('Error in getEmployeeIdsBasedOnWorkSchedule function .catch block.', catchError);
                throw 'EM0200';
            })
        )
    }
    catch(error){
        console.log('Error in getEmployeeIdsBasedOnWorkSchedule function main catch block',error);
        throw 'EM0200';
    }
}

// function to get the custom groupIds which is associated with the employeesIds
async function getGroupIdForEmployeeIds(organizationDbConnection,employeeIdArray,formId){
    try{
        return(
            organizationDbConnection
            .pluck('Custom_Group_Id')
            .from(ehrTables.customGroupAssociatedForms)
            .where('Form_Id',formId)
            .then(getGroupIds=>{
                if(getGroupIds.length>0){
                    return(
                        organizationDbConnection
                        .select('Employee_Id as employeeId','Group_Id')
                        .from(ehrTables.customEmployeeGroupEmployees)
                        .whereIn('Group_Id',getGroupIds)
                        .whereIn('Employee_Id', employeeIdArray)
                        .groupBy('Employee_Id')
                        .then(getGroupDetails=>{
                            return (getGroupDetails.length>0)?getGroupDetails:[];
                        })
                    )
                }
                else{
                    console.log('No custom group details exists.');
                    return [];
                }
            })
            .catch(error => {
                console.log('Error in getGroupIdForEmployeeIds function .catch block.',error);
                throw 'Error in getting the custom group details';
            })
        );
    }
    catch(catchError){
        console.log('Error in getGroupIdForEmployeeIds function main catch block',catchError);
        throw 'Error in getting the custom group details';
    }
}


// function to calculate productivity duration and percentage
async function calculateProductivityBasedOnAppUrlData(resultAppUrlData, fixedDailyWorkHours){
    let outputData=[];
    try{
        console.log('Inside calculateProductivityBasedOnAppUrlData function');
        const { getTimeInHoursMinutesSeconds }=require('../webApplication/employeeMonitoringCommonFunction');
        await Promise.all(resultAppUrlData.map(async(record) => {
            // variable declarations
            let output={};
            let productiveAppTime=0;
            let productiveUrlTime=0;
            let productiveBrowserTime=0;
            let unProductiveAppTime=0;
            let unProductiveUrlTime=0;
            let unProductiveBrowserTime=0;
            let neutralAppTime=0;
            let neutralUrlTime=0;
            let neutralBrowserTime=0;

            // productive app time
            if(record.totalProductiveAppTime){
                productiveAppTime = parseInt(record.totalProductiveAppTime);
            }
            // productive app browser time
            if(record.totalBrowserProductiveAppTime){
                productiveBrowserTime=parseInt(record.totalBrowserProductiveAppTime);
            }
            // productive url time
            if(record.totalProductiveUrlTime){
                productiveUrlTime=parseInt(record.totalProductiveUrlTime);
            }

            // unproductive app time
            if(record.totalUnproductiveAppTime){
                unProductiveAppTime = parseInt(record.totalUnproductiveAppTime);
            }
            // unproductive app browser time
            if(record.totalBrowserUnproductiveAppTime){
                unProductiveBrowserTime=parseInt(record.totalBrowserUnproductiveAppTime);
            }
            // unproductive url time
            if(record.totalUnproductiveUrlTime){
                unProductiveUrlTime=parseInt(record.totalUnproductiveUrlTime);
            }

            // neutral app time
            if(record.totalNeutralAppTime){
                neutralAppTime = parseInt(record.totalNeutralAppTime);
            }
            // neutral app browser time
            if(record.totalBrowserNeutralAppTime){
                neutralBrowserTime=parseInt(record.totalBrowserNeutralAppTime);
            }
            // neutral url time
            if(record.totalNeutralUrlTime){
                neutralUrlTime=parseInt(record.totalNeutralUrlTime);
            }

            // calculate total unproductive app and url time and exclude unproductive browser time
            let totalUnProductiveAppUrlTime=(unProductiveAppTime+unProductiveUrlTime)-unProductiveBrowserTime;

            // calculate total neutral app and url time and exclude neutral browser time
            let totalNeutralAppUrlTime=(neutralAppTime+neutralUrlTime)-neutralBrowserTime;

            // calculate total productive app and url time
            let totalProductiveAppUrlTime=productiveAppTime+productiveUrlTime;
            
            // calculate total browser app time
            let totalBrowserAppTime=parseInt(productiveBrowserTime+unProductiveBrowserTime+neutralBrowserTime);
            
            // calculate total url time
            let totalUrlTime =parseInt(productiveUrlTime+unProductiveUrlTime+neutralUrlTime);

            // difference of browser app and url time
            let differenceOfBrowserAndUrl=(totalBrowserAppTime - totalUrlTime);

            if(differenceOfBrowserAndUrl>0){
                // in private mode and accessing local file in browser -> we will track only app and not the url so we are adding  the differenceOfBrowserAndUrl for productive time calculation
                // total productive time will be total productive app and url time - total browser app time + difference of browser and url time
                totalProductiveAppUrlTime = (totalProductiveAppUrlTime + differenceOfBrowserAndUrl) - (productiveBrowserTime);
            }
            else{
                console.log('Difference of app browser and url time is less than 0',differenceOfBrowserAndUrl);
                // total productive time will be total productive app and url time - total browser app time
                totalProductiveAppUrlTime = (totalProductiveAppUrlTime) - (productiveBrowserTime);
            }

            /** Calculate user productivity  */
            // combine productive and neutral url time
            let combinedProductiveAndNeutralUrlTime=productiveUrlTime+neutralUrlTime;

            // find the minimum of combined browser and url time
            let minimumOfBrowserUrlTime=Math.min(totalBrowserAppTime,combinedProductiveAndNeutralUrlTime);

            // combine productive and neutral app/url time
            let totalProductiveAndNeutralAppUrlTime=productiveAppTime+productiveUrlTime+neutralAppTime+neutralUrlTime;

            // totalProductiveAndNeutralAppUrlTime = (Total productive and neutral app url time) - minimumOfBrowserUrlTime
            totalProductiveAndNeutralAppUrlTime=totalProductiveAndNeutralAppUrlTime-minimumOfBrowserUrlTime;

            // total app time will be sum of productive+unproductive+neutral app time
            let totalTimeSpentOnApps=productiveAppTime+unProductiveAppTime+neutralAppTime;
            
            // productivity percentage = (totalProductiveAndNeutralAppUrlTime/totalTimeSpentOnApps)*100
            let userProductivityPercentageProductiveAndNeutral=Math.round(((parseInt(totalProductiveAndNeutralAppUrlTime))/parseInt(totalTimeSpentOnApps))*100);

            // productivity percentage = (totalProductiveAndNeutralAppUrlTime/totalTimeSpentOnApps)*100
            let userProductivityPercentageProductiveOnly=Math.round(((parseInt(totalProductiveAppUrlTime))/parseInt(totalTimeSpentOnApps))*100);

            // productivity percentage = (totalProductiveAndNeutralAppUrlTime/totalTimeSpentOnApps)*100
            let userProductivityPercentageBasedOnFixedDailyWorkHours=Math.round(((parseInt(totalProductiveAndNeutralAppUrlTime))/parseInt(fixedDailyWorkHours*3600))*100);

            // productivity percentage = (totalProductiveAndNeutralAppUrlTime/totalTimeSpentOnApps)*100
            let userProdPercentageProductiveOnlyBasedOnFixedHours=Math.round(((parseInt(totalProductiveAppUrlTime))/parseInt(fixedDailyWorkHours*3600))*100);

            // form the output response
            output.productiveTimeInSeconds=(totalProductiveAppUrlTime)?totalProductiveAppUrlTime:0;
            output.unProductiveTimeInSeconds=(totalUnProductiveAppUrlTime)?totalUnProductiveAppUrlTime:0;
            output.neutralTimeInSeconds=totalNeutralAppUrlTime?totalNeutralAppUrlTime:0;
            output.userProductiveDurationInSeconds=totalProductiveAndNeutralAppUrlTime?totalProductiveAndNeutralAppUrlTime:0;
     
            output.productiveTimeInMinutes=(totalProductiveAppUrlTime)?await Math.round(parseInt(totalProductiveAppUrlTime)/60):0;
            output.unProductiveTimeInMinutes=(totalUnProductiveAppUrlTime)?await Math.round(parseInt(totalUnProductiveAppUrlTime)/60):0;
            output.neutralTimeInMinutes=totalNeutralAppUrlTime?await Math.round(parseInt(totalNeutralAppUrlTime)/60):0;
            output.userProductiveDurationInMinutes=totalProductiveAndNeutralAppUrlTime?await Math.round(parseInt(totalProductiveAndNeutralAppUrlTime)/60):0;

            output.productiveTime=(totalProductiveAppUrlTime)?await getTimeInHoursMinutesSeconds(totalProductiveAppUrlTime):0;
            output.unProductiveTime=(totalUnProductiveAppUrlTime)?await getTimeInHoursMinutesSeconds(totalUnProductiveAppUrlTime):0;
            output.neutralTime=totalNeutralAppUrlTime?await getTimeInHoursMinutesSeconds(totalNeutralAppUrlTime):0;
            output.userProductiveDuration=totalProductiveAndNeutralAppUrlTime?await getTimeInHoursMinutesSeconds(totalProductiveAndNeutralAppUrlTime):0;
            // round the percentage to 100 if it exceeds 100
            output.userProductivityPercentageProductiveAndNeutral=userProductivityPercentageProductiveAndNeutral?(userProductivityPercentageProductiveAndNeutral>100)?100:userProductivityPercentageProductiveAndNeutral:0;
            output.employeeId=record.employeeId;
            output.activityDate=record.activityDate;
            output.userProductivityPercentageProductiveOnly = userProductivityPercentageProductiveOnly?(userProductivityPercentageProductiveOnly>100)?100:userProductivityPercentageProductiveOnly:0;
            output.userProdPercentageProductiveOnlyBasedOnFixedHours= userProdPercentageProductiveOnlyBasedOnFixedHours ? Math.round(userProdPercentageProductiveOnlyBasedOnFixedHours).toFixed(2) : 0;
            output.userProductivityPercentageBasedOnFixedDailyWorkHours= userProductivityPercentageBasedOnFixedDailyWorkHours ? Math.round(userProductivityPercentageBasedOnFixedDailyWorkHours).toFixed(2) : 0;
            outputData.push(output);
        }));
        return outputData;
    }
    catch(error){
        console.log('Error in calculateProductivityBasedOnAppUrlData function catch block.',error);
        return outputData;
    }
}

// function to calculate category wise app time and browser app time
async function calculateAppAndBrowserTime(data){
    try{
        // require constant values
        const { defaultValues } = require('../common/appConstants');

        let totalProductiveAppTime=0;
        let totalUnproductiveAppTime=0;
        let totalNeutralAppTime=0;
        let totalBrowserProductiveAppTime=0;
        let totalBrowserUnproductiveAppTime=0;
        let totalBrowserNeutralAppTime=0;
        // get the browser application list
        let browserList=defaultValues.browserList;
        for(let field of data){
            employeeId=field.employeeId;
            let checkBrowserApplication=browserList.includes(field.applicationName);
            if(field.timeSpentOnProductiveAppsInSeconds){
                totalProductiveAppTime=totalProductiveAppTime+parseInt(field.timeSpentOnProductiveAppsInSeconds);
                (checkBrowserApplication)?totalBrowserProductiveAppTime=totalBrowserProductiveAppTime+parseInt(field.timeSpentOnProductiveAppsInSeconds):'';
            }
            if(field.timeSpentOnUnproductiveAppsInSeconds){
                totalUnproductiveAppTime=totalUnproductiveAppTime+parseInt(field.timeSpentOnUnproductiveAppsInSeconds);
                (checkBrowserApplication)?totalBrowserUnproductiveAppTime=totalBrowserUnproductiveAppTime+parseInt(field.timeSpentOnUnproductiveAppsInSeconds):'';
            }
            if(field.timeSpentOnNeutralAppsInSeconds){
                totalNeutralAppTime=totalNeutralAppTime+parseInt(field.timeSpentOnNeutralAppsInSeconds);
                (checkBrowserApplication)?totalBrowserNeutralAppTime=totalBrowserNeutralAppTime+parseInt(field.timeSpentOnNeutralAppsInSeconds):'';
            }
        }
        return {
            employeeId:employeeId,
            totalProductiveAppTime:totalProductiveAppTime,
            totalUnproductiveAppTime:totalUnproductiveAppTime,
            totalNeutralAppTime:totalNeutralAppTime,
            totalBrowserProductiveAppTime:totalBrowserProductiveAppTime,
            totalBrowserUnproductiveAppTime:totalBrowserUnproductiveAppTime,
            totalBrowserNeutralAppTime:totalBrowserNeutralAppTime
        };
    }
    catch(error){
        console.log('Error in calculateAppAndBrowserTime function catch block.',error);
        return {};
    }
}

// function to calculate category wise url time
async function calculateUrlAndBrowserTime(data){
    try{
        let totalProductiveUrlTime=0;
        let totalUnproductiveUrlTime=0;
        let totalNeutralUrlTime=0;
        for(let field of data){
            employeeId=field.employeeId;
            if(field.timeSpentOnProductiveUrlsInSeconds){
                totalProductiveUrlTime=totalProductiveUrlTime+parseInt(field.timeSpentOnProductiveUrlsInSeconds);
            }
            if(field.timeSpentOnUnproductiveUrlsInSeconds){
                totalUnproductiveUrlTime=totalUnproductiveUrlTime+parseInt(field.timeSpentOnUnproductiveUrlsInSeconds);
            }
            if(field.timeSpentOnNeutralUrlsInSeconds){
                totalNeutralUrlTime=totalNeutralUrlTime+parseInt(field.timeSpentOnNeutralUrlsInSeconds);
            }
        }
        return {
            employeeId:employeeId,
            totalProductiveUrlTime:totalProductiveUrlTime,
            totalUnproductiveUrlTime:totalUnproductiveUrlTime,
            totalNeutralUrlTime:totalNeutralUrlTime
        };
    }
    catch(error){
        console.log('Error in calculateUrlAndBrowserTime function catch block.',error);
        return {};
    }
}

//function to get workschedule and time zone according to employeeId
async function getWorkScheduleAndTimeZone(organizationDbConnection,employeeId)
{
    try{
        return(
            organizationDbConnection(ehrTables.empJob)
            .select('EJ.Work_Schedule as workScheduleId','T.TimeZone_Id as timeZone')
            .from(ehrTables.empJob +" as EJ")
            .leftJoin(ehrTables.workSchedule + " as WS","EJ.Work_Schedule","WS.WorkSchedule_Id")
            .leftJoin(ehrTables.timezone + " as T","T.Zone_Id","WS.Zone_Id")
            .where("EJ.Employee_Id",employeeId)
            .then(data=>{
                if(data.length>0)
                {
                    return data[0];
                }
                return {};
            })
            .catch(e=>{
                console.log("Error in getWorkScheduleAndTimeZone function .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getWorkScheduleAndTimeZone function main catch block",e);
        return false;
    }
}

async function getEmployeeWorkScheduleDetails (organizationDbConnection, employeeId) {
    try {
        console.log("Inside getEmployeeWorkScheduleDetails function");
        return(
            organizationDbConnection(ehrTables.workSchedule+' as WS')
            .select('WS.Title','WS.WorkSchedule_Id','WS.Regular_Work_Start_Time',
            'WS.Regular_Work_End_Time','WS.Check_In_Consideration_Time','WS.Check_Out_Consideration_Time',
            'WS.Twodays_Flag','TZ.TimeZone_Id as timeZone','WS.Zone_Id')
            .innerJoin(ehrTables.empJob+' as EJ','EJ.Work_Schedule','WS.WorkSchedule_Id')
            .innerJoin(ehrTables.timezone+' as TZ', 'WS.Zone_Id','TZ.Zone_Id')
            .where('EJ.Employee_Id',employeeId)
            .then(async(workScheduleDetails)=>{
                if(Object.keys(workScheduleDetails).length > 0){
                    let timeZone = workScheduleDetails[0].timeZone;
                    let currentDate = moment().tz(timeZone).format('YYYY-MM-DD');
                    //Find the minimum and maximum UTC time for the given date based on the timezone and the work schedule
                    let { minDateTime, maxDateTime, minDateTimeByZone,maxDateTimeByZone } = await formMinAndMaxTimeBasedOnWorkSchedule(organizationDbConnection,workScheduleDetails[0].WorkSchedule_Id,currentDate,timeZone,workScheduleDetails);
                    //Form the response
                    let workScheduleResponse = {
                        workScheduleId:workScheduleDetails[0].WorkSchedule_Id,
                        workSchedule:workScheduleDetails[0].Title,
                        shiftMarginStartDateTime:minDateTimeByZone,
                        shiftMarginEndDateTime:maxDateTimeByZone,
                        minDateTime:minDateTime,
                        maxDateTime:maxDateTime,
                        timeZoneId:workScheduleDetails[0].Zone_Id,
                        timeZone:timeZone
                    };
                    return workScheduleResponse;
                }else{
                    return {};
                }
            })
            .catch(catchError => {
                console.log('Error in getEmployeeWorkScheduleDetails() function .catch block',catchError);
                return false;
            })
        );
    } catch (err){
        console.log('Error in getEmployeeWorkScheduleDetails() function main catch block',err);
        return false;
    }
}


module.exports.formMinAndMaxTimeBasedOnWorkSchedule = formMinAndMaxTimeBasedOnWorkSchedule;
module.exports.getApplicationDurationBasedOnActivityId=getApplicationDurationBasedOnActivityId;
module.exports.getUrlDurationBasedOnActivityId=getUrlDurationBasedOnActivityId;
module.exports.getAppBrowserTimeBasedOnActivityId=getAppBrowserTimeBasedOnActivityId;
module.exports.validateHoursWithShiftMargin=validateHoursWithShiftMargin;
module.exports.calculateTimeBasedOnProductiveNeutralAppUrl=calculateTimeBasedOnProductiveNeutralAppUrl;
module.exports.updatekeyBasedOnInputCategory=updatekeyBasedOnInputCategory;
module.exports.getEmployeeIdsBasedOnWorkSchedule=getEmployeeIdsBasedOnWorkSchedule;
module.exports.getGroupIdForEmployeeIds=getGroupIdForEmployeeIds;
module.exports.calculateProductivityBasedOnAppUrlData=calculateProductivityBasedOnAppUrlData;
module.exports.calculateAppAndBrowserTime=calculateAppAndBrowserTime;
module.exports.calculateUrlAndBrowserTime=calculateUrlAndBrowserTime;
module.exports.getWorkScheduleAndTimeZone=getWorkScheduleAndTimeZone;
module.exports.getEmployeeWorkScheduleDetails=getEmployeeWorkScheduleDetails;
