'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex'); 
// require table alias function
const { appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { formIds,defaultValues } = require('../common/appConstants');
// require common function

//this function is used to prepare data(active instance) in app_purge_manager table as well as in domain_purge_manager table
module.exports.getEmpMonitoringSubscribedInstanceUpdateAppUrlPurge  = async(event, context) =>{
    let appmanagerDbConnection;
    try{
        // get input data
        let inputStatus=event.status;
        if(inputStatus && inputStatus.toLowerCase()==='open')
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.
            Incase of input status as 'Open' proceed to the purge process */
            console.log('Event triggered to process remaining records so move to step2');
            let response={
                nextStep:'Step2',
                input:{'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length){
                // form app manager database connection
                appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                //get the active instances which subscribed empmonitoring
                let empMonitoringSubscribedUsers= await commonLib.func.getInstanceSubscribedToFormId(formIds.activityTracker,appmanagerDbConnection,defaultValues.activeStatus);
                console.log('Employee monitoring subscribed user count',empMonitoringSubscribedUsers.length);
                let insertDate=new Date();
                let inputData=[];
                for(let i=0;i<empMonitoringSubscribedUsers.length;i++)
                {
                    inputData.push({
                        Org_Code: empMonitoringSubscribedUsers[i],
                        Purge_Status: "open",
                        Purge_Date: insertDate
                    })
                }
                let insertIntoDomainPurgeTable = insertIntoDomainPurge(appmanagerDbConnection,inputData);
                let insertIntoAppPurgeTable = await insertIntoAppPurge(appmanagerDbConnection,inputData);
                if(await insertIntoDomainPurgeTable)
                {   
                    appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                    let response={
                        nextStep:'Step2',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next set of instances.'          
                    }
                    return response;
                }
                else if(insertIntoAppPurgeTable)
                {
                    appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                    let response={
                        nextStep:'Step2',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next set of instances.'          
                    }
                    return response;
                }
                else{
                    appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                    let response ={
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Error Occured while Updating the table.'
                    };
                    return response;
                }
            }
        }   
    }
    catch(e){
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        console.log("error occured while inserting in the app and url purge manager table",e);
        let response= {
            nextStep:'Error',
            input:{'status':inputStatus},
            message:'Error in creating app manager database connection.'
        };
        return response;
    }
};
//insert into domain_purge_manager table
async function insertIntoDomainPurge(appmanagerDbConnection,inputData)
{
    try{
        return(
            appmanagerDbConnection(appManagerTables.domainPurgeManager)
            .del()
            .then(()=>{
                return(
                    appmanagerDbConnection(appManagerTables.domainPurgeManager)
                    .insert(inputData)
                    .then(()=>{
                         console.log("data inserted into domain_purge_manager table");
                        return true;
                    })
                    .catch((e)=>{
                        console.log("error occured while inserting in the domain_purge_manager table appmanagerDbConnection",e);
                        return false;
                    })
                )
            })
            .catch((e)=>{
                console.log("error while deleting the record in domain_purge_manager table",e);
                return false;
            })
        )
    }
    catch(e){
        console.log("error occured while inserting in the  url purge table main catch block",e);
        return false;
    }
}
//insert into app_purge_manager table
async function insertIntoAppPurge(appmanagerDbConnection,inputData){
    try{
        return(
            appmanagerDbConnection(appManagerTables.appPurgeManager)
            .del()
            .then(()=>{
                return(
                    appmanagerDbConnection(appManagerTables.appPurgeManager)
                    .insert(inputData)
                    .then(()=>{
                        console.log("data inserted into app_purge_manager table")
                        return true;
                    })
                    .catch((e)=>{
                        console.log("error occured while inserting in the app_purge_manager table",e);
                        return false;
                    })
                )
            })
            .catch((e)=>{
                console.log("error while deleting the record in app_purge_manager table",e);
                return false;
            })
        )
    }
    catch(e){
        console.log("error occured while inserting in the app purge manager table main catch block",e);
        return false;
    }
}