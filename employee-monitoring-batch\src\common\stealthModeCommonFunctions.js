// require table alias
const { ehrTables } = require('./tableAlias');

// function to update member status
async function updateMemberStatus(organizationDbConnection, status,memberId,employeeTimeZone,logInEmpId) {
    try{
        // based on member id update the member status
        return(
            organizationDbConnection(ehrTables.teamMembers)
            .update({
                Member_Status: status,
                Updated_On: employeeTimeZone,
                Updated_By: logInEmpId
            })
            .where('Member_Id', memberId)
            .then(updateStatus =>{
                return 'success';
            })
            .catch(error =>{
                console.log('Error in updateMemberStatus function .catch block.',error);
                throw 'EM0105';
            })
        );
    }
    catch(catchError){
        console.log('Error in updateMemberStatus function main catch block',catchError);
        throw 'EM0105';
    }
};

// function to update asset details
async function updateAssetsDetails(organizationDbConnection, employeeId,updateParams,assetId=null) {
    try{
        let assetQuery;
        let subQuery=organizationDbConnection(ehrTables.assetManagement).update(updateParams)
        // when asset id exist then map based on assetId else based on employeeId
        if(assetId){
            assetQuery=subQuery
            .where('Asset_Id', assetId)
        }
        else{
            assetQuery=subQuery
            .where('Employee_Id', employeeId)
        }
        return(
            assetQuery
            .then(updateDetails =>{
                return 'success';
            })
            .catch(error =>{
                console.log('Error in updateAssetsDetails function .catch block.',error);
                throw error;
            })
        );
    }
    catch(catchError){
        console.log('Error in updateAssetsDetails function main catch block',catchError);
        throw catchError;
    }
};

/**Function to insert the asset details in the asset_management table */
async function insertAssetDetails(organizationDbConnection,assetDetails){
    try{
        return(
            organizationDbConnection(ehrTables.assetManagement)
            .insert(assetDetails)
            .then((assetIdResult)=>{
                if(assetIdResult.length > 0)
                    return assetIdResult[0];
                else
                    return '';
            })
            .catch(insertAssetsError =>{
                console.log('Error while inserting the assets details in insertAssetDetails() function.',insertAssetsError);
                return '';
            })
        );
    } catch(insertAssetDetailsCatchError){
        console.log('Error in insertAssetDetails() function main catch block.', insertAssetDetailsCatchError);
        return '';
    }
};

exports.updateMemberStatus=updateMemberStatus;
exports.updateAssetsDetails=updateAssetsDetails;
exports.insertAssetDetails=insertAssetDetails;
