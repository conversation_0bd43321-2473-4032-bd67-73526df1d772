service: EMPMONITORINGBATCH # service name

plugins:
  - serverless-domain-manager
  - serverless-prune-plugin # PlugIn to maintain lambda versoning
  - serverless-offline # require plugins
  - serverless-step-functions
  - '@haftahave/serverless-ses-template' # Plug-In to deploy SES templates

provider:
  name: aws
  runtime: nodejs18.x #nodejs run time
  stage: ${opt:stage} # get current stage name
  region: ${opt:region} #region in which to be deployed
  role: ${file(../config.${self:provider.stage}.json):lambdaRole} # Assign role to the lambda functions
  vpc:
    securityGroupIds: ${file(../config.${self:provider.stage}.json):securityGroupIds}
    subnetIds: ${file(../config.${self:provider.stage}.json):subnetIds}
  logs: # enable api gateway logs
    restApi: true

custom:
  sesTemplatesConfigFile: '../ses-email-templates/index.js'
  sesTemplatesRegion: ${file(../config.${self:provider.stage}.json):sesRegion}
  customDomain:
    domainName: ${file(../config.${self:provider.stage}.json):customDomainName}
    basePath: 'employeeMonitoringbatch'
    stage: ${self:provider.stage}
    createRoute53Record: true
    endpointType: 'edge'
  prune:
    automatic: true
    number: 3

# Lambda functions
functions:
  roGraphql:
      handler: src/rohandler.graphql
      timeout: 900 # Lambda timeout
      memorySize: 4096
      events:
        - http:
            path: roGraphql
            method: post
            cors:
              origin: '*'
              headers:
                - Content-Type
                - X-Amz-Date
                - X-Api-Key
                - X-Amz-Security-Token
                - X-Amz-User-Agent
                - authorization
                - org_code
                - user_ip
                - refresh_token
                - stealth
                - partnerid
                - additional_headers
              allowCredentials: false
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      environment: # environment variables
        stageName: ${self:provider.stage}
        domainName: ${file(../config.${self:provider.stage}.json):domainName}
        source: BE
        region: ${self:provider.region}
        dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
        dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
        emailFrom: ${file(../config.${self:provider.stage}.json):emailFrom}
        webAddress: ${file(../config.${self:provider.stage}.json):webAddress}
        sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
        refreshCustomGroupStateMachineArn: ${file(../config.${self:provider.stage}.json):refreshCustomGroup}

  initiateDailySummaryStepFunction:
    handler: src/webApplication/initiateDailySummaryStepFunction.initiateDailySummaryStepFunction
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: empmonitor-daily-summary-${self:provider.stage}
          description: 'This rule is used to trigger the process to summarize the daily summary activities for all the instances'
          rate: cron(30 18 * * ? *) #12AM
          enabled: true
      - schedule:
          name: daily-summary-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to summarizate the activites for a next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 19-23 * * ? *) #12.30 to 4.30 AM
          enabled: true
          input:
            status: Open
      - schedule:
          name: daily-summary-failed-instance-${self:provider.stage}
          description: 'This rule is used to trigger the process to summarize the daily summary activities for the failed instances'
          rate: cron(45 23 * * ? *) #5.15AM
          enabled: true
          input:
            status: Failed
      - schedule:
          name: daily-summary-failed-instance-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to summarizate the  activites for the failed instances to the next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/20 0-1 * * ? *) #5.30-6.30 AM
          enabled: true
          input:
            status: Failed

      - schedule:
          name: empmonitor-daily-summary-afternoon-${self:provider.stage}
          description: 'This rule is used to trigger the process to summarize the daily summary activities for all the instances'
          rate: cron(1 9 * * ? *) #2.40PM
          enabled: true
      - schedule:
          name: daily-summary-continuous-trigger-afternoon-${self:provider.stage}
          description: 'This rule is used to summarizate the activites for a next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 9-11 * * ? *) #3.00 to 5.00 PM
          enabled: true
          input:
            status: Open
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):dailySummaryArn}

  getEmpMonitoringSubscribedInstance: #Step 1
    handler: src/stepFunction/getEmpMonitoringSubscribedInstance.getEmpMonitoringSubscribedInstance
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  processAppUrlActivitySummary: #Step 2
    handler: src/stepFunction/processAppUrlActivitySummary.processAppUrlActivitySummary
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      summarizationProcessTrigger: ${self:service}-${opt:stage}-triggerSummarizationProcess

  dailySummaryErrorState:
    handler: src/stepFunction/dailySummaryErrorState.dailySummaryErrorState
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  triggerSummarizationProcess: #Step 3
    handler: src/stepFunction/triggerSummarizationProcess.triggerSummarizationProcess
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      attendanceImportURL: ${file(../config.${self:provider.stage}.json):attendanceImportURL}

  triggerInsightsNotificationFunction:
    handler: src/webApplication/triggerInsightsNotificationFunction.triggerInsightsNotificationFunction
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: empmonitor-insights-admin-notification-${self:provider.stage}
          description: 'This rule is used to trigger the daily insights notification for all the admins based on setting configuration.'
          rate: cron(0 4 * * ? *) #9.30AM
          enabled: true
          input:
            process: admin
            schedule: 1
      - schedule:
          name: empmonitor-insights-manager-notification-${self:provider.stage}
          description: 'This rule is used to trigger the daily insights notification for all the managers based on setting configuration.'
          rate: cron(0 5 * * ? *) #10.30AM
          enabled: true
          input:
            process: manager
            schedule: 1
      - schedule:
          name: empmonitor-insights-employee-notification-${self:provider.stage}
          description: 'This rule is used to trigger the daily insights notification for all the team members based on setting configuration.'
          rate: cron(0 3 * * ? *) #8.30AM
          enabled: true
          input:
            process: employee
            schedule: 1
            
      - schedule:
          name: empmonitor-insights-admin-notification-afternoon-${self:provider.stage}
          description: 'This rule is used to trigger the daily insights notification for all the admins based on setting configuration.'
          rate: cron(45 14 * * ? *) #8.15PM
          enabled: true
          input:
            process: admin
            schedule: 2
      - schedule:
          name: empmonitor-insights-manager-notification-afternoon-${self:provider.stage}
          description: 'This rule is used to trigger the daily insights notification for all the managers based on setting configuration.'
          rate: cron(15 15 * * ? *) #8.45PM
          enabled: true
          input:
            process: manager
            schedule: 2
      - schedule:
          name: empmonitor-insights-employee-notification-afternoon-${self:provider.stage}
          description: 'This rule is used to trigger the daily insights notification for all the team members based on setting configuration.'
          rate: cron(15 14 * * ? *) #7.45PM
          enabled: true
          input:
            process: employee
            schedule: 2
     
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):insightsArn}

  getInsightsEnabledInstance: # Step1
    handler: src/stepFunction/getInsightsEnabledInstance.getInsightsEnabledInstance
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  formInsightsNotificationData: # Step2
    handler: src/stepFunction/formInsightsNotificationData.formInsightsNotificationData
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      logoBucket: ${file(../config.${self:provider.stage}.json):logoBucket}

  sendEmailInsightsNotification: # Step3
    handler: src/stepFunction/sendEmailInsightsNotification.sendEmailInsightsNotification
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      webAddress: ${file(../config.${self:provider.stage}.json):webAddress}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      emailFrom: ${file(../config.${self:provider.stage}.json):emailFrom}
      logoBucket: ${file(../config.${self:provider.stage}.json):logoBucket}
      offlineReportBucket: ${file(../config.${self:provider.stage}.json):offlineReportBucket}

  getInsightDataToManagerAndMember: # Step4
    handler: src/stepFunction/getInsightDataToManagerAndMember.getInsightDataToManagerAndMember
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  triggerDataIntegrationFunction:
    handler: src/webApplication/triggerInsightsNotificationFunction.triggerDataIntegrationFunction
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: di_daily-routine-trigger-${self:provider.stage}
          description: 'This rule is used to trigger the daily data integration routine.'
          rate: cron(30 02 * * ? *) #8 AM IST
          enabled: true
          input:
            frequency: daily
            triggerType: 3
            schedule : 1
      - schedule:
          name: di_daily-routine-morning-${self:provider.stage}
          description: 'This rule is used to trigger the daily data integration routine.'
          rate: cron(0 3 * * ? *) # 03:00 AM 
          enabled: true
          input:
            frequency: daily
            triggerType: 1
            schedule : 2
      - schedule:
          name: di_daily-routine-evening-${self:provider.stage}
          description: 'This rule is used to trigger the daily data integration routine.'
          rate: cron(15 14 * * ? *) #7.45PM
          enabled: true
          input:
            frequency: daily
            triggerType: 1
            schedule : 3
      - schedule:
          name: di_daily-routine-night-${self:provider.stage}
          description: 'This rule is used to trigger the daily data integration routine.'
          rate: cron(30 16 * * ? *) #10 PM
          enabled: true
          input:
            frequency: daily
            triggerType: 2
            schedule : 4
      
      - schedule:
          name: di_weekly-routine-${self:provider.stage}
          description: 'This rule is used to trigger the weekly data integration routine.'
          rate: cron(45 14 ? * SUN *) # Every Sun at 8.15PM
          enabled: true
          input:
            frequency: weekly
            triggerType: 1
      - schedule:
          name: di_monthly-routine-${self:provider.stage}
          description: 'This rule is used to trigger the monthly data integration routine.'
          rate: cron(15 15 1 * ? *) # 8.45PM of 1st month
          enabled: true
          input:
            frequency: monthly
            triggerType: 1
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):dataIntegrationArn}

  getRecordBasedOnFrequency: # Step1
    handler: src/stepFunction/getRecordBasedOnFrequency.getRecordBasedOnFrequency
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  pushReportInStorage: # Step2
    handler: src/stepFunction/pushReportInStorage.pushReportInStorage
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      dataIntegrationBucket: ${file(../config.${self:provider.stage}.json):dataIntegrationBucket}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}

  attendanceDataIntegration: # Step3
    handler: src/stepFunction/attendanceDataIntegration.attendanceDataIntegration
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      dataIntegrationBucket: ${file(../config.${self:provider.stage}.json):dataIntegrationBucket}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      emailFrom: ${file(../config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${self:custom.sesTemplatesRegion}
      refreshCustomGroupStateMachineArn: ${file(../config.${self:provider.stage}.json):refreshCustomGroup}

  initiateProductivitySummary:
    handler: src/webApplication/initiateDailySummaryStepFunction.initiateProductivitySummary
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: em-productivity-daily-summary-${self:provider.stage}
          description: 'This rule is used to trigger the process to summarize the daily productivity summary activities for all the instances'
          rate: cron(45 23 * * ? *) #5.15AM
          enabled: true
      - schedule:
          name: productivity-summary-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to summarizate the productivity activites for a next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 0-4 * * ? *) #5.30-9.30 AM
          enabled: true
          input:
            status: Open
      - schedule:
          name: em-productivity-afternoon-daily-summary-${self:provider.stage}
          description: 'This rule is used to trigger the process to summarize the daily productivity summary activities for all the instances'
          rate: cron(50 10 * * ? *) #5.20PM
          enabled: true
      - schedule:
          name: productivity-summary-afternoon-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to summarizate the productivity activites for a next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 11-14 * * ? *) #5.30PM-7.30PM
          enabled: true
          input:
            status: Open
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):productivitySummaryArn}

  initiateProdutivitySummary: # Step1
    handler: src/stepFunction/initiateProdutivitySummary.initiateProdutivitySummary
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  processProductivityDailySummary: #Step 2
    handler: src/stepFunction/processProductivityDailySummary.processProductivityDailySummary
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      summarizationProcessTrigger: ${self:service}-${opt:stage}-triggerProductivitySummary

  triggerProductivitySummary: #Step 3
    handler: src/stepFunction/triggerProductivitySummary.triggerProductivitySummary
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  uploadEmailTemplateImages:
    handler: src/webApplication/uploadEmailTemplateImages.uploadEmailTemplateImages
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: uploadEmailTemplateImages
          method: post
          cors: true
          authorizer:
            arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment:
      logoBucket: ${file(../config.${self:provider.stage}.json):logoBucket}
      region: ${self:provider.region}
  initiateAppUrlPurgeStepFunction:
    handler: src/webApplication/initiateAppUrlPurgeStepFunction.initiateAppUrlPurgeStepFunction
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: empmonitor-weekly-purge-${self:provider.stage}
          description: 'This rule is used to trigger the process to purge app and url for all the instances'
          # rate: cron(30 14 ? * SAT *) #8PM
          rate: cron(30 13 ? * WED *) #8PM
          enabled: true
      - schedule:
          name: weekly-purge-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to purge the activites for a next set of instances continuously at a interval of 10 minutes'
          # rate: cron(0/10 15-19 ? * SAT *) #8.30 PM to 12.30 AM
          rate: cron(0/10 13-18 ? * WED *) #8.30 PM to 12.30 AM
          enabled: true
          input:
            status: Open
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):weeklyPurgeArn}

  getEmpInstanceAppUrlPurge: #Step 1
    handler: src/stepFunction/getEmpSubscribedInstanceUpdateAppUrlPurgeTable.getEmpMonitoringSubscribedInstanceUpdateAppUrlPurge
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  urlPurge: #Step 2
    handler: src/stepFunction/urlPurge.urlPurge
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  appPurge: #Step 2
    handler: src/stepFunction/appPurge.appPurge
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  initiateMaintainWeekOff:
    handler: src/webApplication/initiateMaintainWeekOff.initiateMaintainWeekOff
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: maintain-weekoff-date-${self:provider.stage}
          description: 'This rule is used to trigger the process to update activities for all the instances in appmanagerdb'
          rate: cron(30 16 1 * ? *) # 10.00PM of 1st month
          enabled: true
      - schedule:
          name: maintain-weekoff-date-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to MaintainWeekOffdates for a next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 17-21 1 * ? *) #10:30PM to 2:30 AM of 1st month
          enabled: true
          input:
            status: Open
      - schedule:
          name: maintain-weekoff-failed-instance-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to trigger the process to MaintainWeekOffdates for the failed instances'
          rate: cron(0/10 21-22 1 * ? *) #3.30AM-4:30AM of 1st month
          enabled: true
          input:
            status: Failed
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):weekOffDateArn}

  getActiveInstances: #Step 1
    handler: src/stepFunction/getActiveInstanceUpdateWeekOffDate.getActiveInstances
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  maintainWeekOffDate: #Step 2
    handler: src/stepFunction/maintainWeekOffDate.maintainWeekOffDate
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  initiateFileTransferSummary:
    handler: src/webApplication/initiateFileTransferDailySummary.initiateFileTransferDailySummary
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: fileTransfer-daily-summary-${self:provider.stage}
          description: 'This rule is used to trigger the process to summarize the daily summary file transfer activities for all the instances'
          rate: cron(30 18 * * ? *) #12AM
          enabled: true
      - schedule:
          name: fileTransfer-summary-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to summarizate the activites for a next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 19-23 * * ? *) #12.30 to 4.30 AM
          enabled: true
          input:
            status: Open
      - schedule:
          name: daily-summary-failed-instances-${self:provider.stage}
          description: 'This rule is used to trigger the process to summarize the daily summary activities for the failed instances'
          rate: cron(45 23 * * ? *) #5.15AM
          enabled: true
          input:
            status: Failed
      - schedule:
          name: daily-summary-failed-instances-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to summarizate the  activites for the failed instances to the next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/20 16-17 * * ? *) #5.30-6.30 AM
          enabled: true
          input:
            status: Failed
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):fileTransferSummaryArn}

  getFileTransferActiveInstances: #Step 1
    handler: src/stepFunction/getFileTransferActiveInstances.getFileTransferActiveInstances
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  getFileTransferActiveEmployees: #step 2
    handler: src/stepFunction/getFileTransferActiveEmployees.getFileTransferActiveEmployees
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  processFileTransferActiveEmployee: #step 3
    handler: src/stepFunction/processFileTransferActiveEmployee.processFileTransferActiveEmployee
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  initiateStateMachineStepFunction:
    handler: src/webApplication/initiateStateMachineStepFunction.initiateStateMachineStepFunction
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: daily-employeedata-import-${self:provider.stage}
          description: 'This rule is used to trigger the process to call api pagt instances on daily basis'
          rate: cron(30 18 * * ? *) #12AM
          enabled: true
          input:
            frequency: Daily
            schedule : 1
      - schedule:
          name: weekly-onboard-status-push-to-api-trigger-${self:provider.stage}
          description: 'This rule is used to call api for pagt instances on weekly basis'
          rate: cron(30 18 ? * FRI *) #At 00:00 AM, only on Friday
          enabled: true
          input:
            frequency: Weekly
            schedule : 2
      - schedule:
          name: monthly-generate-airticket-${self:provider.stage}
          description: 'This rule is used to trigger the generate air ticket summary details'
          rate: cron(0 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22 * * ? *) # Every 1 hours
          enabled: false
          input:
            frequency: Monthly
            schedule : 3
    environment:
      stateMachineArn: ${file(../config.${self:provider.stage}.json):pagtIntegrationArn}
      airTicketStateMachineArn: ${file(../config.${self:provider.stage}.json):airTicketStateMachineArn}

  getPagtIntegration:
    handler: src/stepFunction/getPagtIntegrationDetails.getPagtIntegrationDetails
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      pagtAPIURL: ${file(../config.${self:provider.stage}.json):pagtAPIURL}
      refreshCustomGroupStateMachineArn: ${file(../config.${self:provider.stage}.json):refreshCustomGroup}

  initiateAirTicketSummary:
    handler: src/stepFunction/initiateAirTicketSummary.initiateAirTicketSummary
    memorySize: 4096
    timeout: 900
    environment:
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      processAirTicketSummary: ${file(../config.${self:provider.stage}.json):processAirTicketSummary}


stepFunctions:
  stateMachines:
    empMonitorDailySummary: # step function for daily summary process
      name: ${opt:stage}-empMonitorDailySummary
      events:
        - http:
            path: empMonitorDailySummaryStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We will be summarizing the daily activites in table for all the active instances.'
        StartAt: getEmpMonitoringSubscribedInstance
        States:
          getEmpMonitoringSubscribedInstance:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getEmpMonitoringSubscribedInstance
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: processAppUrlActivitySummary
              - Variable: '$.nextStep'
                StringEquals: Error
                Next: dailySummaryErrorState
            Default: EndFunction
          processAppUrlActivitySummary:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-processAppUrlActivitySummary
            Next: SecondStepChoiceState
          SecondStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Error
                Next: dailySummaryErrorState
            Default: EndFunction
          dailySummaryErrorState:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-dailySummaryErrorState
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Daily summary step function execution completed.'
            End: true
    dailyInsightsNotification: # step function for insights notification
      name: ${opt:stage}-dailyInsightsNotification
      events:
        - http:
            path: dailyInsightsNotification
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We need to send the email notification regarding daily insights'
        StartAt: getInsightsEnabledInstance
        States:
          getInsightsEnabledInstance:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getInsightsEnabledInstance
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: formInsightsNotificationData
            Default: EndFunction
          formInsightsNotificationData:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-formInsightsNotificationData
            Next: SecondStepChoiceState
          SecondStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step3
                Next: sendEmailInsightsNotification
              - Variable: '$.nextStep'
                StringEquals: Step4
                Next: getInsightDataToManagerAndMember
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: formInsightsNotificationData
            Default: EndFunction
          sendEmailInsightsNotification:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-sendEmailInsightsNotification
            Next: ThirdStepChoiceState
          ThirdStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: formInsightsNotificationData
              - Variable: '$.nextStep'
                StringEquals: Step4
                Next: getInsightDataToManagerAndMember
            Default: EndFunction
          getInsightDataToManagerAndMember:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getInsightDataToManagerAndMember
            Next: FourthStepChoiceState
          FourthStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step3
                Next: sendEmailInsightsNotification
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: formInsightsNotificationData
            Default: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Daily insights notification step function execution completed.'
            End: true
    dataIntegrationRoutine: # step function for data integration
      name: ${opt:stage}-dataIntegrationRoutine
      events:
        - http:
            path: dataIntegrationRoutine
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'Based on the process type either Pull or Push report data to different storage platform.'
        StartAt: getRecordBasedOnFrequency
        States:
          getRecordBasedOnFrequency:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getRecordBasedOnFrequency
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: pushReportInStorage
              - Variable: '$.nextStep'
                StringEquals: Step3
                Next: attendanceDataIntegration
            Default: EndFunction
          pushReportInStorage:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-pushReportInStorage
            Next: SecondStepChoiceState
          SecondStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step1
                Next: getRecordBasedOnFrequency
            Default: EndFunction
          attendanceDataIntegration:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-attendanceDataIntegration
            Next: ThirdStepChoiceState
          ThirdStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step1
                Next: getRecordBasedOnFrequency
            Default: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Data integration step function execution completed.'
            End: true

    emProductivitySummary: # step function to process productivity summary details
      name: ${opt:stage}-emProductivitySummary
      events:
        - http:
            path: emProductivitySummary
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We will be summarizing the daily productivity activites in table for all the active employee monitoring instances.'
        StartAt: initiateProdutivitySummary
        States:
          initiateProdutivitySummary:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-initiateProdutivitySummary
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: processProductivityDailySummary
              - Variable: '$.nextStep'
                StringEquals: Error
                Next: dailySummaryErrorState
            Default: EndFunction
          processProductivityDailySummary:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-processProductivityDailySummary
            Next: SecondStepChoiceState
          SecondStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Error
                Next: dailySummaryErrorState
            Default: EndFunction
          dailySummaryErrorState:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-dailySummaryErrorState
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Productivity daily summary step function execution completed.'
            End: true

    empMonitorWeeklyAppUrlPurge: # step function for weekly purge app and url
      name: ${opt:stage}-empMonitorWeeklyAppUrlPurge
      events:
        - http:
            path: empMonitorWeeklyAppUrlPurgeStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We will purge app and url of uncategorized not used from 3 month.'
        StartAt: getEmpInstanceAppUrlPurge
        States:
          getEmpInstanceAppUrlPurge:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getEmpInstanceAppUrlPurge
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: processAppUrlPurgeParallel
            Default: EndFunction
          processAppUrlPurgeParallel:
            Type: Parallel
            Next: EndFunction
            Branches:
              - StartAt: urlPurge
                States:
                  urlPurge:
                    Type: Task
                    Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-urlPurge
                    TimeoutSeconds: 780
                    End: true
              - StartAt: appPurge
                States:
                  appPurge:
                    Type: Task
                    Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-appPurge
                    TimeoutSeconds: 780
                    End: true
          EndFunction:
            Type: Pass
            Result: 'weekly purge step function execution completed.'
            End: true

    processMaintainWeekOffDates: # step function for maintaining weekofdates in weekoff_dates
      name: ${opt:stage}-processMaintainWeekOffDates
      events:
        - http:
            path: maintainWeekOffDatesStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We will be maintaining week of dates in weekoff_dates for every active instances.'
        StartAt: getActiveInstance
        States:
          getActiveInstance:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getActiveInstances
            Next: maintainWeekOffDate
          maintainWeekOffDate:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-maintainWeekOffDate
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'MaintainWeekOffDates step function execution completed.'
            End: true

    fileTransferSummary: # step function to process file transfer summary details
      name: ${opt:stage}-fileTransferSummary
      events:
        - http:
            path: fileTransferSummary
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We will be summarizing the daily file transfer activites in table for all the active instances.'
        StartAt: getFileTransferActiveInstances
        States:
          getFileTransferActiveInstances:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getFileTransferActiveInstances
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: getFileTransferActiveEmployees
            Default: EndFunction
          getFileTransferActiveEmployees:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getFileTransferActiveEmployees
            Next: SecondStepChoiceState
          SecondStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step3
                Next: processFileTransferActiveEmployee
            Default: EndFunction
          processFileTransferActiveEmployee:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-processFileTransferActiveEmployee
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'File Transfer daily summary step function execution completed.'
            End: true

    getPagtIntegration: # step function to process file transfer summary details
      name: ${opt:stage}-getPagtIntegration
      events:
        - http:
            path: getPagtIntegration
            method: POST
            cors: true
            authorizer:
              arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We will be summarizing the daily file transfer activites in table for all the active instances.'
        StartAt: getPagtIntegrationStart
        States:
          getPagtIntegrationStart:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-getPagtIntegration
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: getPagtIntegrationStart
            Default: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Sync employee details integration step function execution completed.'
            End: true

    initiateAirTicketSummary:
      name: ${opt:stage}-initiateAirTicketSummary
      events:
        - http:
            path: initiateAirTicketSummary
            method: POST
            cors: true
            authorizer:
              arn:  ${file(../config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate aync step function."
        StartAt: processAirTicketSummaryFunction
        States:
          processAirTicketSummaryFunction:
            Type: Task
            Resource: ${file(../config.${self:provider.stage}.json):resourceArnPrefix}-initiateAirTicketSummary
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Async step function execution completed."
            End: true
            


resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Forbidden." } }'

    GatewayResponse401: # statusCode 401
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: '401' # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Unauthorized request." } }'

    GatewayResponse5XX: # statusCode 5XX series error code
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "API gateway timeout." } }'
