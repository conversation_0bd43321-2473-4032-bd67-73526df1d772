// require moment timezone
const moment = require('moment-timezone')
// require table alias
const { ehrTables } = require('./tableAlias');
// require common function
const { getTimeInHoursMinutesSeconds,getEmpWorkScheduleBasedWeekOffAndDuration } = require('../webApplication/employeeMonitoringCommonFunction');
const { getOrganizationSubscribedPlan }=require('../common/signInCommonFunction');
const { getJobPostDetails, getJobPostHiringManager, getJobPostRecruiters, getJobPostPanelMembers, 
    getCandidateDetails, getCandidateEducation, getCandidateExperience, getCandidateCertification, getCandidateRecruitmentInfo,
    getCandidateInterview, getCandidateInterviewRound, getInterviewPanelMemberScore, getJobPostOnboardingSpecialList
} = require('./rmsEntomoExportData');

const { getPositionMaster, getHiringForeCastList, getMppDutiesResponsibilities, getMppEducationRequirementsDescriptions, 
    getMppExperienceRequirements, getMppNewPositionRequest, getMppRecruimentRequest, getMppWorkingConditions, getPositionLevel } = require('./mppExportData');

//function to get the asset report
module.exports = {
    getAssetReport:async(organizationDbConnection,args) => {
        let {source} = args;
        try{
            return(
                organizationDbConnection(ehrTables.assetManagement)
                .select('AM.MAC_Address as macAddress','AM.Serial_Number as serialNumber','AM.Asset_Type as assetType','AM.Operating_System as operatingSystem',
                'AM.Agent_Version as agentVersion','AM.Status as status','AM.Computer_Name as computerName','EJ.Emp_Email as employeeEmail','DES.Designation_Name as designation','DEP.Department_Name as department',
                'EPI.Emp_First_Name as employeeFirstName','EPI.Emp_Last_Name as employeeLastName', 'AM.User_Name as systemUserName',
                organizationDbConnection.raw('CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as employeeId'),
                organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager"))
                .from(ehrTables.assetManagement + ' as AM')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI','AM.Employee_Id','EPI.Employee_Id')
                .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI1','EJ.Manager_Id','EPI1.Employee_Id')
                .leftJoin(ehrTables.designation + ' as DES', 'EJ.Designation_Id', 'DES.Designation_Id')
                .leftJoin(ehrTables.department + ' as DEP', 'EJ.Department_Id', 'DEP.Department_Id')
                .then(async(getDetails)=>{
                    let assetDetails = getDetails;
                    // If source is dataintegration then need to form the header based on report type and include in the report
                    if (source.toLowerCase() === 'dataintegration') {
                        if (assetDetails.length > 0) {
                            let formHeader = await formReportHeader('EMAR');
                            assetDetails.splice(0, 0, formHeader);
                            return assetDetails;
                        }else{
                            return [];
                        }
                    }else{
                        //return assetDetails for report
                        return assetDetails.length > 0 ? assetDetails : [];
                    } 
                })
                .catch(catchError => {
                    console.log('Error in assetsReport function .catch block.', catchError);
                    throw (source==='dataintegration')?'Error in getting the asset details':'EM0148';
                })
                );
            }
            catch (mainCatchError){
                console.log('Error in assetReport function main catch block', mainCatchError);
                throw (source==='dataintegration')?'Error in getting the asset details':'EM0148';
            }
        },
        
        
    // function to get productivity summary report based on the input start and end date
        
    productivitySummaryReport:async(organizationDbConnection,args) =>{
        try{
            let {employeeIdsArray,startDate,endDate,source}=args;
            return (
                /** Get the employee details along with activity details. In employee activity daily summary - Total_Activity_Duration and  Productive_Activity_Duration will be hh:mm:ss
                for presentation it is converted  to minutes */
                organizationDbConnection(ehrTables.employeeActivityDailySummary)
                .select('EJ.Emp_Email as employeeEmail','EADS.Activity_Date as activityDate','EJ.Employee_Id as empId','EADS.Total_Activity_Duration as totalSystemUpTime',
                'EADS.System_Uptime_Without_Lockapp_Time as systemUpTimeWithoutLockAppTime', 'EADS.System_Uptime_Without_Lockapp_Time_In_Minutes as systemUpTimeWithoutLockAppTimeInMinutes',
                'EADS.Lockapp_Time as lockAppTime', 'EADS.Lockapp_Time_In_Minutes as lockAppTimeInMinutes',
                'EADS.Active_Duration as totalUserActiveTime', 'EADS.Productive_Activity_Duration as userActiveTimeSpentOnKeyboardAndMouse',
                'EADS.Not_Active_Duration as totalNotActiveTime', 'EADS.Idle_Duration as totalIdleTime',
                'EADS.Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours as sysProductivityPercentageBasedOnFixedDailyWorkHours',
                organizationDbConnection.raw('ROUND(EADS.Not_Active_Duration_In_Minutes) as totalNotActiveTimeInMinutes'),
                organizationDbConnection.raw('ROUND(EADS.Idle_Duration_In_Minutes) as totalIdleTimeInMinutes'),
                organizationDbConnection.raw('ROUND(EADS.Active_Duration_In_Minutes) as totalUserActiveTimeInMinutes'),
                organizationDbConnection.raw('CASE WHEN time_to_sec( EADS.Total_Activity_Duration )>0 THEN ROUND(time_to_sec( EADS.Total_Activity_Duration )/60 )  ELSE "0" END as totalSystemUpTimeInMinutes'),
                organizationDbConnection.raw('CASE WHEN time_to_sec( EADS.Productive_Activity_Duration )>0 THEN ROUND(time_to_sec( EADS.Productive_Activity_Duration )/60 )  ELSE "0" END as userActiveTimeSpentOnKeyboardAndMouseInMinutes'),
                organizationDbConnection.raw('(IF(EADS.Not_Active_Duration_In_Minutes>0, EADS.Not_Active_Duration_In_Minutes, 0)+IF(EADS.Idle_Duration_In_Minutes>0, EADS.Idle_Duration_In_Minutes, 0)) as timeSpentOnIdleAndNotActiveInMinutes'),
                organizationDbConnection.raw('TIME_FORMAT(SEC_TO_TIME((IF(time_to_sec( EADS.Not_Active_Duration)>0, time_to_sec( EADS.Not_Active_Duration), 0)+IF(time_to_sec(EADS.Idle_Duration)>0, time_to_sec(EADS.Idle_Duration), 0))),"%H:%i:%s") as timeSpentOnIdleAndNotActive'),
                organizationDbConnection.raw('(CASE WHEN (time_to_sec( EADS.Productive_Activity_Duration )>0 AND EADS.Total_Activity_Duration_In_Minutes > 0) THEN ROUND((((time_to_sec( EADS.Productive_Activity_Duration )/60 ) / EADS.Total_Activity_Duration_In_Minutes) * 100)) ELSE 0 END) as systemProductivityPercentageIncludesIdleAndNotActiveTime'),
                organizationDbConnection.raw('(CASE WHEN (time_to_sec( EADS.Productive_Activity_Duration )>0 AND EADS.Active_Duration_In_Minutes > 0) THEN ROUND((((time_to_sec( EADS.Productive_Activity_Duration )/60 ) / EADS.Active_Duration_In_Minutes) * 100)) ELSE 0 END) as systemProductivityPercentageExcludesIdleAndNotActiveTime'),
                organizationDbConnection.raw('TIME_FORMAT(TIMEDIFF(Activity_End_Hour, Activity_Start_Hour), "%H:%i:%s") AS attendanceSessionTime'),
                organizationDbConnection.raw('TIMESTAMPDIFF(MINUTE, Activity_Start_Hour, Activity_End_Hour) AS attendanceSessionTimeInMinutes'),
                'PDS.User_Productivity_Percentage_Productive_Only as userProductivityPercentageProductiveOnly', 'PDS.Productive_Duration_In_Minutes as timeSpentOnProductiveAppsAndUrlsInMinutes',
                'PDS.User_Prod_Percentage_Productive_Only_Based_On_Fixed_Hours as userProdPercentageProductiveOnlyBasedOnFixedHours',
                organizationDbConnection.raw('TIME_FORMAT(PDS.Productive_Duration_In_HHMMSS,"%H:%i:%s") as timeSpentOnProductiveAppsAndUrls'),
                'EADS.Activity_Start_Hour as productivityStartTime', 'EADS.Activity_End_Hour as productivityEndTime', 'EADS.TimeZone as timeZone',
                organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),'EJ.Work_Schedule as workScheduleId',
                organizationDbConnection.raw('CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as employeeId'),
                organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager"),
                'EPI.Emp_First_Name as employeeFirstName','EPI.Emp_Last_Name as employeeLastName')
                .from(ehrTables.employeeActivityDailySummary +' as EADS')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EADS.Employee_Id', 'EPI.Employee_Id')
                .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI1','EJ.Manager_Id','EPI1.Employee_Id')
                .leftJoin(ehrTables.appurlActivityDailySummary+' as PDS', function () {
                    this.on('EADS.Employee_Id','PDS.Employee_Id')
                    .andOn(function () {
                        this.on('EADS.Activity_Date','PDS.Activity_Date');
                    })
                })
                .whereIn('EADS.Employee_Id', employeeIdsArray)
                .whereBetween('EADS.Activity_Date',[startDate,endDate])
                .orderBy('EADS.Activity_Date')        
                .then(async(resultData) => {
                    let summaryReportDetails=resultData;
                    // check whether record exist or not
                    if(Object.keys(summaryReportDetails).length> 0){
                        return (
                            /** In app activity daily summary - Total_Activity_Duration stored in seconds , convert to minutes for presentation. */
                            organizationDbConnection(ehrTables.appActivityDailySummary+' as AADS')
                            .select('AADS.Employee_Id as empId','AADS.Activity_Date as activityDate',
                            organizationDbConnection.raw('SUM(AADS.Total_Activity_Duration) as timeSpentOnProductiveAppsInSeconds,CASE WHEN SUM(AADS.Total_Activity_Duration)>0 THEN ROUND(SUM(AADS.Total_Activity_Duration/60))  ELSE "0" END as timeSpentOnProductiveAppsInMinutes'),
                            organizationDbConnection.raw('CASE WHEN SUM(AADS.Total_Activity_Duration) THEN TIME_FORMAT(SEC_TO_TIME(SUM(AADS.Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as timeSpentOnProductiveApps'),
                            )
                            .where('AADS.Category','Productive')
                            .whereIn('AADS.Employee_Id',employeeIdsArray)
                            .whereBetween('AADS.Activity_Date',[startDate,endDate])        
                            .groupBy('AADS.Activity_Date','AADS.Employee_Id')
                            .then(async(summaryAppDetails) => {
                                if(Object.keys(summaryAppDetails).length > 0){
                                    // combine employee activity details with productive app details when activity date and employeeId are equal
                                    summaryReportDetails = summaryReportDetails.map(obj => {
                                        let data = summaryAppDetails.find(item => item.activityDate === obj.activityDate && item.empId === obj.empId);    
                                        return {...obj, ...data}
                                    });
                                }
                                // calculate the total time spent on applications
                                return (
                                    organizationDbConnection(ehrTables.appActivityDailySummary)
                                    .select('Employee_Id as empId','Activity_Date as activityDate',
                                    organizationDbConnection.raw('SUM(Total_Activity_Duration) as totalTimeSpentOnAppsInSeconds'))
                                    .whereIn('Employee_Id',employeeIdsArray)
                                    .whereBetween('Activity_Date',[startDate,endDate])        
                                    .groupBy('Employee_Id','Activity_Date')
                                    .then(async(getTotalAppDetails) => {
                                        // combine total app details with result app details when activity date and employeeId are equal
                                        summaryReportDetails = summaryReportDetails.map(obj => {
                                            let data = getTotalAppDetails.find(item => item.activityDate === obj.activityDate && item.empId === obj.empId);    
                                            return {...obj, ...data}
                                        });
                                        /** In url activity daily summary - Total_Activity_Duration stored in seconds , convert to minutes for presentation. */
                                        return (
                                            organizationDbConnection(ehrTables.urlActivityDailySummary+' as UADS')
                                            .select('UADS.Activity_Date as activityDate','UADS.Employee_Id as empId',
                                            organizationDbConnection.raw('SUM(UADS.Total_Activity_Duration) as timeSpentOnProductiveUrlsInSeconds,CASE WHEN SUM(UADS.Total_Activity_Duration)>0 THEN ROUND(SUM(UADS.Total_Activity_Duration/60))  ELSE "0" END as timeSpentOnProductiveUrlsInMinutes'),
                                            organizationDbConnection.raw('CASE WHEN SUM(UADS.Total_Activity_Duration) THEN TIME_FORMAT(SEC_TO_TIME(SUM(UADS.Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as timeSpentOnProductiveUrls'),
                                            )
                                            .where('UADS.Category','Productive')
                                            .whereIn('UADS.Employee_Id',employeeIdsArray)
                                            .whereBetween('UADS.Activity_Date',[startDate,endDate])        
                                            .groupBy('UADS.Activity_Date','UADS.Employee_Id')
                                            .then(async(summaryUrlDetails) => {
                                                if(Object.keys(summaryUrlDetails).length > 0){
                                                    summaryReportDetails = summaryReportDetails.map(obj => {
                                                        // combine 2 array of json when activity date and employeeId are equal
                                                        let data = summaryUrlDetails.find(item => item.activityDate === obj.activityDate && item.empId === obj.empId);    
                                                        return {...obj, ...data}
                                                    });
                                                }
                                                if(source.toLowerCase() === 'dataintegration'){
                                                    let configDetails = await getCustomReportHeaderConfiguration(organizationDbConnection, source.toLowerCase(),'EMPSR');
                                                    if(configDetails.length > 0){
                                                        if(summaryReportDetails.length>0){
                                                            let formHeader=await formReportHeader('EMPUSR');
                                                            summaryReportDetails.splice(0,0,formHeader);
                                                            summaryReportDetails = await formResponseBasedOnCustomConfiguration(configDetails, summaryReportDetails);
                                                        }
                                                    } else {
                                                        summaryReportDetails = [];
                                                    }
                                                }
                                                /** If data exist then get the week off details based on the work schedule */
                                                if(summaryReportDetails.length>0 && source==='uireport'){
                                                    summaryReportDetails=await getEmpWorkScheduleBasedWeekOffAndDuration(organizationDbConnection,[],summaryReportDetails,startDate, endDate);
                                                }
                                                return summaryReportDetails;
                                            })
                                        );
                                    })
                                )
                            })
                        );
                    }
                    else{
                        console.log('No activity data exists');
                        return [];
                    }
                })
                .catch(catchError => {
                    console.log('Error in productivitySummaryReport function .catch block', catchError);
                    throw (source==='dataintegration')?'Error in calculating report data.':'EM0159';
                })
            );
        } catch (mainCatchError){
            console.log('Error in productivitySummaryReport function main catch block', mainCatchError);
            throw (source==='dataintegration')?'Error in calculating report data.':'EM0159';
        }
    },
    /**
     * Function to return the productive and unproductive summary report details for the employee for the input dates
     * based on the report type
     * @param {Object} organizationDbConnection - Organization DB connection Object
     * @param {Object} args - Input params
     * @returns Returns summary report details based on the source
     * @throw Throws an error code or error JSON when an error occured
     */
    getproductiveUnproductiveSummaryReport:async(organizationDbConnection,appManagerDbConnection,args,orgCode) =>{
        let {employeeIdsArray,startDate,endDate,source}=args;
        try{

            let configDetails = await getCustomReportHeaderConfiguration(organizationDbConnection, source.toLowerCase(),'EMPUSR');
            if(configDetails.length > 0){
                /** Get the employee details along with activity details. In employee activity daily summary - Total_Activity_Duration and  Productive_Activity_Duration will be hh:mm:ss
                for presentation it is converted  to minutes */
                let summaryDataQuery=organizationDbConnection(ehrTables.employeeActivityDailySummary)
                .select('EJ.Emp_Email as employeeEmail','EADS.Activity_Date as activityDate','EJ.Employee_Id as empId','EADS.Total_Activity_Duration as totalSystemUpTime','EADS.Productive_Activity_Duration as userActiveTimeSpentOnKeyboardAndMouse',
                'EADS.System_Uptime_Without_Lockapp_Time as systemUpTimeWithoutLockAppTime', 'EADS.System_Uptime_Without_Lockapp_Time_In_Minutes as systemUpTimeWithoutLockAppTimeInMinutes',
                'EADS.Lockapp_Time as lockAppTime', 'EADS.Lockapp_Time_In_Minutes as lockAppTimeInMinutes','PDS.User_Productivity_Percentage_Productive_Only as userProductivityPercentageProductiveOnly',
                'PDS.User_Prod_Percentage_Productive_Only_Based_On_Fixed_Hours as userProdPercentageProductiveOnlyBasedOnFixedHours',
                'PDS.User_Productivity_Percentage as userProductivityPercentageProductiveAndNeutral',
                'PDS.User_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours as userProductivityPercentageBasedOnFixedDailyWorkHours',
                'PDS.Productive_Duration_In_Minutes as timeSpentOnProductiveAppsAndUrlsInMinutes',
                'PDS.Unproductive_Duration_In_Minutes as timeSpentOnUnProductiveAppsAndUrlsInMinutes',
                'PDS.Neutral_Duration_In_Minutes as timeSpentOnNeutralAppsAndUrlsInMinutes', 'EADS.Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours as sysProductivityPercentageBasedOnFixedDailyWorkHours',
                'EADS.Active_Duration as totalUserActiveTime', organizationDbConnection.raw('(CASE WHEN time_to_sec( EADS.Total_Activity_Duration )>0 THEN ROUND(time_to_sec( EADS.Total_Activity_Duration )/60 )  ELSE "0" END) as totalSystemUpTimeInMinutes'),
                organizationDbConnection.raw('ROUND(EADS.Active_Duration_In_Minutes) as totalUserActiveTimeInMinutes'),
                'EADS.Not_Active_Duration as totalNotActiveTime', 'EADS.Idle_Duration as totalIdleTime',
                organizationDbConnection.raw('ROUND(EADS.Not_Active_Duration_In_Minutes) as totalNotActiveTimeInMinutes'),
                organizationDbConnection.raw('ROUND(EADS.Idle_Duration_In_Minutes) as totalIdleTimeInMinutes'),
                organizationDbConnection.raw('(CASE WHEN time_to_sec( EADS.Productive_Activity_Duration )>0 THEN ROUND(time_to_sec( EADS.Productive_Activity_Duration )/60 )  ELSE "0" END) as userActiveTimeSpentOnKeyboardAndMouseInMinutes'),
                'EADS.Activity_Start_Hour as productivityStartTime', 'EADS.Activity_End_Hour as productivityEndTime', 'EADS.TimeZone as timeZone',
                'EPI.Emp_First_Name as employeeFirstName','EPI.Emp_Last_Name as employeeLastName','EJ.Work_Schedule as workScheduleId',
                organizationDbConnection.raw('(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END) as employeeId'),
                organizationDbConnection.raw(`(IF(EADS.Not_Active_Duration_In_Minutes>0, EADS.Not_Active_Duration_In_Minutes,0)+`+
                `IF(EADS.Idle_Duration_In_Minutes>0,EADS.Idle_Duration_In_Minutes, 0)) as timeSpentOnIdleAndNotActiveInMinutes`),
                organizationDbConnection.raw(`TIME_FORMAT(SEC_TO_TIME((IF(time_to_sec(EADS.Not_Active_Duration)>0, time_to_sec(EADS.Not_Active_Duration), 0)+`+
                `IF(time_to_sec(EADS.Idle_Duration)>0, time_to_sec(EADS.Idle_Duration), 0))),"%H:%i:%s") as timeSpentOnIdleAndNotActive`),
                organizationDbConnection.raw('(CASE WHEN (time_to_sec( EADS.Productive_Activity_Duration )>0 AND EADS.Total_Activity_Duration_In_Minutes > 0) THEN ROUND((((time_to_sec( EADS.Productive_Activity_Duration )/60 ) / EADS.Total_Activity_Duration_In_Minutes) * 100)) ELSE 0 END) as systemProductivityPercentageIncludesIdleAndNotActiveTime'),
                organizationDbConnection.raw('(CASE WHEN (time_to_sec( EADS.Productive_Activity_Duration )>0 AND EADS.Active_Duration_In_Minutes > 0) THEN ROUND((((time_to_sec( EADS.Productive_Activity_Duration )/60 ) / EADS.Active_Duration_In_Minutes) * 100)) ELSE 0 END) as systemProductivityPercentageExcludesIdleAndNotActiveTime'),
                organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager"),
                organizationDbConnection.raw('TIME_FORMAT(PDS.Productive_Duration_In_HHMMSS,"%H:%i:%s") as timeSpentOnProductiveAppsAndUrls'),
                organizationDbConnection.raw('TIME_FORMAT(PDS.UnProductive_Duration_In_HHMMSS,"%H:%i:%s") as timeSpentOnUnProductiveAppsAndUrls'),
                organizationDbConnection.raw('TIME_FORMAT(PDS.Neutral_Duration_In_HHMMSS,"%H:%i:%s") as timeSpentOnNeutralAppsAndUrls'),
                organizationDbConnection.raw('TIME_FORMAT(TIMEDIFF(Activity_End_Hour, Activity_Start_Hour), "%H:%i:%s") AS attendanceSessionTime'),
                organizationDbConnection.raw('TIMESTAMPDIFF(MINUTE, Activity_Start_Hour, Activity_End_Hour) AS attendanceSessionTimeInMinutes'))
                .from(ehrTables.employeeActivityDailySummary +' as EADS')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EADS.Employee_Id', 'EPI.Employee_Id')
                .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI1','EJ.Manager_Id','EPI1.Employee_Id')
                .leftJoin(ehrTables.appurlActivityDailySummary+' as PDS', function () {
                    this.on('EADS.Employee_Id','PDS.Employee_Id')
                    .andOn(function () {
                        this.on('EADS.Activity_Date','PDS.Activity_Date');
                    })
                })
                .whereIn('EADS.Employee_Id', employeeIdsArray) 
                .whereBetween('EADS.Activity_Date',[startDate,endDate])
                .orderBy('EADS.Activity_Date')
                // check the dashboard type for the orgcode
                let dashboardType=await getOrganizationSubscribedPlan(appManagerDbConnection,orgCode);
                // if the dashboard type is hrms then get the designation and department details
                if(dashboardType && dashboardType.toLowerCase()==='hrmsdashboard'){
                    summaryDataQuery=summaryDataQuery
                    .select('DES.Designation_Name as designation','DEP.Department_Name as department')
                    .leftJoin(ehrTables.designation + ' as DES', 'EJ.Designation_Id', 'DES.Designation_Id')
                    .leftJoin(ehrTables.department + ' as DEP', 'EJ.Department_Id', 'DEP.Department_Id')    
                }

                return (
                    await summaryDataQuery
                    .then(async(resultData) => {
                        let productivityUnproductivityDetails=resultData;
                        //Check whether record exist or not
                        if(Object.keys(productivityUnproductivityDetails).length> 0){
                            
                                        /**Get the app duration details for the productivity application ids for the employee for the activity date.
                                         * In app activity daily summary - Total_Activity_Duration stored in seconds , convert to minutes for presentation. */
                                        return(
                                        organizationDbConnection(ehrTables.appActivityDailySummary+' as AADS')
                                        .select('AADS.Employee_Id as empId','AADS.Activity_Date as activityDate', 'AADS.Category as category',
                                        organizationDbConnection.raw('CASE WHEN SUM(AADS.Total_Activity_Duration)>0 THEN ROUND(SUM(AADS.Total_Activity_Duration/60))  ELSE "0" END as timeSpentOnAppsInMinutes'),
                                        organizationDbConnection.raw('CASE WHEN SUM(AADS.Total_Activity_Duration) THEN TIME_FORMAT(SEC_TO_TIME(SUM(AADS.Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as timeSpentOnApps'),
                                        )
                                        // .where('AADS.Category','Productive')
                                        .whereIn('AADS.Employee_Id',employeeIdsArray)
                                        .whereBetween('AADS.Activity_Date',[startDate,endDate])        
                                        .groupBy('AADS.Activity_Date','AADS.Employee_Id', 'AADS.Category')
                                        .then(async(summaryAppDetails) => {
                                                    return (
                                                        organizationDbConnection(ehrTables.urlActivityDailySummary+' as UADS')
                                                        .select('UADS.Activity_Date as activityDate','UADS.Employee_Id as empId', 'UADS.Category as category',
                                                        organizationDbConnection.raw('SUM(UADS.Total_Activity_Duration) as timeSpentOnUrlsInSeconds,CASE WHEN SUM(UADS.Total_Activity_Duration)>0 THEN ROUND(SUM(UADS.Total_Activity_Duration/60))  ELSE "0" END as timeSpentOnUrlsInMinutes'),
                                                        organizationDbConnection.raw('CASE WHEN SUM(UADS.Total_Activity_Duration) THEN TIME_FORMAT(SEC_TO_TIME(SUM(UADS.Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as timeSpentOnUrls'),
                                                        )
                                                        .whereIn('UADS.Employee_Id',employeeIdsArray)
                                                        .whereBetween('UADS.Activity_Date',[startDate,endDate])        
                                                        .groupBy('UADS.Activity_Date','UADS.Employee_Id', 'UADS.Category')
                                                        .then(async(summaryUrlDetails) => {
                                                            //Get the app total duration details for the employee for the activity date
                                                            productivityUnproductivityDetails = productivityUnproductivityDetails.filter(function(item) {
                                                                
                                                                item.timeSpentOnProductiveAppsInMinutes = 0;
                                                                item.timeSpentOnProductiveApps = '00:00:00';
                                                                item.timeSpentOnUnProductiveAppsInMinutes = 0;
                                                                item.timeSpentOnUnProductiveApps = '00:00:00';
                                                                item.timeSpentOnNeutralAppsInMinutes = 0;
                                                                item.timeSpentOnNeutralApps = '00:00:00';

                                                                if(Object.keys(summaryAppDetails).length > 0){
                                                                    for (const summaryAppRecord of summaryAppDetails) {
                                                                        // return (item.empId == summaryAppRecord.empId && item.activityDate == summaryAppRecord.activityDate);
                                                                        if(item.empId == summaryAppRecord.empId && item.activityDate == summaryAppRecord.activityDate && summaryAppRecord.category == 'Productive'){
                                                                            item.timeSpentOnProductiveAppsInMinutes =  summaryAppRecord.timeSpentOnAppsInMinutes;
                                                                            item.timeSpentOnProductiveApps = summaryAppRecord.timeSpentOnApps;
                                                                        } 
                                                                        if(item.empId == summaryAppRecord.empId && item.activityDate == summaryAppRecord.activityDate && summaryAppRecord.category == 'Unproductive'){
                                                                            item.timeSpentOnUnProductiveAppsInMinutes =  summaryAppRecord.timeSpentOnAppsInMinutes;
                                                                            item.timeSpentOnUnProductiveApps = summaryAppRecord.timeSpentOnApps;
                                                                        }
                                                                        if(item.empId == summaryAppRecord.empId && item.activityDate == summaryAppRecord.activityDate && summaryAppRecord.category == 'Uncategorized'){
                                                                            item.timeSpentOnNeutralAppsInMinutes =  summaryAppRecord.timeSpentOnAppsInMinutes;
                                                                            item.timeSpentOnNeutralApps = summaryAppRecord.timeSpentOnApps;
                                                                        }
                                                                    }
                                                                    
                                                                }
                                                                item.timeSpentOnProductiveUrlsInMinutes = 0;
                                                                item.timeSpentOnProductiveUrls =  '00:00:00';
                                                                item.timeSpentOnUnProductiveUrlsInMinutes = 0;
                                                                item.timeSpentOnUnProductiveUrls =  '00:00:00';
                                                                item.timeSpentOnNeutralUrlsInMinutes = 0;
                                                                item.timeSpentOnNeutralUrls =  '00:00:00'; 
                                                                
                                                                if(Object.keys(summaryUrlDetails).length > 0){

                                                                    for (const summaryUrlRecord of summaryUrlDetails) {
                                                                        // return (item.empId == summaryUrlDetails.empId && item.activityDate == summaryUrlDetails.activityDate);
                                                                        if(item.empId == summaryUrlRecord.empId && item.activityDate == summaryUrlRecord.activityDate && summaryUrlRecord.category == 'Productive'){
                                                                            item.timeSpentOnProductiveUrlsInMinutes =  summaryUrlRecord.timeSpentOnUrlsInMinutes;
                                                                            item.timeSpentOnProductiveUrls = summaryUrlRecord.timeSpentOnUrls;
                                                                        } 
                                                                        if(item.empId == summaryUrlRecord.empId && item.activityDate == summaryUrlRecord.activityDate && summaryUrlRecord.category == 'Unproductive'){
                                                                            item.timeSpentOnUnProductiveUrlsInMinutes =  summaryUrlRecord.timeSpentOnUrlsInMinutes;
                                                                            item.timeSpentOnUnProductiveUrls = summaryUrlRecord.timeSpentOnUrls;
                                                                        } 
                                                                        if(item.empId == summaryUrlRecord.empId && item.activityDate == summaryUrlRecord.activityDate && summaryUrlRecord.category == 'Uncategorized'){
                                                                            item.timeSpentOnNeutralUrlsInMinutes =  summaryUrlRecord.timeSpentOnUrlsInMinutes;
                                                                            item.timeSpentOnNeutralUrls = summaryUrlRecord.timeSpentOnUrls;
                                                                        } 
                                                                    }
                                                                }
                                                                return {...item}
                                                            });

                                                            // If souce is dataintegration then need to form the header based on report type and include in the report
                                                            if(source.toLowerCase() === 'dataintegration' || source.toLowerCase() === 'insights'){
                                                                if(productivityUnproductivityDetails.length>0){
                                                                    let formHeader=await formReportHeader('EMPUSR','',dashboardType);
                                                                    productivityUnproductivityDetails.splice(0,0,formHeader);
                                                                    productivityUnproductivityDetails = await formResponseBasedOnCustomConfiguration(configDetails, productivityUnproductivityDetails);
                                                                }
                                                            }
                                                            /** If data exist then get the week off details based on the work schedule */
                                                            if(productivityUnproductivityDetails.length>0 && source==='uireport'){
                                                                productivityUnproductivityDetails=await getEmpWorkScheduleBasedWeekOffAndDuration(organizationDbConnection,[],productivityUnproductivityDetails,startDate, endDate);
                                                            }
                                                            return productivityUnproductivityDetails;
                                                        })
                                                    );
                                        })
                                        )
                                    
                        }
                        else{
                            console.log('No activity data exists');
                            return [];
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in getproductiveUnproductiveSummaryReport() function .catch block', catchError);
                        throw (source==='dataintegration')?'Error in calculating report data.':'EM0177';
                    })
                );
            } else {
                console.log('The custom report headers are not configured or not enabled');
                return [];
            }
        }catch(productiveUnproductiveMainError){
            console.log('Error in getproductiveUnproductiveSummaryReport() function main catch block.', productiveUnproductiveMainError);
            throw (source==='dataintegration')?'Error in calculating report data.':'EM0177';
        }
    },
    /**
     * Function to return the consolidated app and urls report details for the employee for the input dates
     * based on the report type
     * @param {Object} organizationDbConnection - Organization DB connection Object
     * @param {Object} args - Input params
     * @returns Returns summary report details based on the source
     * @throw Throws an error code or error JSON when an error occured
     */
    getConsolidatedAppsUrlsReport:async(organizationDbConnection,args) =>{
        try{
            let {employeeIdsArray,startDate,endDate,source}=args;
            return(
                organizationDbConnection(ehrTables.appActivityDailySummary)
                .select('EJ.Employee_Id as empId','EJ.Emp_Email as employeeEmail','DES.Designation_Name as designation','DEP.Department_Name as department',
                'EPI.Emp_First_Name as employeeFirstName','EPI.Emp_Last_Name as employeeLastName','EJ.Work_Schedule as workScheduleId',
                organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),
                organizationDbConnection.raw('CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as employeeId'),
                organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager"))
                .from(ehrTables.empPersonalInfo +' as EPI')
                .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI1','EJ.Manager_Id','EPI1.Employee_Id')
                .leftJoin(ehrTables.designation + ' as DES', 'EJ.Designation_Id', 'DES.Designation_Id')
                .leftJoin(ehrTables.department + ' as DEP', 'EJ.Department_Id', 'DEP.Department_Id')
                .whereIn('EPI.Employee_Id', employeeIdsArray)
                .then(async(getData) => {
                    let personalInfo=getData;
                    // get the app summary data for the given date along with the employee details
                    return(
                        organizationDbConnection(ehrTables.appActivityDailySummary)
                        .select('ADS.Employee_Id as empId','ADS.Application_Name as applicationOrUrl','ADS.Activity_Date as activityDate','ADS.Category as category',
                        organizationDbConnection.raw('CASE WHEN (ADS.Total_Activity_Duration>0) THEN TIME_FORMAT(SEC_TO_TIME(ADS.Total_Activity_Duration),"%H:%i:%s")  ELSE "00:00:00" END as totalTimeWorked'),
                        organizationDbConnection.raw('CASE WHEN (ADS.Total_Activity_Duration)>0 THEN ROUND(ADS.Total_Activity_Duration/60) ELSE "0" END as totalTimeWorkedInMinutes'))
                        .whereBetween('ADS.Activity_Date',[startDate,endDate])
                        .from(ehrTables.appActivityDailySummary +' as ADS')
                        .whereIn('ADS.Employee_Id', employeeIdsArray)
                        .orderBy('ADS.Activity_Date')
                        .then(async(appDetails) => {
                            // get the url/domain summary data for the given date along with the employee details
                            return(
                                organizationDbConnection(ehrTables.urlActivityDailySummary)
                                .select('UDS.Employee_Id as empId','UDS.Domain_Name as applicationOrUrl','UDS.Activity_Date as activityDate','UDS.Category as category',
                                organizationDbConnection.raw('CASE WHEN (UDS.Total_Activity_Duration>0) THEN TIME_FORMAT(SEC_TO_TIME(UDS.Total_Activity_Duration),"%H:%i:%s")  ELSE "00:00:00" END as totalTimeWorked'),
                                organizationDbConnection.raw('CASE WHEN (UDS.Total_Activity_Duration)>0 THEN ROUND(UDS.Total_Activity_Duration/60) ELSE "0" END as totalTimeWorkedInMinutes'))
                                .whereBetween('UDS.Activity_Date',[startDate,endDate])
                                .from(ehrTables.urlActivityDailySummary +' as UDS')
                                .whereIn('UDS.Employee_Id', employeeIdsArray)
                                .orderBy('UDS.Activity_Date')
                                .then(async(urlDetails) => {
                                    let consolidatedReportDetails = [];
                                    //Include the recordType type column in the app details and URL details array only when the array exist
                                    let appDetailsResponse = appDetails.map(v => ({...v, recordType: 'Application'}));
                                    let urlDetailsResponse = urlDetails.map(v => ({...v, recordType: 'URL'}));
                                    //If both app and URL details exist
                                    if(appDetails.length > 0 && urlDetails.length > 0){
                                        consolidatedReportDetails = appDetailsResponse.concat(urlDetailsResponse);
                                    }
                                    //If app details exist
                                    else if(appDetails.length > 0){
                                        consolidatedReportDetails = appDetailsResponse;
                                    }
                                    //If URL details exist
                                    else if(urlDetails.length > 0){
                                        consolidatedReportDetails = urlDetailsResponse;
                                    }
                                    // combine the appUrl details with the personal details
                                    consolidatedReportDetails = consolidatedReportDetails.map(obj => {
                                        let data = personalInfo.find(item => item.empId === obj.empId);    
                                        return {...obj, ...data}
                                    });
                                    // If souce is dataintegration then need to form the header based on report type and include in the report
                                    if(source.toLowerCase() === 'dataintegration'){
                                        if(consolidatedReportDetails.length>0){
                                            let formHeader=await formReportHeader('EMCAUR');
                                            consolidatedReportDetails.splice(0,0,formHeader);
                                            // remove unwanted fields in report
                                            consolidatedReportDetails.forEach(element=>delete element.empId);
                                            consolidatedReportDetails.forEach(element=>delete element.workScheduleId);
                                        }
                                    }
                                    /** If data exist then get the week off details based on the work schedule */
                                    if(consolidatedReportDetails.length>0 && source==='uireport'){
                                        consolidatedReportDetails=await getEmpWorkScheduleBasedWeekOffAndDuration(organizationDbConnection,[],consolidatedReportDetails,startDate, endDate);
                                    }
                                    return consolidatedReportDetails;
                                })
                            );
                        })
                    );
                })
                .catch(catchError => {
                    console.log('Error in getConsolidatedAppsUrlsReport() function .catch block', catchError);
                    throw (source==='dataintegration')?'Error in calculating report data.':'EM0178';
                })
            )
        }catch(mainCatchError){
            console.log('Error in getConsolidatedAppsUrlsReport function main catch block', mainCatchError);
            throw (source==='dataintegration')?'Error in calculating report data.':'EM0178';
        }
    },

    getAppsReport:async(organizationDbConnection, appManagerDbConnection, args) => {
        try{
            let { employeeIdsArray, startDate, endDate, groupByTitle, source, reportType, orgCode} = args;
            let configDetails = await getCustomReportHeaderConfiguration(organizationDbConnection, source.toLowerCase(),reportType);
            if (configDetails.length){
                let subQuery;
                let appActivityDailySummary=ehrTables.appActivityDailySummary;
                if(groupByTitle)
                {
                    appActivityDailySummary=ehrTables.appTitleActivityDailySummary;
                    subQuery=organizationDbConnection(appActivityDailySummary)
                    .select('EJ.Emp_Email as employeeEmail','DES.Designation_Name as designation','DEP.Department_Name as department',
                    'ADS.Application_Name as application','ADS.Application_Title as applicationTitle','ADS.Activity_Date as activityDate','ADS.Category as category','EJ.Work_Schedule as workScheduleId',
                    organizationDbConnection.raw('CASE WHEN (ADS.Total_Activity_Duration>0) THEN TIME_FORMAT(SEC_TO_TIME(ADS.Total_Activity_Duration),"%H:%i:%s")  ELSE "00:00:00" END as totalTimeWorked'),
                    organizationDbConnection.raw('CASE WHEN (ADS.Total_Activity_Duration)>0 THEN ROUND(ADS.Total_Activity_Duration/60) ELSE "0" END as totalTimeWorkedInMinutes'),
                    organizationDbConnection.raw('CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as employeeId'),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager"),
                    'EPI.Emp_First_Name as employeeFirstName','EPI.Emp_Last_Name as employeeLastName')
                }
                else{
                    subQuery=organizationDbConnection(appActivityDailySummary)
                    .select('EJ.Emp_Email as employeeEmail','DES.Designation_Name as designation','DEP.Department_Name as department',
                    'ADS.Application_Name as application','ADS.Activity_Date as activityDate','ADS.Category as category','EJ.Work_Schedule as workScheduleId',
                    organizationDbConnection.raw('CASE WHEN (ADS.Total_Activity_Duration>0) THEN TIME_FORMAT(SEC_TO_TIME(ADS.Total_Activity_Duration),"%H:%i:%s")  ELSE "00:00:00" END as totalTimeWorked'),
                    organizationDbConnection.raw('CASE WHEN (ADS.Total_Activity_Duration)>0 THEN ROUND(ADS.Total_Activity_Duration/60) ELSE "0" END as totalTimeWorkedInMinutes'),
                    organizationDbConnection.raw('CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as employeeId'),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager"),
                    'EPI.Emp_First_Name as employeeFirstName','EPI.Emp_Last_Name as employeeLastName')
                }
                // get the app summary data for the given date along with the employee details
                return(
                    subQuery
                    .whereBetween('ADS.Activity_Date',[startDate,endDate])
                    .from(appActivityDailySummary +' as ADS')
                    .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'ADS.Employee_Id', 'EPI.Employee_Id')
                    .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
                    .leftJoin(ehrTables.empPersonalInfo + ' as EPI1','EJ.Manager_Id','EPI1.Employee_Id')
                    .leftJoin(ehrTables.designation + ' as DES', 'EJ.Designation_Id', 'DES.Designation_Id')
                    .leftJoin(ehrTables.department + ' as DEP', 'EJ.Department_Id', 'DEP.Department_Id')
                    .whereIn('ADS.Employee_Id', employeeIdsArray)
                    .orderBy('ADS.Activity_Date')
                    .then(async(appDetails) => {
                        // If souce is dataintegration then need to form the header based on report type and include in the report
                        if(source.toLowerCase() === 'dataintegration' || source.toLowerCase() === 'insights'){
                            if(appDetails.length>0){
                                let dashboardType=await getOrganizationSubscribedPlan(appManagerDbConnection,orgCode);
                                let formHeader=await formReportHeader('EMASR','',dashboardType);
                                appDetails.splice(0,0,formHeader);
                                
                                appDetails = await formResponseBasedOnCustomConfiguration(configDetails, appDetails);
                            }
                        }
                        return appDetails;
                    })
                    .catch(catchError => {
                        console.log('Error in getAppsReport .catch block', catchError);
                        throw "EM0141";
                    })
                );
            } else {
                console.log('The custom report headers are not configured or not enabled');
                return [];
            }
        } catch(err){
            console.log('Error in getAppsReport main catch block', err);
            throw "EM0141";
        }

    },

    getUrlsReport:async(organizationDbConnection, appManagerDbConnection, args) => {
        try{
            let { employeeIdsArray, startDate, endDate, source, orgCode} = args;
            let configDetails = await getCustomReportHeaderConfiguration(organizationDbConnection, source.toLowerCase(),'EMUSR');
            if (configDetails.length > 0){                
                // get the url/domain summary data for the given date along with the employee details
                return(
                    organizationDbConnection(ehrTables.urlActivityDailySummary)
                    .select('EJ.Emp_Email as employeeEmail','DES.Designation_Name as designation','DEP.Department_Name as department','UDS.Category as category',
                    'UDS.Domain_Name as domain','UDS.Activity_Date as activityDate','EPI.Emp_First_Name as employeeFirstName','EPI.Emp_Last_Name as employeeLastName','EJ.Work_Schedule as workScheduleId',
                    organizationDbConnection.raw('CASE WHEN (UDS.Total_Activity_Duration>0) THEN TIME_FORMAT(SEC_TO_TIME(UDS.Total_Activity_Duration),"%H:%i:%s")  ELSE "00:00:00" END as totalTimeWorked'),
                    organizationDbConnection.raw('CASE WHEN (UDS.Total_Activity_Duration)>0 THEN ROUND(UDS.Total_Activity_Duration/60) ELSE "0" END as totalTimeWorkedInMinutes'),
                    organizationDbConnection.raw('CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as employeeId'),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as manager"))
                    .whereBetween('UDS.Activity_Date',[startDate,endDate])
                    .from(ehrTables.urlActivityDailySummary +' as UDS')
                    .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'UDS.Employee_Id', 'EPI.Employee_Id')
                    .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
                    .leftJoin(ehrTables.empPersonalInfo + ' as EPI1','EJ.Manager_Id','EPI1.Employee_Id')
                    .leftJoin(ehrTables.designation + ' as DES', 'EJ.Designation_Id', 'DES.Designation_Id')
                    .leftJoin(ehrTables.department + ' as DEP', 'EJ.Department_Id', 'DEP.Department_Id')
                    .whereIn('UDS.Employee_Id', employeeIdsArray)
                    .orderBy('UDS.Activity_Date')
                    .then(async(getUrlDetails) => {
                        // If souce is dataintegration then need to form the header based on report type and include in the report
                        if(source.toLowerCase() === 'dataintegration' || source.toLowerCase() === 'insights'){
                            if(getUrlDetails.length>0){
                                let dashboardType=await getOrganizationSubscribedPlan(appManagerDbConnection,orgCode);
                                let formHeader=await formReportHeader('EMUSR','',dashboardType);
                                getUrlDetails.splice(0,0,formHeader);
                                
                                getUrlDetails = await formResponseBasedOnCustomConfiguration(configDetails, getUrlDetails);
                            }
                        }
                        return getUrlDetails;
                    })
                    .catch(catchError => {
                        console.log('Error in urlsReport function .catch block.', catchError);
                        throw 'EM0142';
                    })
                );
            } else {
                console.log('The custom report headers are not configured or not enabled');
                return [];
            }
        } catch(err){
            console.log('Error in getUrlsReport main catch block', err);
            throw 'EM0142';
        }
    },


    /**
     * Function to return the newly added/ updated employee details
     * based on the report type
     * @param {Object} organizationDbConnection - Organization DB connection Object
     * @param reportType - report type either HREAR / HREUR
     * @param startDate - start date of the report
     * @param endDate - end date of the report
     * @action - Either Add / Update
     * @returns Returns employee report details based on the action
     * @throw Throws an error message when an error occured
     */
     getEmployeesReport:async(organizationDbConnection, reportType, startDate, endDate, action) =>{
        try{
            let employeeReportQryFields = ['EJ.User_Defined_EmpId as Employee_Id','EPI.Emp_First_Name','EPI.Emp_Middle_Name',
                'EPI.Emp_Last_Name','SP.Service_Provider_Name','LOC.Location_Name','DEP.Department_Name','DES.Designation_Name',
                'EJ.Date_Of_Join','EPI.Personal_Email','EJ.Emp_Email','CD.Mobile_No','CD.Work_No',
                'EPI.Aadhaar_Card_Number','EPI.PAN','EPI.UAN','EI.Policy_No as ESI_Number','CD.pApartment_Name',
                'CD.pStreet_Name','CD.pCity','CD.pState','C.Country_Name as pCountry','CD.pPincode','CD.cApartment_Name',
                'CD.cStreet_Name','CD.cCity','CD.cState','country.Country_Name as cCountry','CD.cPincode'];
            return(
                organizationDbConnection(ehrTables.orgDetails)
                .select('Use_Global_Resource_Id')
                .then(orgDetails =>{
                    if(orgDetails.length){
                        let useGlobalResourceId = orgDetails[0].Use_Global_Resource_Id.toLowerCase();
                        /** If the Use_Global_Resource_Id is yes then get the Global_Resource_Id*/
                        if(useGlobalResourceId === 'yes'){
                            employeeReportQryFields.push('EJ.Global_Resource_Id');
                        } 
                        return(
                            organizationDbConnection(ehrTables.empPersonalInfo)
                            .select(employeeReportQryFields)
                            .from(ehrTables.empPersonalInfo+' as EPI')
                            .innerJoin(ehrTables.employeeInfoTimestampLog+' as EITL','EPI.Employee_Id','EITL.Employee_Id')
                            .leftJoin(ehrTables.empJob+' as EJ','EPI.Employee_Id', 'EJ.Employee_Id')
                            .leftJoin(ehrTables.designation+' as DES','EJ.Designation_Id','DES.Designation_Id')
                            .leftJoin(ehrTables.department+' as DEP','EJ.Department_Id','DEP.Department_Id')
                            .leftJoin(ehrTables.location+' as LOC','EJ.Location_Id','LOC.Location_Id')
                            .leftJoin(ehrTables.contactDetails+' as CD','EPI.Employee_Id','CD.Employee_Id')
                            .leftJoin(ehrTables.country+' as C','C.Country_Code','CD.pCountry')
                            .leftJoin(ehrTables.country+' as country','country.Country_Code','CD.cCountry')
                            .leftJoin(ehrTables.empInsurancePolicyNo+' as EI','EI.Employee_Id','EPI.Employee_Id')
                            .leftJoin(ehrTables.insuranceType + ' as IT',function() {
                                this.on('IT.InsuranceType_Id', '=', 'EI.InsuranceType_Id')
                                    .on('IT.Employee_State_Insurance', 1)
                            })
                            .leftJoin(ehrTables.serviceProvider+' as SP','EJ.Service_Provider_Id','SP.Service_Provider_Id')
                            .where('EPI.Form_Status',1)
                            .where('EJ.Emp_Status','Active')
                            .where('EITL.Action',action)
                            .whereBetween(organizationDbConnection.raw('date(EITL.Log_Timestamp)'),[startDate,endDate])        
                            .groupBy(organizationDbConnection.raw('date(EITL.Log_Timestamp), EITL.Employee_Id'))
                            .then(async(employeeDetails) => {
                                if(employeeDetails.length>0){
                                    let formHeader=await formReportHeader(reportType,useGlobalResourceId);
                                    employeeDetails.splice(0,0,formHeader);
                                }
                                return employeeDetails;
                            })
                            .catch(catchError => {
                                console.log('Error in getEmployeesReport() function .catch block', catchError);
                                throw 'Error in retrieving the '+action+' employee report details';
                            })
                        );
                    }else{
                        console.log('Organization details does not exists');
                        throw 'Organization details does not exists';
                    }
                })
            )
        }catch(mainCatchError){
            console.log('Error in getEmployeesReport function main catch block', mainCatchError);
            throw 'Error in getting the '+action+' employee report details';
        }
    },
    /**
     * Function to return the newly added leave details
     * based on the report type
     * @param {Object} organizationDbConnection - Organization DB connection Object
     * @param reportType - report type should be HRLR
     * @param startDate - start date of the report
     * @param endDate - end date of the report
     * @returns Returns leave report details
     * @throw Throws an error message when an error occured
     */
    getEmployeeLeaveReport:async(organizationDbConnection, reportType, startDate, endDate) =>{
        try{
            let leaveReportQryFields = ['EJ.User_Defined_EmpId as Employee_Id','EPI.Emp_First_Name','EPI.Emp_Middle_Name',
                'EPI.Emp_Last_Name','LT.Leave_Name','EL.Duration','EL.Start_Date','EL.End_Date','EL.Total_Days','EL.Approval_Status',
                'EPIA.Emp_First_Name as Approver_First_Name','EPIA.Emp_Middle_Name as Approver_Middle_Name','EPIA.Emp_Last_Name as Approver_Last_Name'];

            return(
                organizationDbConnection(ehrTables.orgDetails)
                .select('Use_Global_Resource_Id')
                .then(orgDetails =>{
                    if(orgDetails.length){
                        let useGlobalResourceId = orgDetails[0].Use_Global_Resource_Id.toLowerCase();
                        /** If the Use_Global_Resource_Id is yes then get the Global_Resource_Id*/
                        if(useGlobalResourceId === 'yes'){
                            leaveReportQryFields.push('EJ.Global_Resource_Id');
                        } 
                        return(
                            organizationDbConnection(ehrTables.empPersonalInfo)
                            .select(leaveReportQryFields)
                            .from(ehrTables.empPersonalInfo+' as EPI')
                            .innerJoin(ehrTables.empJob+' as EJ','EPI.Employee_Id', 'EJ.Employee_Id')
                            .innerJoin(ehrTables.empLeaves+' as EL','EPI.Employee_Id','EL.Employee_Id')
                            .innerJoin(ehrTables.leavetype+' as LT','EL.LeaveType_Id','LT.LeaveType_Id')
                            .innerJoin(ehrTables.empPersonalInfo+' as EPIA','EL.Approved_By', 'EPIA.Employee_Id')
                            .where('EPI.Form_Status',1)
                            .where('EL.Approved_By','>',0)
                            .whereIn('EL.Approval_Status',['Approved','Cancelled'])
                            .whereBetween(organizationDbConnection.raw('date(EL.Approved_On)'),[startDate,endDate])        
                            .then(async(leaveDetails) => {
                                if(leaveDetails.length>0){
                                    let formHeader=await formReportHeader(reportType,useGlobalResourceId);
                                    leaveDetails.splice(0,0,formHeader);
                                }
                                return leaveDetails;
                            })
                            .catch(catchError => {
                                console.log('Error in getEmployeeLeaveReport() function .catch block', catchError);
                                throw 'Error in retrieving the leave report details';
                            })
                        );
                    }else{
                        console.log('Organization details does not exists');
                        throw 'Organization details does not exists';
                    }
                })
            )
        }catch(mainCatchError){
            console.log('Error in getEmployeeLeaveReport function main catch block', mainCatchError);
            throw 'Error in getting the leave report details';
        }
    },


    /**
     * Function to return the newly added resignation details
     * based on the report type
     * @param {Object} organizationDbConnection - Organization DB connection Object
     * @param reportType - report type should be HRRR
     * @param startDate - start date of the report
     * @param endDate - end date of the report
     * @returns Returns resignation report details
     * @throw Throws an error message when an error occured
     */
     getEmployeeResignationReport:async(organizationDbConnection, reportType, startDate, endDate) =>{
        try{
            let resignationReportQryFields = ['EJ.User_Defined_EmpId as Employee_Id','EPI.Emp_First_Name','EPI.Emp_Middle_Name',
                'EPI.Emp_Last_Name','ER.Notice_Date','ER.Resignation_Date'];

            return(
                organizationDbConnection(ehrTables.orgDetails)
                .select('Use_Global_Resource_Id')
                .then(orgDetails =>{
                    if(orgDetails.length){
                        let useGlobalResourceId = orgDetails[0].Use_Global_Resource_Id.toLowerCase();
                        /** If the Use_Global_Resource_Id is yes then get the Global_Resource_Id*/
                        if(useGlobalResourceId === 'yes'){
                            resignationReportQryFields.push('EJ.Global_Resource_Id');
                        } 
                        return(
                            organizationDbConnection(ehrTables.empPersonalInfo)
                            .select(resignationReportQryFields)
                            .from(ehrTables.empPersonalInfo+' as EPI')
                            .innerJoin(ehrTables.empJob+' as EJ','EPI.Employee_Id', 'EJ.Employee_Id')
                            .innerJoin(ehrTables.empResignation+' as ER','EPI.Employee_Id','ER.Employee_Id')
                            .innerJoin(ehrTables.employeeInfoTimestampLog +" as EITL","EITL.Employee_Id","EJ.Employee_Id")
                            .where('EPI.Form_Status',1)
                            .where('ER.Approval_Status','Approved')
                            .where('EITL.Action','InActive')
                            .having(organizationDbConnection.raw("MAX(DATE(EITL.Log_Timestamp))"),'>=',startDate)
                            .having(organizationDbConnection.raw("MAX(DATE(EITL.Log_Timestamp))"),'<=',endDate)
                            .groupBy('EJ.Employee_Id')        
                            .then(async(resignationDetails) => {
                                if(resignationDetails.length>0){
                                    let formHeader=await formReportHeader(reportType,useGlobalResourceId);
                                    resignationDetails.splice(0,0,formHeader);
                                }
                                return resignationDetails;
                            })
                            .catch(catchError => {
                                console.log('Error in getEmployeeResignationReport() function .catch block', catchError);
                                throw 'Error in retrieving the resignation report details';
                            })
                        );
                    }else{
                        console.log('Organization details does not exists');
                        throw 'Organization details does not exists';
                    }
                })
            )
        }catch(mainCatchError){
            console.log('Error in getEmployeeResignationReport function main catch block', mainCatchError);
            throw 'Error in getting the resignation report details';
        }
    },

    rmsDataExportEntomoDashboard: async(organizationDbConnection, reportType, startDate, endDate) =>{

        try {
             console.log('Inside rmsDataExportEntomoDashboard function');
            // Get all job-post, candidate and interview details record
            const [jobPostDetails, jobPostHiringManager, jobPostRecruiters, jobPostPanelMembers, jobPostOnboardSpecialList,
                candidateDetails, candidateEducation, candidateExperience, candidateCertification, candidateRecruitmentInfo,
                candidateInterview, candidateInterviewRound, interviewPanelMemberScore
                ] = await Promise.all([
                    getJobPostDetails(organizationDbConnection, startDate, endDate),
                    getJobPostHiringManager(organizationDbConnection, startDate, endDate),
                    getJobPostRecruiters(organizationDbConnection, startDate, endDate),
                    getJobPostPanelMembers(organizationDbConnection, startDate, endDate),
                    getJobPostOnboardingSpecialList(organizationDbConnection, startDate, endDate),
                    getCandidateDetails(organizationDbConnection, startDate, endDate),
                    getCandidateEducation(organizationDbConnection, startDate, endDate),
                    getCandidateExperience(organizationDbConnection, startDate, endDate),
                    getCandidateCertification(organizationDbConnection, startDate, endDate),
                    getCandidateRecruitmentInfo(organizationDbConnection, startDate, endDate),
                    getCandidateInterview(organizationDbConnection, startDate, endDate),
                    getCandidateInterviewRound(organizationDbConnection, startDate, endDate),
                    getInterviewPanelMemberScore(organizationDbConnection, startDate, endDate)
                ]);

            return [ 
                {'Job-Post': jobPostDetails}, 
                { 'Hiring-Team-Recruiters': jobPostRecruiters},
                { 'Hiring-Team-PanelMembers': jobPostPanelMembers},
                { 'Hiring-Team-Managers': jobPostHiringManager},
                { 'Hiring-Onboard-Specialist': jobPostOnboardSpecialList},
                { 'Candidate': candidateDetails},
                { 'Cadnidate-Recruitement-Info': candidateRecruitmentInfo},
                { 'Candidate-Education': candidateEducation},
                { 'Candidate-Experience': candidateExperience},
                { 'Candidate-Certification': candidateCertification},
                { 'Candidate-Interview-Rounds': candidateInterview},
                { 'Interview-Rounds': candidateInterviewRound},
                { 'PanelMember-Score-Card': interviewPanelMemberScore},
            ];
            
        }catch(mainCatchError){
            console.log('Error in rmsDataExportEntomoDashboard function main catch block', mainCatchError);
            throw 'Error in getting the resignation report details';
        }
    },

    manPowerPlanningDataExport: async(organizationDbConnection, reportType, startDate, endDate) =>{

        try {
             console.log('Inside manPowerPlanningDataExport function');
            // Get all job-post, candidate and interview details record
            const [ positionMasterData, hiringForeCastData, workingConditionsData, recruimentRequestData,
                newPositionRequestData, dutiesResponsibilitiesData, experienceRequirements, 
                educationRequirementsDescData, positionLevelData
            ] = 
                await Promise.all([
                    getPositionMaster(organizationDbConnection, startDate, endDate),
                    getHiringForeCastList(organizationDbConnection, startDate, endDate), 
                    getMppWorkingConditions(organizationDbConnection, startDate, endDate),
                    getMppRecruimentRequest(organizationDbConnection, startDate, endDate),
                    getMppNewPositionRequest(organizationDbConnection, startDate, endDate),
                    getMppDutiesResponsibilities(organizationDbConnection, startDate, endDate), 
                    getMppExperienceRequirements(organizationDbConnection, startDate, endDate),
                    getMppEducationRequirementsDescriptions(organizationDbConnection, startDate, endDate), 
                    getPositionLevel(organizationDbConnection, startDate, endDate)
                ]);

            return [ 
                { 'position_master': positionMasterData}, 
                { 'hiring_forecasting':  hiringForeCastData},
                { 'mpp_working_conditions': workingConditionsData},
                { 'mpp_recruitment_request': recruimentRequestData},
                { 'mpp_new_position_request': newPositionRequestData},
                { 'mpp_duties_responsibilities': dutiesResponsibilitiesData},
                { 'mpp_experience_requirements': experienceRequirements},
                { 'mpp_education_requirements_desc': educationRequirementsDescData},
                { 'position_level': positionLevelData},
            ];
            
        }catch(mainCatchError){
            console.log('Error in manPowerPlanningDataExport function main catch block', mainCatchError);
            throw 'Error in getting the resignation report details';
        }
    }
};



// function to form the report header based on input report type
async function formReportHeader(reportType, useGlobalResourceId = null,dashboardType=null){
    let reportHeaders={};
    try{
        /** If report type is EMPSR then form the header for productivity summary report */
        if(reportType==='EMPSR'){
            reportHeaders={
                employeeId: "Employee ID",
                employeeFirstName:"Employee First Name",
                employeeLastName:"Employee Last Name",
                employeeName: "Employee Name",
                employeeEmail: "Employee Email Address",
                manager:"Manager",
                activityDate: "Activity Date",
                totalSystemUpTime: "Total System Up Time",
                totalSystemUpTimeInMinutes: "Total System Up Time (In Minutes)",
                totalUserActiveTime : "Total User Active Time",
                totalUserActiveTimeInMinutes: "Total User Active Time (In Minutes)",
                userActiveTimeSpentOnKeyboardAndMouse: "User Active Time Spent on Keyboard & Mouse",
                userActiveTimeSpentOnKeyboardAndMouseInMinutes: "User Active Time Spent on Keyboard & Mouse (In Minutes)",
                totalNotActiveTime : "Total Not Active Time",
                totalNotActiveTimeInMinutes: "Total Not Active Time (In Minutes)",
                totalIdleTime : "Total Idle Time",
                totalIdleTimeInMinutes: "Total Idle Time (In Minutes)",
                timeSpentOnIdleAndNotActive:"Time Spent on Idle & Not Active",
                timeSpentOnIdleAndNotActiveInMinutes:"Time Spent on Idle & Not Active (In Minutes)",
                systemUpTimeWithoutLockAppTime: "Total System Active Time Without Lock App",
                systemUpTimeWithoutLockAppTimeInMinutes: "Total System Active Time Without Lock App (In Minutes)",
                lockAppTime: "Total Lock App Time",
                lockAppTimeInMinutes: "Total Lock App Time (In Minutes)",
                systemProductivityPercentageIncludesIdleAndNotActiveTime:"System Productivity % (Includes Idle & Not Active Time)",
                systemProductivityPercentageExcludesIdleAndNotActiveTime:"System Productivity % (Excludes Idle & Not Active Time)",
                sysProductivityPercentageBasedOnFixedDailyWorkHours:"System productivity % (Based on fixed daily work hours)",
                timeSpentOnProductiveApps: "Time Spent on Productive Apps",
                timeSpentOnProductiveAppsInMinutes: "Time Spent on Productive Apps (In Minutes)",
                timeSpentOnProductiveUrls: "Time Spent on Productive URLs",
                timeSpentOnProductiveUrlsInMinutes: "Time Spent on Productive URLs (In Minutes)",
                timeSpentOnProductiveAppsAndUrls: "Time Spent on Productive Apps & URLs",
                timeSpentOnProductiveAppsAndUrlsInMinutes: "Time Spent on Productive Apps & URLs (In Minutes)",
                userProductivityPercentageProductiveOnly:'User Productivity Percentage (Productive Only)',
                userProdPercentageProductiveOnlyBasedOnFixedHours:"User Productivity Percentage (Productive Only) Based On Fixed Daily Work Hours",
                productivityStartTime: "Productivity Start Time",
                productivityEndTime: "Productivity End Time",
                attendanceSessionTime: "Attendance Session Time",
                attendanceSessionTimeInMinutes: "Attendance Session Time (In Minutes)",
                timeZone: "Time Zone"
            }
        }
        /** If report type is EMPUSR then form the header for productivity & unproductivity summary report */
        else if(reportType==='EMPUSR'){
            reportHeaders={
                employeeId: "Employee ID",
                employeeFirstName:"Employee First Name",
                employeeLastName:"Employee Last Name",
                employeeEmail: "Employee Email Address",
                manager:"Manager",
                activityDate: "Activity Date",
                totalSystemUpTime: "Total System Up Time",
                totalSystemUpTimeInMinutes: "Total System Up Time (In Minutes)",
                totalUserActiveTime : "Total User Active Time",
                totalUserActiveTimeInMinutes: "Total User Active Time (In Minutes)",
                userActiveTimeSpentOnKeyboardAndMouse: "User Active Time Spent on Keyboard & Mouse",
                userActiveTimeSpentOnKeyboardAndMouseInMinutes:"User Active Time Spent on Keyboard & Mouse (In Minutes)",
                totalNotActiveTime : "Total Not Active Time",
                totalNotActiveTimeInMinutes: "Total Not Active Time (In Minutes)",
                totalIdleTime : "Total Idle Time",
                totalIdleTimeInMinutes: "Total Idle Time (In Minutes)",
                timeSpentOnIdleAndNotActive: "Time Spent on Idle & Not Active",
                timeSpentOnIdleAndNotActiveInMinutes:"Time Spent on Idle & Not Active (In Minutes)",
                systemUpTimeWithoutLockAppTime: "Total System Active Time Without Lock App",
                systemUpTimeWithoutLockAppTimeInMinutes: "Total System Active Time Without Lock App (In Minutes)",
                lockAppTime: "Total Lock App Time",
                lockAppTimeInMinutes: "Total Lock App Time (In Minutes)",
                systemProductivityPercentageIncludesIdleAndNotActiveTime:"System Productivity % (Includes Idle & Not Active Time)",
                sysProductivityPercentageBasedOnFixedDailyWorkHours:"System productivity % (Based on fixed daily work hours)",
                systemProductivityPercentageExcludesIdleAndNotActiveTime:"System Productivity % (Excludes Idle & Not Active Time)",
                timeSpentOnProductiveApps: "Time Spent on Productive Apps",
                timeSpentOnProductiveAppsInMinutes: "Time Spent on Productive Apps (In Minutes)",
                timeSpentOnNeutralApps:"Time Spent on Neutral Apps",
                timeSpentOnNeutralAppsInMinutes:"Time Spent on Neutral Apps (In Minutes)",
                timeSpentOnUnProductiveApps:"Time Spent on Unproductive Apps",
                timeSpentOnUnProductiveAppsInMinutes:"Time Spent on Unproductive Apps (In Minutes)",
                timeSpentOnProductiveUrls: "Time Spent on Productive URLs",
                timeSpentOnProductiveUrlsInMinutes: "Time Spent on Productive URLs (In Minutes)",
                timeSpentOnNeutralUrls:"Time Spent on Neutral URLs",
                timeSpentOnNeutralUrlsInMinutes:"Time Spent on Neutral URLs (In Minutes)",
                timeSpentOnUnProductiveUrls:"Time Spent on Unproductive URLs",
                timeSpentOnUnProductiveUrlsInMinutes:"Time Spent on Unproductive URLs (In Minutes)",
                timeSpentOnProductiveAppsAndUrls: "Time Spent on Productive Apps & URLs",
                timeSpentOnProductiveAppsAndUrlsInMinutes:"Time Spent on Productive Apps & URLs (In Minutes)",
                timeSpentOnNeutralAppsAndUrls: "Time Spent on Neutral Apps & URLs",
                timeSpentOnNeutralAppsAndUrlsInMinutes:"Time Spent on Neutral Apps & URLs (In Minutes)",
                timeSpentOnUnProductiveAppsAndUrls: "Time Spent on UnProductive Apps & URLs",
                timeSpentOnUnProductiveAppsAndUrlsInMinutes:"Time Spent on UnProductive Apps & URLs (In Minutes)",
                userProductivityPercentageProductiveOnly:'User Productivity Percentage (Productive Only)',
                userProductivityPercentageProductiveAndNeutral:'User Productivity Percentage (Productive & Neutral)',
                userProductivityPercentageBasedOnFixedDailyWorkHours:"User Productivity Percentage (Productive & Neutral) Based On Fixed Daily Work Hours",
                userProdPercentageProductiveOnlyBasedOnFixedHours:"User Productivity Percentage (Productive Only) Based On Fixed Daily Work Hours",
                productivityStartTime: "Productivity Start Time",
                productivityEndTime: "Productivity End Time",
                attendanceSessionTime: "Attendance Session Time",
                attendanceSessionTimeInMinutes: "Attendance Session Time (In Minutes)",
                timeZone: "Time Zone",
                department:'Department',
                designation:'Designation'
            }
        }
        /** If report type is EMCAUR then form the header for Consolidated Apps and URLs report */
        else if(reportType==='EMCAUR'){
            reportHeaders={
                employeeId: 'Employee ID',
                employeeFirstName: 'Employee First Name',
                employeeLastName: 'Employee Last Name',
                employeeEmail: 'Employee Email Address',
                department: 'Department',
                designation: 'Designation',
                activityDate: 'Activity Date',
                applicationOrUrl: 'Application/URL',
                recordType: 'Record Type (Application or URL)',
                category: 'Category',
                totalTimeWorked: 'Total Time Worked',
                totalTimeWorkedInMinutes: 'Total Time Worked (In Minutes)',
            }
        }
        /** If report type is HREAR/HREUR then form the header for Employee report */
        else if(reportType==='HREAR' || reportType==='HREUR'){
            reportHeaders={
                Employee_Id: 'Employee_Id',
                Emp_First_Name: 'Emp_First_Name',
                Emp_Middle_Name: 'Emp_Middle_Name',
                Emp_Last_Name: 'Emp_Last_Name',
                Service_Provider_Name: 'Service_Provider_Name',
                Location_Name: 'Location_Name',
                Department_Name: 'Department_Name',
                Designation_Name: 'Designation_Name',
                Date_Of_Join: 'Date_Of_Join',
                Personal_Email: 'Personal_Email',
                Emp_Email: 'Emp_Email',
                Mobile_No: 'Mobile_No',
                Work_No: 'Work_No',
                Aadhaar_Card_Number: 'Aadhaar_Card_Number',
                PAN: 'PAN',
                UAN: 'UAN',
                ESI_Number: 'ESI_Number',
                pApartment_Name: 'pApartment_Name',
                pStreet_Name: 'pStreet_Name',
                pCity: 'pCity',
                pState: 'pState',
                pCountry: 'pCountry',
                pPincode: 'pPincode',
                cApartment_Name: 'cApartment_Name',
                cStreet_Name: 'cStreet_Name',
                cCity: 'cCity',
                cState:'cState',
                cCountry: 'cCountry',
                cPincode: 'cPincode'
            }
            /** If the Use_Global_Resource_Id is yes then add the Global_Resource_Id column*/
            if(useGlobalResourceId === 'yes') {
                reportHeaders.Global_Resource_Id = 'Global_Resource_Id';
            }
        }
        else if(reportType==='HRLR')
        {
            reportHeaders={
                Employee_Id:'Employee_Id',
                Emp_First_Name:'Emp_First_Name',
                Emp_Middle_Name:'Emp_Middle_Name',
                Emp_Last_Name:'Emp_Last_Name',
                Leave_Name:'Leave_Name',
                Duration:'Duration',
                Start_Date:'Start_Date',
                End_Date:'End_Date',
                Total_Days:'Total_Days',
                Approval_Status:'Approval_Status',
                Approver_First_Name:'Approver_First_Name',
                Approver_Middle_Name:'Approver_Middle_Name',
                Approver_Last_Name:'Approver_Last_Name'
            }
            /** If the Use_Global_Resource_Id is yes then add the Global_Resource_Id column*/
            if(useGlobalResourceId === 'yes') {
                reportHeaders.Global_Resource_Id = 'Global_Resource_Id';
            }
        }
        else if(reportType==='HRRR')
        {
            reportHeaders={
                Employee_Id:'Employee_Id',
                Emp_First_Name:'Emp_First_Name',
                Emp_Middle_Name:'Emp_Middle_Name',
                Emp_Last_Name:'Emp_Last_Name',
                Notice_Date:'Notice_Date',
                Resignation_Date:'Resignation_Date'
            }
            /** If the Use_Global_Resource_Id is yes then add the Global_Resource_Id column*/
            if(useGlobalResourceId === 'yes') {
                reportHeaders.Global_Resource_Id = 'Global_Resource_Id';
            }
        }
        /** If report type is EMAR then form the header for asset report */
        else if(reportType == 'EMAR'){
            reportHeaders={
                employeeId: "Employee ID",
                employeeFirstName:"Employee First Name",
                employeeLastName:"Employee Last Name",
                employeeEmail: "Employee Email Address",
                manager:"Manager",
                serialNumber: "Serial Number",
                macAddress: "MAC Address",
                computerName: "Computer Name",
                assetType: "Asset Type",
                operatingSystem: "Operating System",
                agentVersion: "Agent Version",
                status: "Status",
                systemUserName: "System User Name"
            }
        }
        /** If report type is EMASR then form the header for apps summary report */
        else if(reportType == 'EMASR' ){
            reportHeaders= {
                employeeId: "Employee ID",
                employeeFirstName: "Employee First Name",
                employeeLastName: "Employee Last Name",
                employeeEmail:"Employee Email Address",
                manager: "Manager",
                application: "Application",
                applicationTitle: "Application Title",
                category: "Category",
                activityDate: "Activity Date",
                totalTimeWorked: "Total Time Worked",
                totalTimeWorkedInMinutes: "Total Time Worked (In Minutes)",
                department:'Department',
                designation:'Designation'
            };
        }
        /** If report type is EMUSR then form the header for urls summary report */
        else if(reportType == 'EMUSR' ){
            reportHeaders = {
                employeeId: "Employee ID",
                employeeFirstName: "Employee First Name",
                employeeLastName: "Employee Last Name",
                employeeEmail: "Employee Email Address",
                manager: "Manager",
                domain: "Domain",
                category: "Category",
                activityDate: "Activity Date",
                totalTimeWorked: "Total Time Worked",
                totalTimeWorkedInMinutes: "Total Time Worked (In Minutes)",
                department:'Department',
                designation:'Designation' 
            };
        }
        return reportHeaders;
    }
    catch(error){
        console.log('Error in formReportHeader function main catch block.',error);
        return {};
    }
}

/** function to update key name based on category */
async function updatekeyNameBasedOnCategory(data,source){
    try{
        if(source==='app'){
            let output = await Promise.all(data.map(async(field) => {
                // If category is productive then update time spent as productive time spent
                if(field.Category==='Productive'){
                    field.timeSpentOnProductiveAppsInMinutes = field.timeSpentOnAppsInMinutes;
                    field.timeSpentOnProductiveAppsInSeconds = field.timeSpentOnAppsInSeconds;
                    field.timeSpentOnProductiveApps = await getTimeInHoursMinutesSeconds(field.timeSpentOnAppsInSeconds);
                }
                // If category is unproductive then update time spent as unproductive time spent
                else if(field.Category==='Unproductive'){
                    field.timeSpentOnUnproductiveAppsInMinutes = field.timeSpentOnAppsInMinutes;
                    field.timeSpentOnUnproductiveAppsInSeconds = field.timeSpentOnAppsInSeconds;
                    field.timeSpentOnUnproductiveApps = await getTimeInHoursMinutesSeconds(field.timeSpentOnAppsInSeconds);
                }
                // If category is uncategorized then update time spent as neutral time spent
                else if(field.Category==='Uncategorized'){
                    field.timeSpentOnNeutralAppsInMinutes = field.timeSpentOnAppsInMinutes;
                    field.timeSpentOnNeutralAppsInSeconds = field.timeSpentOnAppsInSeconds;
                    field.timeSpentOnNeutralApps = await getTimeInHoursMinutesSeconds(field.timeSpentOnAppsInSeconds);
                }
                delete field.Category;
                return field;
            }))
            return output;
        }
        if(source==='url'){
            let output = await Promise.all(data.map(async(field) => {
                // If category is productive then update time spent as productive time spent
                if(field.Category==='Productive'){
                    field.timeSpentOnProductiveUrlsInMinutes = field.timeSpentOnUrlInMinutes;
                    field.timeSpentOnProductiveUrlsInSeconds = field.timeSpentOnUrlInSeconds;
                    field.timeSpentOnProductiveUrls = await getTimeInHoursMinutesSeconds(field.timeSpentOnUrlInSeconds);
                }
                // If category is unproductive then update time spent as unproductive time spent
                else if(field.Category==='Unproductive'){
                    field.timeSpentOnUnproductiveUrlsInMinutes = field.timeSpentOnUrlInMinutes;
                    field.timeSpentOnUnproductiveUrlsInSeconds = field.timeSpentOnUrlInSeconds;
                    field.timeSpentOnUnproductiveUrls = await getTimeInHoursMinutesSeconds(field.timeSpentOnUrlInSeconds);
                }
                // If category is uncategorized then update time spent as neutral time spent
                else if(field.Category==='Uncategorized'){
                    field.timeSpentOnNeutralUrlsInMinutes = field.timeSpentOnUrlInMinutes;
                    field.timeSpentOnNeutralUrlsInSeconds = field.timeSpentOnUrlInSeconds;
                    field.timeSpentOnNeutralUrls = await getTimeInHoursMinutesSeconds(field.timeSpentOnUrlInSeconds);
                }
                delete field.Category;
                return field;
            }))
            return output;
        }
        if(source==='browserapptime'){
            let output = await Promise.all(data.map(async(field) => {
                // If category is productive then update time spent as productive browser app time
                if(field.Category==='Productive'){
                    field.totalProductiveAppBrowserTimeInSeconds = field.totalAppBrowserTimeInSeconds;
                }
                // If category is unproductive then update time spent as unproductive browser app time
                else if(field.Category==='Unproductive'){
                    field.totalUnproductiveAppBrowserTimeInSeconds = field.totalAppBrowserTimeInSeconds;
                }
                // If category is uncategorized then update time spent as neutral browser app time
                else if(field.Category==='Uncategorized'){
                    field.totalNeutralAppBrowserTimeInSeconds = field.totalAppBrowserTimeInSeconds;
                }
                delete field.Category;
                delete field.totalAppBrowserTimeInSeconds;
                return field;
            }))
            return output; 
        }
    }
    catch(error){
        console.log('Error in updatekeyNameBasedOnCategory function main catch block.',error);
        throw 'EM0197';
    }
}

async function getCustomReportHeaderConfiguration(organizationDbConnection, reportSource, reportType){
    try {
        return(
            organizationDbConnection(ehrTables.customReportHeadersConfiguration)
                .select('Custom_Headers')
                .from(ehrTables.customReportHeadersConfiguration)
                .where('Report_Source',reportSource)
                .where('Report_Type',reportType)
                .where('Enable','Yes')
                .then(async(configDetails) => {
                    let configData = [];
                        try {
                            configData = JSON.parse(configDetails[0].Custom_Headers);
                        } catch (jsonError) {
                            console.log('Error parsing Custom_Headers JSON:', jsonError);
                            const defaultHeadersObj = JSON.parse(configDetails[0]?.Default_Headers || '{}');
                            configData = Object.keys(defaultHeadersObj);
                        }
                    return configData;
                }).catch(catchError => {
                    console.log('Error in getCustomReportHeaderConfiguration() function .catch block', catchError);
                    return [];
                })
        );

    } catch (err){
        console.log('Error in getCustomReportHeaderConfiguration function .catch block', err);
        return [];
    }
}


async function formResponseBasedOnCustomConfiguration(configData, reportData){
    try {
        let resultArray = [];
        /**Form the final array containing only the configured keys */
        resultArray = reportData.map(details => {
            let result = {};
            configData.forEach(key => {
                result[key] = details[key];
            });
            return result;
        });
        return resultArray;

    } catch (err){
        console.log('Error in formResponseBasedOnCustomConfiguration function .catch block', err);
        return reportData;
    }
}

module.exports.getCustomReportHeaderConfiguration=getCustomReportHeaderConfiguration;
