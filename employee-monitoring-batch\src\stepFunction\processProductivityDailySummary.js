'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require constant value
const { defaultValues }=require('../common/appConstants');
// require common function
const { updateSummaryDataInMasterTable,triggerSummarizationLambda }=require('./commonFunctions');

// variable declarations
let summarizationStatusTable=ehrTables.appurlActivitySummarizationStatus;
let masterTable=appManagerTables.appurlActivitySummarizationManager;
let inputStatus;
let orgCode='';
let appmanagerDbConnection='';
let inputSource;

module.exports.processProductivityDailySummary  = async(event, context) =>{
    try{
        console.log('Inside processProductivityDailySummary function',event);
        // get input data
        let inputData=event.input;
        inputStatus=inputData.status;
        inputSource=inputData.process;
        // make database connection
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',0);
        // check whether connection exist or not
        if(Object.keys(databaseConnection).length>0){
            // get app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            return(
                // get all the records with Open status from manager table
                appmanagerDbConnection(masterTable)
                .select('Org_Code')
                .where('Status','Open')
                .then(async (summaryData) =>{
                    // check data exist or not
                    if (summaryData.length>0){
                        // formation of params to update the inprogress status
                        let updateParams={
                            Transaction_Status : 'Inprogress',
                            Summarization_Time : new Date()
                        }
                        /**  we are making async call for summarization process so we need to limit the number of database connection so
                         * process maximum of 20 records in a job and handle rest in separate batch.
                         * Based on condition form the number of instance to be processed */
                        let instanceToBeProcessed=(summaryData.length>defaultValues.empMonitorUserCount)?(defaultValues.empMonitorUserCount):(summaryData.length);
                        // iterate for all the instances
                        for(let i = 0; i <instanceToBeProcessed; i++){
                            orgCode=summaryData[i].Org_Code;
                            // update the inprogress status in status table
                            let updateStatus=await updateInprogressStatusInStatusTable(updateParams,summarizationStatusTable,orgCode,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region)
                            console.log('Update inprogress status in organization table for '+orgCode+' instance',updateStatus);
                            if(updateStatus){
                                let updateParamsInManagerTable={
                                    Status : 'Inprogress'
                                }
                                // function to update inprogress status in manager table
                                let updateMasterTableStatus=await updateSummaryDataInMasterTable(appmanagerDbConnection,updateParamsInManagerTable,orgCode,masterTable);
                                if(updateMasterTableStatus){
                                    console.log('Update inprogress status in manager table for '+orgCode+' instance',updateMasterTableStatus);
                                    // formation of payload
                                    let payLoad={
                                        process:inputSource,
                                        status:inputStatus,
                                        orgCode:orgCode
                                    }
                                    const params = {
                                        FunctionName: process.env.summarizationProcessTrigger,
                                        InvocationType: "RequestResponse",
                                        Payload: JSON.stringify(payLoad)
                                    };
                                    // Invoke lambda to summarize the daily activities
                                    await triggerSummarizationLambda(params,orgCode);
                                }
                                else{
                                    console.log('Error while updating inprogress status in manager table for '+orgCode+' instance.Summarization process need to be triggered.',updateMasterTableStatus);
                                }
                            }
                            else{
                                console.log('Error in updating inprogress status in organization table for '+ orgCode +' instance');
                            }
                        }
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'inputSource':inputSource,'status':inputStatus},
                            message:'summarization process completed'
                        };
                        return response;
                    }
                    else{
                        console.log('No open record found in manager table. So quit the execution');
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'inputSource':inputSource,'status':inputStatus},
                            message:'No open records found.'
                        };
                        return response;
                    }
                })
                .catch(catchError=>{
                    console.log('Error in processProductivityDailySummary .catch block.', catchError);
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'Error',
                        input:{'inputSource':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error from step2'},
                        message:'Error in step 2 .catch block'
                    };
                    return response;
                })
            );
        }
        else{
            console.log('Error while creating app manager database connection in step2');
            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
            let response = 
            {
                nextStep:'Error',
                input:{'inputSource':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error creating app manager database connection from step2'},
                message:'Error in creating app manager database connection in step2.'
            };
            return response;
        }
    } catch (mainCatchError){
        console.log('Error in processProductivityDailySummary function main catch block.', mainCatchError);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response = 
        {
            nextStep:'Error',
            input:{'inputSource':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error in step2 main catch block.'},
            message:'Error in step2 main catch block.'
        };
        return response;
    }
};

// function to update status in organization table
async function updateInprogressStatusInStatusTable(updateParams,tableName,orgCode,stageName,dbPrefix,dbSecretName,region){
    try{
        let databaseConnection=await commonLib.stepFunctions.getConnection(stageName,dbPrefix,dbSecretName,region,'',1);
        let appManagerDbConnection = knex(databaseConnection.AppManagerDb);
        //Form additional headers
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection,orgCode);
        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
            

            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(stageName,dbPrefix,dbSecretName,region,orgCode,0,additionalHeaders);
            if(Object.keys(databaseConnection).length>0){
                let orgDbConnection = knex(databaseConnection.OrganizationDb);
                return(
                    orgDbConnection(tableName)
                    .update(updateParams)
                    .where('Transaction_Status','Open')
                    .then(async (updateData) => {
                        console.log('Inprogress status updated in organization table for '+orgCode+' instance',updateData);
                        orgDbConnection?orgDbConnection.destroy():null;
                        appManagerDbConnection?appManagerDbConnection.destroy():null;
                        return 'success';
                    })
                    .catch(function (catchError) {
                        console.log('Error in updateInprogressStatusInStatusTable function .catch block.', catchError);
                        orgDbConnection?orgDbConnection.destroy():null;
                        appManagerDbConnection?appManagerDbConnection.destroy():null;
                        return 'error';
                    })        
                );
            }
            else{
                console.log('Error while creating database connection for '+orgCode+' instance');
                orgDbConnection?orgDbConnection.destroy():null;
                appManagerDbConnection?appManagerDbConnection.destroy():null;
                return 'error';
            }
        } else{
            console.log('Error while getting data region for '+orgCode+' instance');
            orgDbConnection?orgDbConnection.destroy():null;
            appManagerDbConnection?appManagerDbConnection.destroy():null;
            return 'error';
        }
    } catch (mainCatchError){
        console.log('Error in updateInprogressStatusInStatusTable function main catch block.', mainCatchError);
        orgDbConnection?orgDbConnection.destroy():null;
        appManagerDbConnection?appManagerDbConnection.destroy():null;
        return 'error';
    }
}