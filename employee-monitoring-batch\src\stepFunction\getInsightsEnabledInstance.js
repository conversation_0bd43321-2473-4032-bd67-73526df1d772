'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { formIds,defaultValues } = require('../common/appConstants');
// require common function
const {calculateActivityDateBasedOnZone}=require('./commonFunctions');

// variable declarations
let appManagerDbConnection='';
let orgDbConnection='';

// function to get the employee monitoring notification enabled settings
module.exports.getInsightsEnabledInstance  = async(event, context) =>{
    let source='';
    let schedule = 0;
    try{
        console.log('Inside getInsightsEnabledInstance stepfunction ', event);
        source=event.source;
        schedule = event.schedule;
        // variable declarations
        let activePlanDetails=[];
        // make database connection
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether data exist or not
        if(Object.keys(databaseConnection).length){
            // get app manager database connection
            appManagerDbConnection=knex(databaseConnection.AppManagerDb);
            // get active employee monitoring plan subscribed users
            let empMonitoringSubscribedUsers= await commonLib.func.getInstanceSubscribedToFormId(formIds.activityTracker,appManagerDbConnection,defaultValues.activeStatus);
            // check whether employee monitoring subscribed user exist or not
            if(empMonitoringSubscribedUsers.length>0){
                // get the insights notification settings enabled for the active users
                return(
                    appManagerDbConnection
                    .select('Org_Code')
                    .from(appManagerTables.orgRateChoice)
                    .whereIn('Org_Code',empMonitoringSubscribedUsers)
                    .where('Plan_Status','Active')
                    .where(qb =>
                        qb.where("Plan_End_Date", "0000-00-00").orWhereNull("Plan_End_Date")
                    )
                    .where('Insights_Notification','Enable')
                    .then(async(getPlanDetails)=>{
                        // check whether data exist  or not
                        if(getPlanDetails.length>0){
                            let resultArray=[];
                            for(let key of getPlanDetails){
                                //Form additional headers
                                let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection,key.Org_Code);
                                if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                                    let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                                    
                                    // make database connection
                                    let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,key.Org_Code,0,additionalHeaders);
                                    orgDbConnection=knex(databaseConnection.OrganizationDb);
                                    let response;
                                    /** when source is employee */
                                    if(source==='employee'){
                                        /** based on settings get the employees and check their summary data for the activity date */
                                        response=await checkEmployeeNotificationSettings(source,key,orgDbConnection,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region, schedule);
                                    }
                                    /** source will be either admin or manager */
                                    else{
                                        // calculate the activity date and check summary data exist for the input orgcode
                                        response=await checkSummaryDataExistence(source,key,orgDbConnection,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region, schedule);
                                    }
                                    if(response){
                                        resultArray.push(response);
                                    }
                                } else {
                                    console.log('Error while getting the data region');
                                }
                            }
                            activePlanDetails=resultArray;
                            
                            if(activePlanDetails.length>0)
                            {
                                // delete the records in manager table
                                return (
                                    appManagerDbConnection(appManagerTables.insightsNotificationManager)
                                    .del()
                                    .then(deleteUser =>{
                                        console.log('Clean up the notification manager table before inserting', activePlanDetails );
                                        // insert the record in manager table
                                        return (
                                            appManagerDbConnection(appManagerTables.insightsNotificationManager)
                                            .insert(activePlanDetails)
                                            .then(insertData => {
                                                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                let response = 
                                                {
                                                    nextStep:'Step2',
                                                    input:{'process':source},
                                                    message:'Process the step2.'
                                                };
                                                return response;
                                            })
                                        )
                                    })
                                )
                            }
                            else{
                                console.log('No activity details/notification enabled instances found');
                                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                orgDbConnection?orgDbConnection.destroy():null;
                                let response = 
                                {
                                    nextStep:'End',
                                    input:{'process':source},
                                    message:'No data exist.'
                                };
                                return response;
                            }
                        }
                        else{
                            console.log('No insights notification settings enabled instance found in org rate choice.');
                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                            let response = 
                            {
                                nextStep:'End',
                                input:{'process':source},
                                message:'No notification enabled instance exist.'
                            };
                            return response;
                        }
                    })
                    .catch(error => {
                        console.log('Error in getInsightsEnabledInstance function .catch block',error);
                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'process':source},
                            message:'Error from step1 .catch block'
                        };
                        return response;
                    })
                );
            }
            else{
                console.log('There is no employee monitoring subscribed active users. So stop the execution.');
                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                let response = 
                {
                    nextStep:'End',
                    input:{'process':source},
                    message:'No active instances exists.'
                };
                return response;
            }
        }
        else{
            console.log('Error while making database connection');
            appManagerDbConnection ? appManagerDbConnection.destroy():null;
            let response = 
            {
                nextStep:'End',
                input:{'process':source},
                message:'Error while making database connection'
            };
            return response;
        }
    }
    catch (mainCatchError){
        console.log('Error in getInsightsEnabledInstance function main catch block.', mainCatchError);
        appManagerDbConnection ? appManagerDbConnection.destroy():null;
        let response = 
        {
            nextStep:'End',
            input:{'process':source},
            message:'Error from step1 main catch block'
        };
        return response;
    }
};

// function to check summary data exist for the activity date
async function checkSummaryDataExistence(source,key,orgDbConnection,stageName,dbPrefix,dbSecretName,region, schedule){
    try{
        let fieldName=(source==='admin')?'Insights_Admin_Notification':'Insights_Manager_Notification';
        // check notification status enabled based on the source for admin/manager 
        return(
            orgDbConnection
            .select(fieldName)
            .from(ehrTables.employeeMonitorSettings)
            .where(fieldName,"Enable")
            .then(async(getSettingData)=>{
                if(getSettingData.length>0){
                    console.log(source+ ' insights notification is enabled for '+key.Org_Code+ ' instance');
                    // calculate the activity date based on work schedule timezone
                    let currentDateTime=await calculateActivityDateBasedOnZone(orgDbConnection, schedule);
                    if(currentDateTime){
                        let dateBasedOnZone=moment(currentDateTime).format('YYYY-MM-DD');
                        console.log('Activity date for '+key.Org_Code+' instance',dateBasedOnZone);
                        // check daily summary record exist for the previous activity date
                        return(
                            orgDbConnection
                            .count('Employee_Id as count')
                            .from(ehrTables.employeeActivityDailySummary)
                            .where('Activity_Date',dateBasedOnZone)
                            .then(async(summaryData)=>{
                                if(summaryData[0].count>0){
                                    key['Activity_Date']=dateBasedOnZone;
                                    key['Status']='Open';
                                    orgDbConnection ? orgDbConnection.destroy():null;
                                    return key;
                                }
                                else{
                                    console.log('No activity summary details found');
                                    orgDbConnection ? orgDbConnection.destroy():null;
                                    return '';
                                }
                            })
                        );
                    } else {
                        return '';
                    }
                }
                else{
                    console.log(source+' insights settings not enabled for '+key.Org_Code+ ' instance');
                    orgDbConnection ? orgDbConnection.destroy():null;
                    return '';
                }
            })
            .catch(catchError=>{
                console.log('Error in checkSummaryDataExistence function .catch block',catchError);
                orgDbConnection ? orgDbConnection.destroy():null;
                return '';
            })
        )
    }
    catch(error){
        console.log('Error in checkSummaryDataExistence function catch block',error);
        orgDbConnection ? orgDbConnection.destroy():null;
        return '';
    }
};

/** If source is employee then check whether notification settings enabled */
async function checkEmployeeNotificationSettings(source,key,orgDbConnection,stageName,dbPrefix,dbSecretName,region, schedule){
    try{
        // Get the employeeIds for which notification is enabled
        return(
            orgDbConnection
            .pluck('Employee_Id')
            .from(ehrTables.employeeLevelInsightsNotificationSettings)
            .where('Insights_Notification',"Enable")
            .then(async(getSettingData)=>{
                if(getSettingData.length>0){
                    console.log(source+ ' insights notification is enabled for some of the employees in '+key.Org_Code+ ' instance');
                    // calculate the activity date based on work schedule timezone
                    let currentDateTime=await calculateActivityDateBasedOnZone(orgDbConnection, schedule);
                    if(currentDateTime){
                        let dateBasedOnZone=moment(currentDateTime).format('YYYY-MM-DD');
                        console.log('Activity date for '+key.Org_Code+' instance',dateBasedOnZone);
                        // check whether summary record exist for the employee
                        return(
                            orgDbConnection
                            .count('Employee_Id as count')
                            .from(ehrTables.employeeActivityDailySummary)
                            .where('Activity_Date',dateBasedOnZone)
                            .whereIn('Employee_Id',getSettingData)
                            .then(async(getSettingData)=>{
                                if(getSettingData[0].count>0){
                                    key['Activity_Date']=dateBasedOnZone;
                                    key['Status']='Open';
                                    orgDbConnection ? orgDbConnection.destroy():null;
                                    return key;
                                }
                                else{
                                    console.log('No activity summary details found for the employees');
                                    orgDbConnection ? orgDbConnection.destroy():null;
                                    return '';
                                }
                            })
                        );
                    } else {
                        return '';
                    }
                }
                else{
                    console.log('Employee level notification is not enabled for any employees for '+key.Org_Code+ ' instance');
                    orgDbConnection ? orgDbConnection.destroy():null;
                    return '';
                }
            })
        );
    }
    catch(error){
        console.log('Error in checkEmployeeNotificationSettings function catch block',error);
        orgDbConnection ? orgDbConnection.destroy():null;
        return '';
    }
}
