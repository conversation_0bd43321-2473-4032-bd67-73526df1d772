//get common functions
const{updateInMasterTable,getDataFromMasterTableAccordingToStatus}=require("./commonFunctions");
//get tablealias
const{defaultValues}=require("../common/appConstants")
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
//require tablealias
const{appManagerTables,ehrTables}=require("../common/tableAlias")
//Require moment
const moment = require('moment-timezone');
const { formMinAndMaxTimeBasedOnWorkSchedule,getWorkScheduleAndTimeZone}=require('../common/activityCommonFunctions');
const { getDateTimeBasedOnTimeZoneOffset}=require('../webApplication/employeeMonitoringCommonFunction');
//variable declaration
let appmanagerDbConnection;
let organizationDbConnection;
let updateParams;
let masterTable=appManagerTables.fileTransferSummarizationManager;
let status="Open";

module.exports.processFileTransferActiveEmployee  = async(event,context) =>{
    try{
        console.log("Inside processFileTransferActiveEmployee() function",event)
        let inputStatus=event.input.status;
        if(inputStatus && inputStatus.toLowerCase()==='failed')
        {
            status="Failed";
        }
        let currentDate;
        let maxActivityDate;
        let currentUtcDate=moment.utc().format("YYYY-MM-DD");
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether connection exist or not
        if(Object.keys(databaseConnection).length){
            // get app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            let activeInstances= await getDataFromMasterTableAccordingToStatus(appmanagerDbConnection,masterTable,"Summarization_Status",status,1);
            if(activeInstances)
            {
                if(activeInstances.length>0)
                {
                    let instanceToBeProcessed=(activeInstances.length>defaultValues.activeInstanceToBeProcessed)?(defaultValues.activeInstanceToBeProcessed):(activeInstances.length);
                    for(let i=0;i<instanceToBeProcessed;i++)
                    {
                        let orgCode=activeInstances[i]['Org_Code'];
                        console.log("orgCode: ", orgCode)
                        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
                        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                            
                            //Get database connection
                            let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                            if(Object.keys(connection).length)
                            {
                                organizationDbConnection=knex(connection.OrganizationDb);
                                let activeEmployees= await getEmployeeFromOrgTable(organizationDbConnection,status);
                                if(activeEmployees)
                                {
                                    console.log(orgCode, "activeEmployees..: ", activeEmployees);
                                    if(activeEmployees.length>0)
                                    {
                                        let checkFailed=false;
                                        for(let j=0;j<activeEmployees.length;j++)
                                        {
                                            let employeeId=activeEmployees[j];
                                            // let maxActivityDate=await getMaxActivityDate(organizationDbConnection,employeeId);
                                            // let minActivityDate= await getMinActivityDate(organizationDbConnection,employeeId);
                                            let workScheduleDetails=await getWorkScheduleAndTimeZone(organizationDbConnection,employeeId);
                                            let workScheduleId=workScheduleDetails['workScheduleId'];
                                            let timeZone=workScheduleDetails['timeZone'];
                                            let offSet = moment.tz(moment.utc(), timeZone).utcOffset();
                                            currentDate=moment.tz(moment(),timeZone).format('YYYY-MM-DD');
                                            maxActivityDate=moment(currentDate).subtract(1,'days').format("YYYY-MM-DD");
                                            if(workScheduleDetails &&  workScheduleId && timeZone && timeZone.length>0)
                                            {
                                                console.log("maxActivityDate: ",maxActivityDate, currentDate, maxActivityDate<currentDate)
                                                if(maxActivityDate<currentDate)
                                                {
                                                    while(maxActivityDate<currentDate)
                                                    {
                                                        let activityDate=maxActivityDate;
                                                        // calculate the min and max date time based on input work scheduleId
                                                        let minMaxDateTime= await formMinAndMaxTimeBasedOnWorkSchedule(organizationDbConnection,workScheduleId,activityDate,timeZone);
                                                        let minDateTime=minMaxDateTime.minDateTime;
                                                        let maxDateTime=minMaxDateTime.maxDateTime;
                                                        let fileAndWebEventSummary=await getSummaryDataForFileAndWebEvents(organizationDbConnection,minDateTime,maxDateTime,employeeId,activityDate,currentUtcDate);
                                                        let fileExtensionSummary=await getFileExtensionSummary(organizationDbConnection,minDateTime,maxDateTime,employeeId,activityDate,currentUtcDate);
                                                        let webEventSummary=await getWebFileEventSummary(organizationDbConnection,minDateTime,maxDateTime,employeeId,activityDate,currentUtcDate);
                                                        let fileTransferDetailsAsPerWs=await getFileTransferDetailsAsPerWs(organizationDbConnection,minDateTime,maxDateTime,employeeId,currentUtcDate,timeZone,offSet);
                                                        if(fileAndWebEventSummary && fileExtensionSummary && webEventSummary && fileTransferDetailsAsPerWs )
                                                        {
                                                            if(fileAndWebEventSummary.length===0)
                                                            {
                                                                let defaultFileEvent={Employee_Id:employeeId,Create_Count:0,Rename_Count:0,Delete_Count:0,Write_Count:0,Access_Count:0,Upload_Count:0,Download_Count:0,Copy_Count:0,Activity_Date:activityDate,Load_Timestamp:currentUtcDate};
                                                                fileAndWebEventSummary.push(defaultFileEvent);
                                                            }
                                                            let insertStatus=await insertIntoTables(organizationDbConnection,fileAndWebEventSummary,fileExtensionSummary,webEventSummary,fileTransferDetailsAsPerWs);
                                                            if(insertStatus)
                                                            {
                                                                updateParams={
                                                                    Status : 'Success'
                                                                }
                                                                await updateInEmployeeLevelFileTransferSummarizationManagerTable(organizationDbConnection,updateParams,employeeId)
                                                            }
                                                            else{
                                                                checkFailed=true;
                                                                updateParams={
                                                                    Status : 'Failed'
                                                                }
                                                                await updateInEmployeeLevelFileTransferSummarizationManagerTable(organizationDbConnection,updateParams,employeeId)  
                                                            }
                                                        }
                                                        else{
                                                            checkFailed=true;
                                                            updateParams={
                                                                Status : 'Failed'
                                                            }
                                                            await updateInEmployeeLevelFileTransferSummarizationManagerTable(organizationDbConnection,updateParams,employeeId)
                                                        }
                                                        maxActivityDate=moment(maxActivityDate).add(1,'days').format("YYYY-MM-DD");
                                                    }
                                                }
                                                else{
                                                    updateParams={
                                                        Status : 'Success'
                                                    }
                                                    await updateInEmployeeLevelFileTransferSummarizationManagerTable(organizationDbConnection,updateParams,employeeId)
                                                }                                            
                                            }
                                            else{
                                                checkFailed=true;
                                                updateParams={
                                                    Status : 'Failed'
                                                }
                                                await updateInEmployeeLevelFileTransferSummarizationManagerTable(organizationDbConnection,updateParams,employeeId)
                                            }
                                        }
                                        if(checkFailed)
                                        {
                                            updateParams={
                                                Summarization_Status : 'Failed'
                                            }
                                            await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                                        }
                                        else
                                        {
                                            updateParams={
                                                Summarization_Status : 'Success'
                                            }
                                            await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                                        }
                                    }
                                    else{
                                        console.log("No employees found.");
                                        updateParams={
                                            Summarization_Status : 'Success'
                                        }
                                        await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                                    }
                                }
                                else
                                {
                                    console.log("Error While getting employees.")
                                    updateParams={
                                        Summarization_Status : 'Failed'
                                    }
                                    await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                                }
                                organizationDbConnection?organizationDbConnection.destroy():null;
                            }
                            else{
                                console.log('Error while creating organization database connection in step3');
                                updateParams={
                                    Summarization_Status : 'Failed'
                                }
                                await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                            }
                        }
                        else{
                            console.log("Error while getting orgRegionDetails")
                            updateParams={
                                Summarization_Status : 'Failed'
                            }
                            await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                        }
                    }
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Execution Completed.'
                    };
                    return response;   
                }
                else{
                    console.log('No open or failed record found in file transfer summarization_manager. So quit the execution');
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'No open instances found.'
                    };
                    return response;
                }
            }
            else{
                console.log('Error while getting open instances.');
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response = 
                {
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Error while getting open instances in step3.'
                };
                return response;
            }
         }
         else{
            console.log('Error while creating app manager database connection in step3');
            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
            let response = 
            {
                nextStep:'End',
                input:{'status':inputStatus},
                message:'Error in creating app manager database connection in step3.'
            };
            return response;
         }
    }
    catch(e)
    {
        console.log("Error in processFileTransferActiveEmployee function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        organizationDbConnection?organizationDbConnection.destroy():null; 
        let response ={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Error Occured in processFileTransferActiveEmployee.'
        };
        return response;
    }
}

async function getEmployeeFromOrgTable(organizationDbConnection,status)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeLevelFileTransferSummarizationManager)
            .pluck('Employee_Id')
            .where('Status',status)
            .then(empId=>{
                return empId;
            })
            .catch(e=>{
                console.log('Error in getOpenEmployeeFromOrgTable function .catch block.', e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log('Error in getOpenEmployeeFromOrgTable function main catch block.', e);
        return false;
    }
}


 // function to update in employeeLevelFileTransferSummarizationManager`
 async function updateInEmployeeLevelFileTransferSummarizationManagerTable(organizationDbConnection,updateParams,employeeId){
    try{
        return(
            organizationDbConnection(ehrTables.employeeLevelFileTransferSummarizationManager)
            .update(updateParams)
            .where('Employee_Id',employeeId)
            .then((updatedData) => {
                return true;
            })
            .catch(catchError=>{
                console.log('Error in updateInEmployeeLevelFileTransferSummarizationManagerTable .catch block.', catchError);
                return false;
            })
        );
    }
    catch(error){
        console.log('Error in updateInEmployeeLevelFileTransferSummarizationManagerTable function main catch block',error);
        return false;
    }    
}

async function getMaxActivityDate(organizationDbConnection,employeeId)
{
    try{
        return(
            organizationDbConnection(ehrTables.fileAndWebEventSummary)
            .max('Activity_Date as maxActivityDate')
            .where('Employee_Id',employeeId)
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log("Error in getMaxActivityDate function .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getMaxActivityDate function .catch block",e);
        return false;
    }
}




async function getSummaryDataForFileAndWebEvents(organizationDbConnection,minDateTime,maxDateTime,employeeId,activityDate,currentUtcDate)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeFileTransferDetails)
            .select("Employee_Id as Employee_Id",
            organizationDbConnection.raw('sum(case when Action = "Create"  then 1 else 0 end) as Create_Count'),
            organizationDbConnection.raw('sum(case when Action = "Rename"  then 1 else 0 end) as Rename_Count'),
            organizationDbConnection.raw('sum(case when Action = "Delete"  then 1 else 0 end) as Delete_Count'),
            organizationDbConnection.raw('sum(case when Action = "Write" then 1 else 0 end) as Write_Count'),
            organizationDbConnection.raw('sum(case when Action = "Access"  then 1 else 0 end) as Access_Count'),
            organizationDbConnection.raw('sum(case when Action = "Upload"  then 1 else 0 end) as Upload_Count'),
            organizationDbConnection.raw('sum(case when Action = "Download"  then 1 else 0 end) as Download_Count'),
            organizationDbConnection.raw('sum(case when Action = "Copy" then 1 else 0 end) as Copy_Count')
            )
            .where("Action_Time",'>=',minDateTime)
            .andWhere("Action_Time",'<=',maxDateTime)
            .andWhere('Employee_Id',employeeId)
            .groupBy('Employee_Id')
            .then(data=>{
                for(let i=0;i<data.length;i++)
                {
                    data[i]["Activity_Date"]=activityDate;
                    data[i]["Load_Timestamp"]=currentUtcDate;
                }
                return data;
            })
            .catch(e=>{
                console.log("Error in getSummaryDataForFileAndWebEvents function .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getSummaryDataForFileAndWebEvents function main catch block",e);
        return false;
    }
}

async function getFileExtensionSummary(organizationDbConnection,minDateTime,maxDateTime,employeeId,activityDate,currentUtcDate)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeFileTransferDetails)
            .select({Employee_Id:"EFTD.Employee_Id",File_Extension:"EFTD.File_Extension"
            })
            .count("EFTD.File_Extension as File_Extension_Count")
            .from(ehrTables.employeeFileTransferDetails + " as EFTD")
            .where("EFTD.Action_Time",'>=',minDateTime)
            .where("EFTD.Action_Time",'<=',maxDateTime)
            .where('EFTD.Employee_Id',employeeId)
            .whereNotNull("EFTD.File_Extension")
            .groupBy('EFTD.File_Extension')
            .then(data=>{
                for(let i=0;i<data.length;i++)
                {
                    data[i]["Activity_Date"]=activityDate;
                    data[i]["Load_Timestamp"]=currentUtcDate;
                }
                return data;
            })
            .catch(e=>{
                console.log("Error in getFileExtensionSummary() function .catch block.",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getFileExtensionSummary() function main catch block.",e);
        return false;
    }
}

async function getWebFileEventSummary(organizationDbConnection,minDateTime,maxDateTime,employeeId,activityDate,currentUtcDate)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeFileTransferDetails)
            .select({Employee_Id:"EFTD.Employee_Id",Domain_Name:"EFTD.Domain_Name",
            Upload_Count:organizationDbConnection.raw('sum(case when EFTD.Action = "Upload"  then 1 else 0 end)'),
            Download_Count:organizationDbConnection.raw('sum(case when EFTD.Action = "Download"  then 1 else 0 end)')
            })
            .from(ehrTables.employeeFileTransferDetails + " as EFTD")
            .where("EFTD.Action_Time",'>=',minDateTime)
            .where("EFTD.Action_Time",'<=',maxDateTime)
            .where('EFTD.Employee_Id',employeeId)
            .whereNotNull("EFTD.Domain_Name")
            .groupBy('EFTD.Domain_Name')
            .then(data=>{
                for(let i=0;i<data.length;i++)
                {
                    data[i]["Activity_Date"]=activityDate;
                    data[i]["Load_Timestamp"]=currentUtcDate;
                }
                return data;
            })
            .catch(e=>{
                console.log("Error in getWebFileEventSummary() function .catch block.",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getWebFileEventSummary() function main catch block.",e);
        return false;
    }
}

async function insertIntoTables(organizationDbConnection,fileAndWebEventSummary,fileExtensionSummary,webEventSummary,fileTransferDetailsAsPerWs)
{
    try{
        return(
            organizationDbConnection
            .transaction(function(trx){
                return(
                    organizationDbConnection(ehrTables.fileAndWebEventSummary)
                    .insert(fileAndWebEventSummary)
                    .transacting(trx)
                    .then(async()=>
                        {
                            let extensionSummary=fileExtensionSummary && fileExtensionSummary.length>0 ? await insertFileExtensionSummary(organizationDbConnection,fileExtensionSummary,trx):true;
                            let webSummary = webEventSummary && webEventSummary.length>0 ? await insertWebEventSummary(organizationDbConnection,webEventSummary,trx) : true;
                            let fileTransferDetails=fileTransferDetailsAsPerWs && fileTransferDetailsAsPerWs.length>0 ? await insertFileTransferDetailsAsPerWs(organizationDbConnection,fileTransferDetailsAsPerWs,trx):true;
                            if(!extensionSummary || !webSummary || !fileTransferDetails)
                            {
                                throw("Error while inserting fileAndWebEventSummary,fileExtensionSummary,fileTransferDetailsAsPerWs.")
                            }
                        }
                    )
                )
                
        })
        .then(()=>{
            return true;
        })
        .catch((e)=>{
            console.log("Error in insertIntoTables() function .catch block",e);
            return false;
        })
        )
    }
    catch(e)
    {
        console.log("Error in insertIntoTables() function main catch block",e);
        return false;
    }
}

async function insertFileExtensionSummary(organizationDbConnection,fileExtensionSummary,trx)
{
    try{
        return(
            organizationDbConnection(ehrTables.fileExtensionSummary)
            .insert(fileExtensionSummary)
            .transacting(trx)
            .then(data=>{
                return true;
            })
            .catch(e=>{
                console.log("Error in insertFileExtensionSummary() function .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in insertFileExtensionSummary() function main catch block",e)
        return false;
    }
}

async function insertWebEventSummary(organizationDbConnection,webEventSummary,trx)
{
    try{
        return(
            organizationDbConnection(ehrTables.webEventSummary)
            .insert(webEventSummary)
            .transacting(trx)
            .then(data=>{
                return true;
            })
            .catch(e=>{
                console.log("Error in insertWebEventSummary() function .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in insertWebEventSummary() function main catch block",e)
        return false;
    }
}

async function insertFileTransferDetailsAsPerWs(organizationDbConnection,fileTransferDetailsAsPerWs,trx)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeFileTransferDetailsAsPerWs)
            .insert(fileTransferDetailsAsPerWs)
            .transacting(trx)
            .then(data=>{
                return true;
            })
            .catch(e=>{
                console.log("Error in insertFileTransferDetailsAsPerWss() function .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in insertFileTransferDetailsAsPerWs() function main catch block",e)
        return false;
    }
}

async function getFileTransferDetailsAsPerWs(organizationDbConnection,minDateTime,maxDateTime,employeeId,currentUtcDate,timeZone,offSet)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeFileTransferDetails)
            .select("*")
            .where("Action_Time",">=",minDateTime)
            .andWhere("Action_Time","<=",maxDateTime)
            .andWhere("Employee_Id",employeeId)
            .then(async(data)=>{
                if(data.length>0)
                {
                    let computerName=await getAssestName(organizationDbConnection,employeeId);
                    if(computerName.length>0)
                    {
                        computerName=computerName[0]["Computer_Name"];
                    }
                    else{
                        computerName='';
                    }
                    for(let i=0;i<data.length;i++)
                    {
                        data[i]['Computer_Name']=computerName;
                        data[i]['TimeZone_Id']=timeZone;
                        data[i]['Action_Time_Based_On_Time_Zone']=await getDateTimeBasedOnTimeZoneOffset(data[i]['Action_Time'], offSet)
                        data[i]['Load_Timestamp']=currentUtcDate;
                    }
                }
                return data;
                
            })
            .catch(e=>{
                console.log("Error in getFileTransferDetailsAsPerWs() function .catch block.",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getFileTransferDetailsAsPerWs() function main catch block.",e);
        return false;
    }
}


async function getAssestName(organizationDbConnection,employeeId)
{
    try{
        let subQuery=organizationDbConnection(ehrTables.assetManagement).select('Computer_Name').where("Employee_Id",employeeId).groupBy("Employee_Id");
        let subQuery1=organizationDbConnection(ehrTables.assetManagement).select('Computer_Name').where("Employee_Id",employeeId).groupBy("Employee_Id");
        let assetDetails=await subQuery.where("Status","Working").then(data=>{
            return data;
        })
        .catch(e=>{
            console.log("Error in getAssestName with status working .catch block.")
            return false;
        })
        if(!assetDetails)
        {
            return false;
        }
        if(assetDetails && assetDetails.length>0)
        {
            return assetDetails;
        }
        else
        {
            return(
               await subQuery1
                .then(data=>{
                  return data;
                })
                .catch(e=>{
                    console.log("Error in getAssestName .catch block.",e)
                    return false;
                })
            )
        }
    }
    catch(e)
    {
        console.log("Error in getAssestName() function main catch block.",e);
        return false;
    }
}


async function getMinActivityDate(organizationDbConnection,employeeId)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeFileTransferDetails)
            .select(organizationDbConnection.raw('MIN( CAST( `Action_Time` AS Date) ) as minActivityDate'))
            .where('Employee_Id',employeeId)
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log("Error in getMinActivityDate function .catch block",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in getMinActivityDate function .catch block",e);
        return false;
    }
}


