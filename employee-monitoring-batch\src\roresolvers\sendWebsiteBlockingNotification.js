const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const {getTemplateData, sendEmailWithReportAttachment} = require('./emailCommonFunctions');
const AWS = require("aws-sdk");
const ses = new AWS.SES({ region: process.env.sesTemplatesRegion });
const { ApolloError } = require('apollo-server-lambda');
const nodemailer = require('nodemailer');
const moment = require('moment-timezone');
const { ehrTables } = require('../common/tableAlias');

module.exports.sendWebsiteBlockingNotification = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log('Inside sendWebsiteBlockingNotification');

        organizationDbConnection = knex(context.connection.OrganizationDb);
        const employeeName = await organizationDbConnection(ehrTables.empPersonalInfo)
                .select(organizationDbConnection.raw("CONCAT_WS(' ',Emp_First_Name,Emp_Middle_Name, Emp_Last_Name) as employeeName"))
                .from(ehrTables.empPersonalInfo)
                .where('Employee_Id', args.employeeId)
                .then(async(getData) => {
                    return getData && getData.length > 0 ? getData[0].employeeName : "";
                }).catch(catchError => {
                    console.log('Error in getting employee name', catchError);
                    return "";
                });
        
        let mailContent = "";
        let subject = ""
        if(args.serviceStatus && args.serviceStatus.toLowerCase() == 'running'){
            mailContent = "Our application has detected that there has been an override of the inetrnet access control settings. <p>Org_Code: "+context.Org_Code+"</p><p> Employee Name: "+employeeName+" </p><p>Employee Id: "+args.employeeId+"</p>";
            subject = "User override detected in Internet access Configuration";
        } else {
            mailContent = "Our application has detected that the Windows service is not running on the user system. <p>Org_Code: "+context.Org_Code+"</p><p> Employee Name: "+employeeName+" </p><p>Employee Id: "+args.employeeId+"</p>";
            args.fileContent = "";
            subject = "Service not running";
        }

        const inputParams = {
            mailContent: mailContent,
            pdfData: args.fileContent,
            emailIdList: ["<EMAIL>","<EMAIL>"], //"<EMAIL>"
            redirectionURL: "",
            filename: `Internet access configuration file - ${moment().format('ddd MMM DD YYYY HH:mm:ss')}`,
        };

        const emailTemplateData = await getTemplateData(inputParams);
        const mailList = inputParams.emailIdList;

        if (emailTemplateData.htmlToSend && mailList.length > 0) {
            const numberOfMailToBeTriggered = Math.min(mailList.length, 50);
            const destinationArray = mailList.slice(0, numberOfMailToBeTriggered);

            const mailOptions = {
                from: process.env.emailFrom,
                html: emailTemplateData.htmlToSend,
                bcc: destinationArray.join(),
                attachments: emailTemplateData.attachmentsDetails,
                subject: subject
            };

            const transporter = nodemailer.createTransport({ SES: ses });
            const sendEmail = await sendEmailWithReportAttachment(transporter, mailOptions);

            if (sendEmail === 'success') {
                return { errorCode: '', message: 'Dashboard email sent successfully.' };
            } else {
                throw 'EM0277';
            }
        }

        organizationDbConnection?organizationDbConnection.destroy():null;
        return { errorCode: '', message: 'Notification email sent successfully.' };

    } catch (e) {
        organizationDbConnection?organizationDbConnection.destroy():null;
        console.log('Error in sendDashboardEmail function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'EM0277');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

