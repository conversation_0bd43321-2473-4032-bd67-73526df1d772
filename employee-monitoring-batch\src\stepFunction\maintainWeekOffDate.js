// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// Organization database connection
const knex = require('knex');
// require table alias
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { defaultValues } = require('../common/appConstants');
const moment = require('moment-timezone');
const { updateSummaryDataInMasterTable }=require('./commonFunctions');

//main function to maintain week of date according to workschedule.
module.exports.maintainWeekOffDate  = async(event, context) =>{
    let appmanagerDbConnection;
    let organizationDbConnection;
    try{
        let inputData=event.input;
        let status=inputData.status;
        let instance;
        let response = 
        {
            nextStep:'End',
            message:'Error Occured in updateSummaryDataInMasterTable while updating weekOffDatesManager table.'
        }
       let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1); 
       if(Object.keys(connection).length){
            // form app manager database connection 
            appmanagerDbConnection=knex(connection.AppManagerDb);
            //geting open instances or failed instances based on status that need to be proceed further
            instance=await getOpenOrFailedInstances(appmanagerDbConnection,status);
        }
        else{
            console.log("Error while getting database connection in maintainWeekOfDate");
            response = 
            {
                nextStep:'End',
                message:'Error while getting database connection.'
            };
            return response;
        }
        if(instance.length>0)
        {
           //getting start date in format of year and month only because we are adding day  mannualy by increasing the day by one 
            let startDate=moment.utc().add(1,'days').format('YYYY-MM-DD');
            //gives the last date of the month and then we add one more day
            let lastDateOfMonth=moment(startDate).endOf('month').add(1,'days').format('YYYY-MM-DD');
            //number of instances should be processed in one call
            let instanceToBeProcessed=(instance.length>defaultValues.activeInstanceToBeProcessed)?(defaultValues.activeInstanceToBeProcessed):(instance.length);
            //this function will return weekend date according dayid and weekofmonth
            let dayWeekOfMonthDate=await commonLib.func.getDayWeekOfMonthFromDate(startDate,lastDateOfMonth);
            if(dayWeekOfMonthDate.length)
            {
                for(let i=0;i<instanceToBeProcessed;i++)
                {   
                    let updateMasterTableStatus;
                    let orgCode=instance[i]['Org_Code']
                    
                    let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
                    if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                        let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                        
                        
                        let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);

                        if(Object.keys(connection).length)
                        {   organizationDbConnection=knex(connection.OrganizationDb);
                            //function to update weekoff_dates table
                            let check=await commonLib.func.updateWeekOffDate(organizationDbConnection,dayWeekOfMonthDate);
                            organizationDbConnection?organizationDbConnection.destroy():null;
                            if(check)
                            {
                                let updateParams={
                                    Update_Status : 'Success'
                                }
                             updateMasterTableStatus=await updateSummaryDataInMasterTable(appmanagerDbConnection,updateParams,orgCode,appManagerTables.weekOffDatesManager);
                            }
                            else
                            {
                                let updateParams={
                                    Update_Status : 'Failed'
                                }
                                updateMasterTableStatus=await updateSummaryDataInMasterTable(appmanagerDbConnection,updateParams,orgCode,appManagerTables.weekOffDatesManager);
                            }
                            
                        }
                        else{
                            let updateParams={
                                Update_Status : 'Failed'
                            }
                            updateMasterTableStatus=await updateSummaryDataInMasterTable(appmanagerDbConnection,updateParams,orgCode,appManagerTables.weekOffDatesManager);
                        }
                        if(updateMasterTableStatus==="success")
                        {
                            console.log("weekOffDatesManager table updated.")
                        }
                        else{
                            console.log("Error Occured in updateSummaryDataInMasterTable while updating weekOffDatesManager table")
                            response = 
                            {
                                nextStep:'End',
                                message:'Error Occured in updateSummaryDataInMasterTable while updating weekOffDatesManager table.'
                            };
                        }
                    } else {
                        let updateParams={
                            Update_Status : 'Failed'
                        }
                        updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.weekOffDatesManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);

                        if(updateMasterTableStatus==="success")
                        {
                            console.log("weekOffDatesManager table updated.")
                        }
                        else{
                            console.log("Error Occured in updateSummaryDataInMasterTable while updating weekOffDatesManager table")
                            response = 
                            {
                                nextStep:'End',
                                message:'Error Occured in updateSummaryDataInMasterTable while updating weekOffDatesManager table.'
                            };
                        }

                    }
                        
                }
                response = 
                {
                    nextStep:'End',
                    message:'Update weekOffDates process Completed.'
                };
            }
            else{
                console.log("Error Occured while getting data from getDayWeekOfMonthFromDate");
                response = 
                {
                    nextStep:'End',
                    message:'Error Occured while getting data from getDayWeekOfMonthFromDate.'
                };
            }
            
        }
        else{
            console.log("No instances Found");
            response = 
            {
                nextStep:'End',
                message:'Update weekOffDates process Completed.'
            };
        }
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        return response;      
    }
    catch(e)
    {
    console.log("error occure while doing the update process in maintain week of data",e);
    appmanagerDbConnection?appmanagerDbConnection.destroy():null;
    organizationDbConnection?organizationDbConnection.destroy():null;
    let response = {
                nextStep:'End',
                message:'Error occured while doing the update process in maintain week of data'
            };
        return response;
    }
};

//function to get open or failed instances from weekoff_dates_manager table
async function getOpenOrFailedInstances(appmanagerDbConnection,status)
{
    try{
        return(     
        appmanagerDbConnection(appManagerTables.weekOffDatesManager)
        .select('Org_Code')
        .where('Update_Status',status)
        .then(async (instancesData) =>{
            return instancesData;
        })
        .catch((e)=>{
            console.log("error while geting Open/closed Instanaces from maintainWeekOffDate",e);
            return "";
        })
        )
    }
    catch(e){
        console.log("error in get Open/closed Instances from maintainWeekOffDate",e);
        return "";
    }
}



