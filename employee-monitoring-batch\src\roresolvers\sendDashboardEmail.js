const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const AWS = require("aws-sdk");
const ses = new AWS.SES({ region: process.env.sesTemplatesRegion });
const { ApolloError } = require('apollo-server-lambda');
const nodemailer = require('nodemailer');
const moment = require('moment-timezone');
const { getTemplateData, sendEmailWithReportAttachment } = require('./emailCommonFunctions');

module.exports.sendDashboardEmail = async (parent, args, context, info) => {
    try {
        const redirectionURL = "https://" + context.Org_Code + "." + process.env.domainName + process.env.webAddress + "/in/productivity-monitoring/activity-dashboard";
        const inputParams = {
            mailContent: args.body,
            pdfData: args.pdfData,
            emailIdList: args.emailIdArray,
            redirectionURL: redirectionURL,
            fileName: `Productivity Insights - ${moment().format('ddd MMM DD YYYY HH:mm:ss')}.pdf`,
        };

        const emailTemplateData = await getTemplateData(inputParams);
        const mailList = inputParams.emailIdList;

        if (emailTemplateData.htmlToSend && mailList.length > 0) {
            const numberOfMailToBeTriggered = Math.min(mailList.length, 50);
            const destinationArray = mailList.slice(0, numberOfMailToBeTriggered);

            const mailOptions = {
                from: process.env.emailFrom,
                html: emailTemplateData.htmlToSend,
                bcc: destinationArray.join(),
                attachments: emailTemplateData.attachmentsDetails,
                subject: "Productivity Monitoring Insights"
            };

            const transporter = nodemailer.createTransport({ SES: ses });
            const sendEmail = await sendEmailWithReportAttachment(transporter, mailOptions);

            if (sendEmail === 'success') {
                return { errorCode: '', message: 'Dashboard email sent successfully.' };
            } else {
                throw 'EM0277';
            }
        }
    } catch (e) {
        console.log('Error in sendDashboardEmail function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'EM0277');
        throw new ApolloError(errResult.message, errResult.code);
    }
};

