const { ehrTables } = require('../common/tableAlias');

async function getJobPostDetails(organizationDbConnection, startDate, endDate){
    return organizationDbConnection( ehrTables.jobPost + ' as JP')
    .select('JP.Job_Post_Id',  'JP.Job_Post_Name as Job_Title',  'JP.Experience_Level', 'JP.Posting_Date', 'JP.Closing_Date', 'JP.Job_Description',
        'DES.Designation_Name as Position', 'AST.Status as Job_Post_Status', 'OG.Organization_Group as Core_Group',  
        'SP.Service_Provider_Name  as Company_Name', 'CEG.Group_Name as Custom_Group', 'SFWPOS.Pos_Name as Recruiter_Groups', 'DEP.Department_Name as Department', 
        'COU.Country_Name', 'ST.State_Name', 'CI.City_Name', 'JP.No_Of_Vacancies', 'JP.Work_Place_Type as WorkPlace_Type', 
        'ET.Employee_Type as Job_Type', 'CUR.Currency_Name as Currency', organizationDbConnection.raw(" CASE WHEN JP.Skill_Set IS NOT NULL THEN REPLACE(REPLACE(REPLACE(JP.Skill_Set, '[\"', ''), '\"]', ''), '\", \"', ',') ELSE '' END as Key_Skills"), 'JP.Min_Work_Experience as Min_Experience_Range', 
        'JP.Max_Work_Experience as Max_Experience_Range' , 'JP.Min_Payment_Frequency as Min_Salary_Range', 'JP.Max_Payment_Frequency as Max_Salary_Range', 
        organizationDbConnection.raw("GROUP_CONCAT(distinct CD.Course_Name) as Educational_Attainment"), 
        organizationDbConnection.raw("GROUP_CONCAT(distinct ROU.Round_Name) as Rounds"),  
        organizationDbConnection.raw("CASE WHEN JP.Travel_Required=1 THEN 'Yes' else 'No' END as Field_Work"),
        'JP.Cooling_Period', 'JP.Hiring_Manager_Name as Hiring_Manager', 'JP.Reason_For_Opening as Reason_For_Vacancy', 'WO.Workflow_Name as Workflow',
        'JP.Added_On', 'EJ.User_Defined_EmpId as Added_By_Id' , organizationDbConnection.raw("CONCAT_WS(' ',EMP1.Emp_First_Name,EMP1.Emp_Middle_Name, EMP1.Emp_Last_Name) as Added_By_Name") 
    )
    .leftJoin( ehrTables.organizationGroup + ' as OG', 'OG.Organization_Group_Id', 'JP.Organization_Group_Id')
    .leftJoin( ehrTables.serviceProvider + ' as SP', 'SP.Service_Provider_Id', 'JP.Service_Provider_Id')
    .leftJoin( ehrTables.atsStatusTable + ' as AST', 'AST.Id', 'JP.Status')
    .leftJoin( ehrTables.customEmployeeGroup + ' as CEG', 'CEG.Group_Id', 'JP.Custom_Group_Id')
    .leftJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Organization_Structure_Id', 'JP.Organization_Structure_Id')
    .leftJoin( ehrTables.department + ' as DEP', 'DEP.Department_Id', 'JP.Functional_Area')
    .leftJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'JP.Designation')
    .leftJoin( ehrTables.city + ' as CI', 'CI.City_Id', 'JP.City_Id')
    .leftJoin( ehrTables.state + ' as ST', 'ST.State_Id', 'JP.State_Id')
    .leftJoin( ehrTables.country + ' as COU', 'COU.Country_Code', 'JP.Country_Code')
    .leftJoin( ehrTables.empType + ' as ET', 'ET.EmpType_Id', 'JP.Job_Type')
    .leftJoin( ehrTables.currency + ' as CUR', 'CUR.Currency_Id', 'JP.Currency')
    .leftJoin( ehrTables.jobPostQualifications + ' as JPQ', 'JPQ.Job_Post_Id', 'JP.Job_Post_Id')
    .leftJoin( ehrTables.courseDetails + ' as CD', 'CD.Course_Id', 'JPQ.Qualification')
    .leftJoin( ehrTables.jobPostRounds + ' as JPR', 'JPR.Job_Post_Id', 'JP.Job_Post_Id')
    .leftJoin( ehrTables.rounds + ' as ROU', 'ROU.Round_Id', 'JPR.Round_Id')
    .leftJoin( ehrTables.workflows + ' as WO', 'WO.Workflow_Id', 'JP.Jobpost_Workflow')
    .leftJoin( ehrTables.empPersonalInfo + ' as EMP1', 'EMP1.Employee_Id', 'JP.Added_By')
    .leftJoin( ehrTables.empJob + ' as EJ' , 'EJ.Employee_Id', 'EMP1.Employee_Id')
    .groupBy('JP.Job_Post_Id')
    .orderBy('JP.Job_Post_Id', 'asc');
}


async function getJobPostRecruiters(organizationDbConnection, startDate, endDate){
    return organizationDbConnection( ehrTables.jobPostRecruiters + ' as JPR')
    .select( 'JPR.Job_Post_Id', 'EJ.User_Defined_EmpId as Recruiter_Id', organizationDbConnection.raw("CONCAT_WS(' ', EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name) as Recruiter"), 
    'EJ.User_Defined_EmpId as Job_Owner_Id', organizationDbConnection.raw("CONCAT_WS(' ', EMP1.Emp_First_Name, EMP1.Emp_Middle_Name, EMP1.Emp_Last_Name) as Job_Owner"))
    .innerJoin( ehrTables.empPersonalInfo + ' as EMP', 'EMP.Employee_Id', 'JPR.Recruiter_Id')
    .leftJoin( ehrTables.empJob + ' as EJ' , 'EJ.Employee_Id', 'EMP.Employee_Id')
    .leftJoin( ehrTables.jobPost + ' as JP', 'JPR.Job_Post_Id', 'JP.Job_Post_Id')
    .leftJoin( ehrTables.empPersonalInfo + ' as EMP1', 'EMP1.Employee_Id', 'JP.Added_By')
    .leftJoin( ehrTables.empJob + ' as EJ1' , 'EJ1.Employee_Id', 'EMP1.Employee_Id')
    .orderBy('JPR.Job_Post_Id', 'asc');
}

async function getJobPostPanelMembers(organizationDbConnection, startDate, endDate){
    return organizationDbConnection( ehrTables.jobPostPanelMembers + ' as JPPM')
    .select('JPPM.Job_Post_Id', 'EJ.User_Defined_EmpId as Panel_Member_Id', organizationDbConnection.raw("CONCAT_WS(' ', EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name) as Panel_Member"))
    .innerJoin( ehrTables.empPersonalInfo + ' as EMP', 'EMP.Employee_Id', 'JPPM.Panel_Member_Id')
    .leftJoin( ehrTables.empJob + ' as EJ' , 'EJ.Employee_Id', 'EMP.Employee_Id')
    .orderBy('JPPM.Job_Post_Id', 'asc');

}

async function getJobPostHiringManager(organizationDbConnection, startDate, endDate){  
    return organizationDbConnection( ehrTables.jobPostHiringManagers + ' as JPHM')
    .select('JPHM.Job_Post_Id', 'EJ.User_Defined_EmpId as Hiring_Manager_Id', organizationDbConnection.raw("CONCAT_WS(' ', EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name) as Hiring_Manager"))
    .innerJoin( ehrTables.empPersonalInfo + ' as EMP', 'EMP.Employee_Id', 'JPHM.Hiring_Manager_Id')
    .leftJoin( ehrTables.empJob + ' as EJ' , 'EJ.Employee_Id', 'EMP.Employee_Id')
    .orderBy('JPHM.Job_Post_Id', 'asc');

}

async function getJobPostOnboardingSpecialList(organizationDbConnection, startDate, endDate){  
    return organizationDbConnection( ehrTables.jobPostOnboardSpecialist + ' as JPOS')
    .select('JPOS.Job_Post_Id', 'EJ.User_Defined_EmpId as Onboard_Specialist_Id', organizationDbConnection.raw("CONCAT_WS(' ', EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name) as Onboard_Specialist"))
    .innerJoin( ehrTables.empPersonalInfo + ' as EMP', 'EMP.Employee_Id', 'JPOS.Onboard_Specialist_Id')
    .leftJoin( ehrTables.empJob + ' as EJ' , 'EJ.Employee_Id', 'EMP.Employee_Id')
    .orderBy('JPOS.Job_Post_Id', 'asc');

}


async function getCandidateDetails(organizationDbConnection, startDate, endDate){
    return organizationDbConnection( ehrTables.candidatePersonalInfo + ' as CPI')
    .select('CPI.Candidate_Id', 'CPI.Emp_First_Name as First_Name', 'CPI.Emp_Middle_Name as Middle_Name', 'CPI.Emp_Last_Name as Last_Name', 
        'CPI.DOB', 'CPI.Gender', 'CPI.Salutation', 'CPI.Pronoun', 'CPI.Gender_Orientations',
        organizationDbConnection.raw("GROUP_CONCAT(distinct LAN.Language_Name) as Languages_Known"),
        'MS.Marital_Status as Marital_Status_Name', 'NATION.Nationality as Nationality', 'CRI.Source as Source_Of_Application', 'JP.Job_Post_Name as Job_Title',
        organizationDbConnection.raw("GROUP_CONCAT(distinct LOC.Location_Name) as Preferred_Location"),
        organizationDbConnection.raw("CASE WHEN CRI.Skill_Set IS NOT NULL THEN REPLACE(REPLACE(REPLACE(CRI.Skill_Set, '[\"', ''), '\"]', ''), '\", \"', ',') ELSE '' END as Skill_Set"),
        'CRI.Current_Employer', 'CRI.Notice_Period as Availability_To_Join', 'CRI.Total_Experience_In_Years', 'CRI.Total_Experience_In_Months',
        'CRI.Current_CTC as Current_Basic_Salary', 'CRI.Expected_CTC as Expected_Basic_Salary', 'CUR.Currency_Name', 
        'CR.Verifier_Name as Name_of_Character_Reference', 'CR.Verifier_Phone_Number as Character_Reference_Mobile_Number', 'CR.Verifier_Email_Id as Character_Reference_Email_Address',
        'CCD.pApartment_Name as street1', 'CCD.pStreet_Name as street2', 'CCD.pCity as city', 'CCD.pState as state', 'CCD.pCountry as country', 'CCD.pPincode as zipcode',
        'CCD.Mobile_No', 'CPI.Personal_Email', 'CRI.Resume', 'CPI.Statutory_Insurance_Number', 'CPI.PRAN_No', 'AST.Status as Candidate_Status'
    )
    .leftJoin( ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
    .leftJoin( ehrTables.atsStatusTable + ' as AST', 'AST.Id', 'CRI.Candidate_Status')
    .leftJoin( ehrTables.jobPost + ' as JP', 'JP.Job_Post_Id', 'CRI.Job_Post_Id')
    .leftJoin( ehrTables.currency + ' as CUR', 'CUR.Currency_Id', 'CRI.Currency')
    .leftJoin( ehrTables.candidateContactDetails + ' as CCD', 'CCD.Candidate_Id', 'CPI.Candidate_Id')
    .leftJoin( ehrTables.candidateLanguage + ' as CL', 'CL.Candidate_Id', 'CPI.Candidate_Id')
    .leftJoin( ehrTables.languages + ' as LAN', 'LAN.Lang_Id', 'CL.Lang_Known')
    .leftJoin( ehrTables.candidatePreferedJobLocation + ' as CPJL', 'CPJL.Candidate_Id', 'CPI.Candidate_Id')
    .leftJoin( ehrTables.location + ' as LOC', 'LOC.Location_Id', 'CPJL.Preferred_Location')
    .leftJoin( ehrTables.nationality + ' as NATION', 'NATION.Nationality_Id', 'CPI.Nationality_Id')
    .leftJoin( ehrTables.maritalStatus + ' as MS', 'MS.Marital_Status_Id', 'CPI.Marital_Status')
    .leftJoin( ehrTables.candidateReference + ' as CR', 'CR.Candidate_Id', 'CPI.Candidate_Id')
    .groupBy('CPI.Candidate_Id')
    .orderBy('CPI.Candidate_Id', 'asc');
}

async function getCandidateEducation(organizationDbConnection, startDate, endDate){
    return organizationDbConnection( ehrTables.candidateEducation + ' as CE')
    .select('CE.Candidate_Id','CD.Course_Name as Education_Type','CE.Specialisation',
        'CE.Institute_Name', 'CE.Year_Of_Passing', 'CE.Percentage')
    .leftJoin( ehrTables.courseDetails + ' as CD', 'CD.Course_Id', 'CE.Education_Type')
    .orderBy('CE.Candidate_Id', 'asc');
}

async function getCandidateExperience(organizationDbConnection, startDate, endDate){
    return organizationDbConnection( ehrTables.candidateExperience + ' as CE')
    .select('CE.Candidate_Id', 'CE.Prev_Company_Name', 'CE.Prev_Company_Location', 'CE.Designation',
        'CE.Start_Date', 'CE.End_Date', 'CE.Duration', 'CE.Years', 'CE.Months')
    .orderBy('CE.Candidate_Id', 'asc');
}

async function getCandidateCertification(organizationDbConnection, startDate, endDate) {
    return organizationDbConnection( ehrTables.candidateCertifications + ' as CC')
    .select('CC.Candidate_Id','CC.Certification_Name', 'CC.Received_Date', 'CC.Certificate_Received_From')
    .orderBy('CC.Candidate_Id', 'asc');
}


async function getCandidateRecruitmentInfo(organizationDbConnection, startDate, endDate) {
    return organizationDbConnection( ehrTables.candidateRecruitmentInfo )
    .select('candidate_recruitment_info.Candidate_Id', 'candidate_recruitment_info.Job_Post_Id', 'candidate_recruitment_info.Current_Employer', 
        organizationDbConnection.raw("CASE WHEN candidate_recruitment_info.Skill_Set IS NOT NULL THEN REPLACE(REPLACE(REPLACE(candidate_recruitment_info.Skill_Set, '[\"', ''), '\"]', ''), '\", \"', ',') ELSE '' END as Skill_Set"),
        organizationDbConnection.raw(" CASE WHEN candidate_recruitment_info.Applicant_Ranking IS NOT NULL THEN (candidate_recruitment_info.Applicant_Ranking * 100) ELSE 0 END as Job_Match_Score"),
        'candidate_recruitment_info.Notice_Period as Notice_Period_Days', 'candidate_recruitment_info.Total_Experience_In_Years', 'candidate_recruitment_info.Total_Experience_In_Months',
        'candidate_recruitment_info.Current_CTC', 'candidate_recruitment_info.Expected_CTC', 'currency.Currency_Name','ats_status_table.Id as Candidate_Status_Id', 'ats_status_table.Status as Candidate_Status',
        'candidate_recruitment_info.Shortlisting_Date', 'EJ2.User_Defined_EmpId as Shortlisted_By_Id', organizationDbConnection.raw("CONCAT_WS(' ', epi2.Emp_First_Name, epi2.Emp_Last_Name) as Shortlisted_By"),
        'candidate_recruitment_info.Hiring_Date', 'EJ1.User_Defined_EmpId as Hired_By_Id', organizationDbConnection.raw("CONCAT_WS(' ', epi1.Emp_First_Name, epi1.Emp_Last_Name) as Hired_By"),
        'candidate_recruitment_info.Added_On', 'EJ3.User_Defined_EmpId as Added_By_Id', organizationDbConnection.raw("CONCAT_WS(' ', epi3.Emp_First_Name, epi3.Emp_Last_Name) as Added_By"),
        'candidate_recruitment_info.Offer_Letter_Rolled_Out_Date', 'candidate_recruitment_info.Offer_Letter_Response_Date'
    )
    .leftJoin( ehrTables.empPersonalInfo + ' as epi1', 'epi1.Employee_Id', 'candidate_recruitment_info.Hired_By')
    .leftJoin( ehrTables.empJob + ' as EJ1' , 'EJ1.Employee_Id', 'epi1.Employee_Id')
    .leftJoin( ehrTables.empPersonalInfo + ' as epi2', 'epi2.Employee_Id', 'candidate_recruitment_info.Shortlisted_By')
    .leftJoin( ehrTables.empJob + ' as EJ2' , 'EJ2.Employee_Id', 'epi2.Employee_Id')
    .leftJoin( ehrTables.empPersonalInfo + ' as epi3', 'epi3.Employee_Id', 'candidate_recruitment_info.Added_By')
    .leftJoin( ehrTables.empJob + ' as EJ3' , 'EJ3.Employee_Id', 'epi3.Employee_Id')
    .leftJoin( ehrTables.currency , 'currency.Currency_Id', 'candidate_recruitment_info.Currency')
    .leftJoin(ehrTables.atsStatusTable, 'ats_status_table.Id', 'candidate_recruitment_info.Candidate_Status')
    .orderBy(['candidate_recruitment_info.Candidate_Id', 'candidate_recruitment_info.Job_Post_Id'], 'asc');
}


async function getCandidateInterview(organizationDbConnection, startDate, endDate) {
    return organizationDbConnection( ehrTables.interviewCandidates +' as IC')
    .select('IC.Candidate_Id', 'job_post.Job_Post_Id', 'job_post.Job_Post_Name as Job_Title',
        'interviews.Interview_Id', 'interviews.Interview_Name', 'interviews.Venue', 'interview_rounds.Round_Start_Date_Time',
        'interview_rounds.Round_End_Date_Time', 'interviews.Calendar_Link', 'ats_status_table.Status as Interview_Status',
        organizationDbConnection.raw("GROUP_CONCAT(distinct IC.Round_Id) as Round_Id"),
        organizationDbConnection.raw("GROUP_CONCAT(distinct rounds.Round_Name) as Round_Name")
    )
    .leftJoin(ehrTables.interviews, 'IC.Interview_Id', 'interviews.Interview_Id')
    .leftJoin(ehrTables.jobPost, 'interviews.Job_Post', 'job_post.Job_Post_Id')
    .leftJoin(ehrTables.rounds, 'IC.Round_Id', 'rounds.Round_Id')
    .leftJoin(ehrTables.interviewRounds , function() {
        this.on('IC.Interview_Id', '=', 'interview_rounds.Interview_Id')
            .andOn('IC.Round_Id', '=', 'interview_rounds.Round_Id');
    })
    .leftJoin(ehrTables.atsStatusTable, 'interviews.Status', 'ats_status_table.Id')
    .groupBy('IC.Interview_Id')
    .orderBy('IC.Candidate_Id')
    .orderBy('job_post.Job_Post_Id')
    .orderBy('interviews.Interview_Id');
}

async function getCandidateInterviewRound(organizationDbConnection, startDate, endDate) {
    return organizationDbConnection( ehrTables.interviewCandidatesRoundsScore )
    .select(
        'interview_candidates_rounds_score.Candidate_Id', 'interviews.Interview_Id', 'interviews.Interview_Name',
        'emp_job.User_Defined_EmpId as Panel_Member_Id',
        organizationDbConnection.raw("CONCAT_WS(' ', emp_personal_info.Emp_First_Name, emp_personal_info.Emp_Middle_Name, emp_personal_info.Emp_Last_Name) as Panel_Member_Name"),
        'designation.Designation_Name','rounds.Round_Id', 'rounds.Round_Name', 'interview_candidates_rounds_score.Total_Score',
        'interview_candidates_rounds_score.Status', 'interview_candidates_rounds_score.Rating', 'interview_candidates_rounds_score.Note'
    )
    .leftJoin( ehrTables.empPersonalInfo , 'emp_personal_info.Employee_Id', 'interview_candidates_rounds_score.Panel_member_Id')
    .leftJoin( ehrTables.interviews, 'interviews.Interview_Id', 'interview_candidates_rounds_score.Interview_Id')
    .leftJoin( ehrTables.rounds, 'rounds.Round_Id', 'interview_candidates_rounds_score.Round_Id')
    .leftJoin( ehrTables.empJob , 'emp_job.Employee_Id', 'emp_personal_info.Employee_Id')
    .leftJoin(ehrTables.designation , 'designation.Designation_Id', 'emp_job.Designation_Id')
    .orderBy([
        { column: 'interview_candidates_rounds_score.Candidate_Id', order: 'asc' },
        { column: 'interviews.Interview_Id', order: 'asc' }
    ]);
}

async function getInterviewPanelMemberScore(organizationDbConnection, startDate, endDate) {

    return organizationDbConnection(ehrTables.interviewCandidatesSkillsScore )
    .select(
        'interview_candidates_skills_score.Candidate_Id', 'interviews.Interview_Id', 'interviews.Interview_Name',
        'rounds.Round_Id', 'rounds.Round_Name',  'emp_job.User_Defined_EmpId as Panel_Member_Id',
        organizationDbConnection.raw("CONCAT_WS(' ', emp_personal_info.Emp_First_Name, emp_personal_info.Emp_Middle_Name, emp_personal_info.Emp_Last_Name) as Panel_member_Name"),
        'round_skills.Skill_Id', 'round_skills.Skill_Name', 'interview_candidates_skills_score.Skill_Score', 'interview_candidates_skills_score.Skill_Comment'
    )
    .leftJoin(ehrTables.interviews, 'interviews.Interview_Id', 'interview_candidates_skills_score.Interview_Id')
    .leftJoin(ehrTables.rounds, 'rounds.Round_Id', 'interview_candidates_skills_score.Round_Id')
    .leftJoin(ehrTables.roundSkills, 'round_skills.Skill_Id', 'interview_candidates_skills_score.Skill_Id')
    .leftJoin(ehrTables.empPersonalInfo , 'emp_personal_info.Employee_Id', 'interview_candidates_skills_score.Panel_member_Id')
    .leftJoin( ehrTables.empJob , 'emp_job.Employee_Id', 'emp_personal_info.Employee_Id')
    .orderBy([
        { column: 'interview_candidates_skills_score.Candidate_Id', order: 'asc' },
        { column: 'interviews.Interview_Id', order: 'asc' }
    ]);
}

module.exports.getCandidateInterview = getCandidateInterview;
module.exports.getCandidateInterviewRound = getCandidateInterviewRound;
module.exports.getInterviewPanelMemberScore =getInterviewPanelMemberScore;
module.exports.getCandidateDetails = getCandidateDetails;
module.exports.getCandidateEducation = getCandidateEducation;
module.exports.getCandidateExperience = getCandidateExperience;
module.exports.getCandidateCertification = getCandidateCertification;
module.exports.getCandidateRecruitmentInfo = getCandidateRecruitmentInfo;
module.exports.getJobPostDetails = getJobPostDetails
module.exports.getJobPostRecruiters = getJobPostRecruiters
module.exports.getJobPostPanelMembers = getJobPostPanelMembers
module.exports.getJobPostHiringManager = getJobPostHiringManager
module.exports.getJobPostOnboardingSpecialList = getJobPostOnboardingSpecialList


