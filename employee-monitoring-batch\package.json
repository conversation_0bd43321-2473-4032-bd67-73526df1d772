{"name": "employee-monitoring", "version": "1.0.0", "description": "employee monitoring node modules", "main": "handler.js", "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "@google-cloud/storage": "^7.0.1", "@haftahave/serverless-ses-template": "^1.3.3", "apollo-server": "^2.14.4", "apollo-server-lambda": "^2.14.4", "graphql": "^14.2.0", "handlebars": "^4.7.7", "html-entities": "^2.3.2", "nodemailer": "^6.7.2", "path": "^0.12.7", "serverless-domain-manager": "^7.1.2", "serverless-offline": "^13.2.0", "serverless-prune-plugin": "^2.0.2", "serverless-step-functions": "^3.17.0", "xlsx": "^0.18.5", "axios": "^1.7.2"}, "devDependencies": {"eslint": "^6.5.1", "serverless-offline": "^13.2.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "author": "<PERSON><PERSON>", "license": "ISC"}