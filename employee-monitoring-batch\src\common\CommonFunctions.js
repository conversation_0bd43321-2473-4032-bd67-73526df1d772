const { ehrTables } = require('./tableAlias');

//Function to get the custom configuration for the employee monitoring reports
async function getCustomReportHeaderConfiguration(organizationDbConnection, employeeId, reportSource, reportType, employeeLevel){
    try {
        return(
            organizationDbConnection(ehrTables.customReportHeadersConfiguration)
                .select('Custom_Headers','Default_Headers','Configuration_Id')
                .from(ehrTables.customReportHeadersConfiguration)
                .where({
                    'Report_Source': reportSource,
                    'Report_Type': reportType,
                    'Enable': 'Yes'
                }).andWhere(function () {
                    if(employeeLevel && employeeId){ // Employee Level
                        this.where('Employee_Id', employeeId);
                    }
                    else if(employeeId){ //Either employee or organization level
                        this.where('Employee_Id', employeeId).orWhereNull('Employee_Id');
                    } else { // Organization level
                        this.whereNull('Employee_Id');
                    }
                }).orderBy('Employee_Id','desc')
                .then(async(configDetails) => {
                    return (configDetails && configDetails.length > 0) ? configDetails : [];
                }).catch(catchError => {
                    console.log('Error in getCustomReportHeaderConfiguration() function .catch block', catchError);
                    return false;
                })
        );

    } catch (err){
        console.log('Error in getCustomReportHeaderConfiguration function .catch block', err);
        return false;
    }
}

async function getOrganizationCustomReportHeaderConfiguration(organizationDbConnection){
    try {
        console.log('Inside getOrganizationCustomReportHeaderConfiguration function');
        return(
            organizationDbConnection(ehrTables.customReportHeadersConfiguration)
                .select('*')
                .from(ehrTables.customReportHeadersConfiguration)
                .whereNull('Employee_Id')
                .orderBy('Employee_Id','desc')
                .then(async(configDetails) => {
                    return (configDetails && configDetails.length > 0) ? configDetails : [];
                }).catch(catchError => {
                    console.log('Error in getCustomReportHeaderConfiguration() function .catch block', catchError);
                    return false;
                })
        );

    } catch (err){
        console.log('Error in getCustomReportHeaderConfiguration function .catch block', err);
        return false;
    }
}

module.exports.getCustomReportHeaderConfiguration=getCustomReportHeaderConfiguration;
module.exports.getOrganizationCustomReportHeaderConfiguration=getOrganizationCustomReportHeaderConfiguration;
