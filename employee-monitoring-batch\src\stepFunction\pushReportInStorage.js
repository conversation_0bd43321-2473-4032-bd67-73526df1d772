'use strict';
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone');
// require xlsx package
const reader = require('xlsx');
// require aws sdk
const aws = require('aws-sdk');
const fs = require('fs');
// require file to access constant values
const { s3FileUpload } = require('../common/appConstants');
// require common function
const commonFunction=require('./commonFunctions');
const formReportData=require('../common/formReportData');
// Imports the Google Cloud client library
const {Storage} = require('@google-cloud/storage');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let appManagerDbConnection='';
let orgDbConnection='';

/** function to push report data based on input storage */
module.exports.pushReportInStorage  = async(event, context) =>{
    // variable declarations
    let frequency='';
    let logId='';
    let orgCode='';
    let jobId='';
    let triggerType=1;
    try{
        // get input params
        if(event && event.input){
            frequency=(event.input.frequency).toLowerCase();
            orgCode=event.input.orgCode;
            logId=event.input.logId;
            jobId=event.input.jobId;
            triggerType = event.input.triggerType;
        }
        let startDate;
        let endDate;
        let exportFileName;
        let successResponse;
        // check input param exist
        if(frequency && orgCode && logId && jobId && triggerType){
            let getDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            appManagerDbConnection=knex(getDbConnection.AppManagerDb);
            let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection,orgCode);
            if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                

                // make organization database connection
                let getDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                if(Object.keys(getDbConnection).length){
                    orgDbConnection=knex(getDbConnection.OrganizationDb);
                    
                    let updateParams={
                        Status:'Inprogress',
                        Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                    }
                    // Update the job status as inprogress in data integration log table
                    let updateStatus=await commonFunction.updateDataIntegrationLog(orgDbConnection,updateParams,logId);
                    // get the data integration schedule details based on jobId
                    let scheduleDetails=await commonFunction.getIntegrationDetails(appManagerDbConnection,jobId);
                    // check whether data exist or not
                    if(Object.keys(scheduleDetails).length>0){
                        let reportType=scheduleDetails.Report_Type;
                        let fileFormat=scheduleDetails.File_Format;
                        let storageType=scheduleDetails.Storage_Platform;
                        let folderPathSettings=scheduleDetails.Upload_In_Root_Folder
                        // function to get the log details based on logId
                        let getLogData=await commonFunction.getLogDetails(orgDbConnection,logId);
                        // check logdata exist or not
                        if(Object.keys(getLogData).length>0){
                            startDate=getLogData.Start_Date;
                            endDate=getLogData.End_Date;
                            try{
                                // based on the start,end date and report type form the report data
                                let getData=await formReportData.getReportData(orgDbConnection,startDate,endDate,reportType,appManagerDbConnection,orgCode);
                                if(getData.length>0){
                                    // convert frequency to camelCase for filename. Example daily -> Daily
                                    let frequencyInCamelCase=frequency.substring(0,1).toUpperCase()+frequency.substring(1);
                                    // form s3 filename. Example infotech_EMPSR_Daily.csv
                                    let s3FileName=orgCode+'_'+reportType+'_'+frequencyInCamelCase+'.'+fileFormat;
                                    // form the report based on file format
                                    try{

                                        if(reportType.toUpperCase() === 'BID' || reportType.toUpperCase()==='MPP'){
                                            if(orgCode == 'cebprouat' || orgCode == 'cebpro' ){
                                                console.log("reportType: ", orgCode, reportType.toUpperCase());
                                            }
                                            // Convert JSON to XLSX workbook
                                            let wb = reader.utils.book_new();
                                            await Promise.resolve(getData.forEach((data, index) => {
                                                let key = Object.keys(data)[0]; // Get the key (name of the sheet)
                                                reader.utils.book_append_sheet(wb, reader.utils.json_to_sheet(data[key]), key); // Use the key as the sheet name
                                            }));
                                            exportFileName = '/tmp/'+s3FileName;
                                            reader.writeFile(wb, exportFileName);

                                        } else {
                                            let workSheet = reader.utils.json_to_sheet(getData, {
                                                skipHeader: 1,
                                            });
                                            
                                            let workBook = reader.utils.book_new();                        
                                            reader.utils.book_append_sheet(workBook, workSheet, `response`);
                                            exportFileName = '/tmp/'+s3FileName;
                                            reader.writeFile(workBook, exportFileName);
                                        }
                                    }
                                    catch(error){
                                        console.log('Error in creating '+fileFormat+' file',error);
                                        throw 'Error in generating report.';
                                    }
                                    let executionDate=moment(endDate).format('YYYY-MM-DD');
                                    // form folder path
                                    let s3FilePath=process.env.domainName+'/'+orgCode+'/'+executionDate+'/'+s3FileName;
                                    // upload file in s3
                                    let pushFile=await uploadFileInS3(s3FileName,process.env.dataIntegrationBucket,s3FilePath);
                                    if(pushFile==='success'){
                                        try{
                                            // get the secret details
                                            let secretData=await commonFunction.getSecretDetails(process.env.region,orgCode,appManagerDbConnection);
                                            // check whether secret data exist or not
                                            if(secretData && Object.keys(secretData).length>0){
                                                let hrappSecretKeys=(secretData.hrapp)?JSON.parse(secretData.hrapp):{};
                                                let secretKeys=(secretData[orgCode])?JSON.parse(secretData[orgCode]):{};
                                                if(Object.keys(secretKeys).length>0 && Object.keys(hrappSecretKeys).length>0){
                                                    let pathToBeUpload;
                                                    // if storage type is aws
                                                    if(storageType.toLowerCase()==='aws')
                                                    {
                                                        let accessKeyId = secretKeys.access_key ? (secretKeys.access_key).trim() : null;
                                                        let secretAccessKey = secretKeys.secret_key ? (secretKeys.secret_key).trim() : null;
                                                        let bucketName = secretKeys.bucket_name ? (secretKeys.bucket_name).trim() : null;
                                                        let region = secretKeys.region ? (secretKeys.region).trim() : null;
                                                        if(accessKeyId && secretAccessKey && bucketName)
                                                        {

                                                            let awsConfig = {
                                                                accessKeyId:accessKeyId,
                                                                secretAccessKey:secretAccessKey
                                                            }

                                                            if(region){
                                                                awsConfig = {
                                                                    accessKeyId:accessKeyId,
                                                                    secretAccessKey:secretAccessKey,
                                                                    region: region
                                                                }
                                                            }
                                                            // update the client secret keys
                                                            aws.config.update(awsConfig);
                                                            // form the file path to be uploaded in client storage based on settings
                                                            if(folderPathSettings.toLowerCase()==='yes'){
                                                                pathToBeUpload=s3FileName;
                                                            }
                                                            else{
                                                                pathToBeUpload=executionDate+'/'+s3FileName;
                                                            }
                                                            
                                                            if(reportType === 'BID'){
                                                                pathToBeUpload = orgCode+'/recruitmentdata/'+moment().format('YYYYMMDD')+'/RMS Data Export.xlsx';
                                                            }
                                                            if(reportType.toUpperCase() === 'MPP'){
                                                                pathToBeUpload = orgCode+'/mppdata/'+moment().format('YYYYMMDD')+'/MPP Data Export.xlsx';
                                                            }

                                                            // upload the file in client bucket
                                                            let pushFileToS3=await uploadFileInS3(s3FileName,bucketName,pathToBeUpload);
                                                            console.log("pushFileToS3: ",pushFileToS3);
                                                            // Once file upload then update our credentials to avoid access client account
                                                            aws.config.update(
                                                                {
                                                                    accessKeyId:hrappSecretKeys.access_key,
                                                                    secretAccessKey:hrappSecretKeys.secret_key
                                                                }
                                                            );
                                                            if(pushFileToS3==='success')
                                                            {
                                                                // update success status in log table
                                                                let updateParams={
                                                                    Status:'Success',
                                                                    S3_File_Name:s3FileName,
                                                                    Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')                
                                                                }
                                                                await commonFunction.updateDataIntegrationLog(orgDbConnection,updateParams,logId);
                                                                let updateSuccessParams={
                                                                    Status:'Success',
                                                                    Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')                
                                                                }
                                                                // update success status in data schedule table
                                                                await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection,updateSuccessParams,jobId);  
                                                                await commonFunction.deleteTempFolderFiles(exportFileName);
                                                                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                                orgDbConnection?orgDbConnection.destroy():null;    
                                                                let response = 
                                                                {
                                                                    nextStep:'Step1',
                                                                    input:{'frequency':frequency, 'triggerType': triggerType},
                                                                    message:'Execution completed so process the next record.'
                                                                };
                                                                return response;         
                                                            }
                                                            else{
                                                                throw 'Error while uploading file in storage type.';
                                                            }
                                                        }
                                                        else{
                                                            console.log('Either client accessKeyId or secretAccessKey or bucketName details is missing');
                                                            throw 'Secret key is missing.';
                                                        }
                                                    } else if(storageType.toLowerCase()==='gcp'){
                                                        let projectId=(secretKeys.project_id).trim();
                                                        let clientEmail=(secretKeys.client_email).trim();
                                                        let privateKey=(secretKeys.private_key).trim();
                                                        let bucketName=(secretKeys.bucket_name).trim();
                                                
                                                        if(projectId && clientEmail && privateKey && bucketName)
                                                        {
                                                            // Creates a cloud storage client
                                                            const storage = new Storage({
                                                                projectId: projectId,
                                                                credentials:{
                                                                'client_email':clientEmail,
                                                                'private_key':privateKey.replace(/\\n/g, '\n')
                                                                }
                                                            });
                                                            /** Upload file in google cloud storage */
                                                            await storage.bucket(bucketName).upload(exportFileName, {
                                                                destination: executionDate+'/'+s3FileName,
                                                            }).then(async function(){
                                                                // update success status in log table
                                                                let updateParams={
                                                                    Status:'Success',
                                                                    S3_File_Name:s3FileName,
                                                                    Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')                
                                                                }
                                                                await commonFunction.updateDataIntegrationLog(orgDbConnection,updateParams,logId);
                                                                let updateSuccessParams={
                                                                    Status:'Success',
                                                                    Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')                
                                                                }
                                                                // update success status in data schedule table
                                                                await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection,updateSuccessParams,jobId);  
                                                                await commonFunction.deleteTempFolderFiles(exportFileName);
                                                                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                                orgDbConnection?orgDbConnection.destroy():null;    
                                                                successResponse = 
                                                                {
                                                                    nextStep:'Step1',
                                                                    input:{'frequency':frequency, 'triggerType': triggerType},
                                                                    message:'Execution completed so process the next record.'
                                                                };
                                                                return successResponse; 

                                                            }).catch(function (uploadError){
                                                                console.log('Error while uploading the file in Cloud Storage.',uploadError);
                                                                throw 'Error while uploading the file in Cloud Storage.';
                                                            });
                                                            return successResponse;
                                                        } else{
                                                            throw 'Secret key is missing.';
                                                        }
                                                    }
                                                    else{
                                                        console.log('Invalid storage type - ',storageType);
                                                        // need to implement for other types. Currently we implement for aws so this block is handled as error
                                                        throw 'Invalid storage type';
                                                    }
                                                }
                                                else{
                                                    console.log('No data in secret manager');
                                                    throw 'Secret not exist for this instance';
                                                }
                                            }
                                            else{
                                                throw 'Error in getting secret details from secret manager.';
                                            }
                                        }
                                        catch(error){
                                            console.log('Error after calling get secret details function',error);
                                            let errorMessages=['No secret details exist.','Error in getting secret details.','Secret not exist for this instance','Invalid storage type','Error while uploading file in storage type.','Error in getting secret details from secret manager.','Secret key is missing.'];
                                            let failureReason=errorMessages.includes(error)?error:'Error uploading file in s3';
                                            await updateFailureStatus(orgDbConnection,appManagerDbConnection,logId,jobId,failureReason);
                                            await commonFunction.deleteTempFolderFiles(exportFileName);
                                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                            orgDbConnection?orgDbConnection.destroy():null;    
                                            let response = 
                                            {
                                                nextStep:'Step1',
                                                input:{'frequency':frequency, 'triggerType': triggerType},
                                                message:'Error while processing the data.So process for the next instance.'
                                            };
                                            return response;
                                        }
                                    }                        
                                    else{
                                        console.log('Error in uploading file in s3 bucket');
                                        await updateFailureStatus(orgDbConnection,appManagerDbConnection,logId,jobId,'Error uploading file in s3')
                                        await commonFunction.deleteTempFolderFiles(exportFileName);
                                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                        orgDbConnection?orgDbConnection.destroy():null;    
                                        let response = 
                                        {
                                            nextStep:'Step1',
                                            input:{'frequency':frequency, 'triggerType': triggerType},
                                            message:'Error while processing the data.So process for the next instance.'
                                        };
                                        return response;
                                    }
                                }
                                else{
                                    console.log('No report data exist');
                                    let updateParams={
                                        Status:'Success',
                                        Reason:'No activity data found.',
                                        Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                    }
                                    // Update the job status as success in data integration log table
                                    await commonFunction.updateDataIntegrationLog(orgDbConnection,updateParams,logId);
                                    let updateStatusParams={
                                        Status:'Success',
                                        Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                    }
                                    // Update the job status as success in data schedule table
                                    await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection,updateStatusParams,jobId);
                                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                    orgDbConnection?orgDbConnection.destroy():null;
                                    let response = 
                                    {
                                        nextStep:'Step1',
                                        input:{'frequency':frequency, 'triggerType': triggerType},
                                        message:'Process the next record.'
                                    };
                                    return response;
                                }
                            }
                            catch(error){
                                console.log('Error after calling getReportData function',error);
                                await updateFailureStatus(orgDbConnection,appManagerDbConnection,logId,jobId,'Error while retrieving the report data');
                                await commonFunction.deleteTempFolderFiles(exportFileName);
                                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                orgDbConnection?orgDbConnection.destroy():null;    
                                let response = 
                                {
                                    nextStep:'Step1',
                                    input:{'frequency':frequency, 'triggerType': triggerType},
                                    message:'Error while processing the data.So process for the next instance.'
                                };
                                return response;
                            }
                        }
                    }
                    else{
                        console.log('Error in getting the schedule details for '+orgCode+' instance');
                        await updateFailureStatus(orgDbConnection,appManagerDbConnection,logId,jobId,'Error while getting schedule details.');
                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                        orgDbConnection?orgDbConnection.destroy():null;    
                        let response = 
                        {
                            nextStep:'Step1',
                            input:{'frequency':frequency, 'triggerType': triggerType},
                            message:'Error while getting schedule details.'
                        };
                        return response;    
                    }
                }
                else{
                    console.log('Error while making organization database connection.');
                    await updateFailureStatus(orgDbConnection,appManagerDbConnection,logId,jobId,'Error while making connection.');
                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                    orgDbConnection?orgDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'Step1',
                        input:{'frequency':frequency, 'triggerType': triggerType},
                        message:'Error in making database connection.Process the next record'
                    };
                    return response;
                }
            } else{
                console.log('Error while getting the data region.');
                await updateFailureStatus(orgDbConnection,appManagerDbConnection,logId,jobId,'Error while getting the data region.');
                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                orgDbConnection?orgDbConnection.destroy():null;
                let response = 
                {
                    nextStep:'Step1',
                    input:{'frequency':frequency, 'triggerType': triggerType},
                    message:'Error while getting the data region.Process the next record'
                };
                return response;
            }
        }
        else{
            console.log('Input value is empty. So process the next record');
            appManagerDbConnection ? appManagerDbConnection.destroy():null;
            orgDbConnection?orgDbConnection.destroy():null;
            let response = 
            {
                nextStep:'Step1',
                input:{'frequency':frequency, 'triggerType': triggerType},
                message:'Invalid input.Process the next record.'
            };
            return response;
        }
    }
    catch(error){
        console.log('Error in pushReportInStorage function main catch block.', error);
        appManagerDbConnection ? appManagerDbConnection.destroy():null;
        orgDbConnection?orgDbConnection.destroy():null;
        let response = 
        {
            nextStep:'Step1',
            input:{'frequency':frequency, 'triggerType': triggerType},
            message:'Error in step2 main catch block.'
        };
        return response;
    }
};

async function uploadFileInS3(exportFileName,bucketName,filePath){
    try{
        let s3 = new aws.S3();
        let fileName='/tmp/'+exportFileName;
        let data=fs.createReadStream(fileName);
        let params = {
            Bucket: bucketName,
            Key: filePath,
            Body: data,
            ContentType: s3FileUpload.binaryFile,
            ServerSideEncryption:s3FileUpload.defaultEncryption
        };
        let uploadResponse = await s3.upload(params).promise();
        return 'success';
    }
    catch(error){
        console.log('Error in uploadFileInS3 function catch block.', error);
        return 'error'
    }
};

async function updateFailureStatus(orgDbConnection,appManagerDbConnection,logId,jobId,reason){
    try{
        let updateFailureParams={
            Status:'Failure',
            Reason:reason,
            S3_File_Name:null,
            Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
        }
        await commonFunction.updateDataIntegrationLog(orgDbConnection,updateFailureParams,logId);

        let updateFailureStatus={
            Status:'Failure',
            Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
        }
        await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection,updateFailureStatus,jobId);
        return 'success';
    }
    catch(error){
        console.log('Error in updateFailureStatus function catch block.', error);
        return 'error';
    }
}
