'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const commonFunction = require('../common/initiateStepFunction');

// Function to initiate daily insights notification step function
module.exports.triggerInsightsNotificationFunction  = async(event, context) =>{
    try{
        console.log('Inside triggerInsightsNotificationFunction function',event);
        let inputParams={
            schedule:event.schedule,
            source:event.process
        }
        let triggerAdminNotification= await commonFunction.triggerStepFunction(process.env.stateMachineArn,event.process, '',inputParams);
       
        return {errorCode:'',message: 'Daily insights notification step function initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in triggerInsightsNotificationFunction function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EM0091');
        return {errorCode:errResult.code,message: errResult.message};
    }
};

// function to initiate data integration step function
module.exports.triggerDataIntegrationFunction  = async(event, context) =>{
    try{
        console.log('Inside triggerDataIntegrationFunction function',event);
        let inputParams={
            frequency:event.frequency,
            triggerType: event.triggerType ? event.triggerType : 1,
            schedule: event.schedule ? event.schedule : 0
        }
        let triggerAdminNotification= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'','',inputParams);
        return {errorCode:'',message: 'Step function initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in triggerDataIntegrationFunction function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'BP0101');
        return {errorCode:errResult.code,message: errResult.message};
    }
};