'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { formIds,defaultValues } = require('../common/appConstants');
// require common function
const { getSummarizationDateBasedOnZone,insertRecordForFailedWSData,updateSummaryDataInMasterTable,getWorkScheduleData,removeUserFromAppManager,removeBulkInstancesFromAppManager }=require('./commonFunctions');

// variable declarations
let summarizationStatusTable;
let masterTable;
let inputSource;
let inputStatus;
let appmanagerDbConnection='';
let orgCode='';
let activityDate;
let summarizationDate;

// Function to get the employee monitoring subscribed user for summarizing the daily activities
module.exports.getEmpMonitoringSubscribedInstance  = async(event, context) =>{
    try{
        console.log('Inside getEmpMonitoringSubscribedInstance', event, event.source);
        // get input data
        inputSource=event.source;
        inputStatus=event.status;
        // Same logic is used for different input source so we define the table and field name
        if(inputSource==='app'){
            summarizationStatusTable=ehrTables.appActivitySummarizationStatus;
            masterTable=appManagerTables.appActivitySummarizationManager;
            activityDate='App_Summarization_Date';
        }
        else if(inputSource==='url'){
            summarizationStatusTable=ehrTables.urlActivitySummarizationStatus;
            masterTable=appManagerTables.urlActivitySummarizationManager;
            activityDate='Url_Summarization_Date';
        }
        else if(inputSource==='appTitle')
        {
            summarizationStatusTable=ehrTables.appTitleActivitySummarizationStatus;
            masterTable=appManagerTables.appTitleActivitySummarizationManager;
            activityDate='App_Title_Summarization_Date';   
        }
        else{
            summarizationStatusTable=ehrTables.employeeActivitySummarizationStatus;
            masterTable=appManagerTables.employeeActivitySummarizationManager;
            activityDate='Activity_Summarization_Date';
        }
        // if input status contain open then proceed to step2
        if(inputStatus && inputStatus.toLowerCase()==='open')
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.
            Incase of input status as 'Open' proceed to the summarization process */
            console.log('Event triggered to process remaining records so move to step2');
            let response={
                nextStep:'Step2',
                input:{'process':inputSource,'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length){
                // form app manager database connection
                appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                // The event tried for summarization process will have status as empty so get all emp monitoring users and insert into manager table.
                if(!inputStatus)
                {
                    // get active employee monitoring plan subscribed users
                    let empMonitoringSubscribedUsers= await commonLib.func.getInstanceSubscribedToFormId(formIds.activityTracker,appmanagerDbConnection,defaultValues.activeStatus);
                    console.log('Employee monitoring subscribed user count',empMonitoringSubscribedUsers.length);
                    
                    //Delete the instances from mastertable where the orgcode is not in active employee monitoring plan
                    await removeBulkInstancesFromAppManager(appmanagerDbConnection,empMonitoringSubscribedUsers,masterTable);
                    // Check whether users exist or not
                    if(empMonitoringSubscribedUsers.length>0)
                    {
                        // select the orgcode and date from the manager table
                        let managerTableSubQuery=appmanagerDbConnection(masterTable)
                        .select('Org_Code')
                        .whereIn('Org_Code',empMonitoringSubscribedUsers)

                        // based on source form the select query
                        if(inputSource==='app'){
                            managerTableSubQuery=managerTableSubQuery
                            .select('App_Summarization_Date as date')
                        }
                        else if(inputSource==='url'){
                            managerTableSubQuery=managerTableSubQuery
                            .select('Url_Summarization_Date as date')
                        }
                        else if(inputSource==='appTitle')
                        {
                            managerTableSubQuery=managerTableSubQuery
                            .select('App_Title_Summarization_Date as date')
                        }
                        else{
                            managerTableSubQuery=managerTableSubQuery
                            .select('Activity_Summarization_Date as date')
                        }

                        // get the details for all the orgcode in manager table
                        let getRecord=await managerTableSubQuery
                        .then(async (orgcodeExist) =>{
                            return orgcodeExist;
                        })

                        // iterate the loop for all the subscribed users
                        for(let i = 0; i < empMonitoringSubscribedUsers.length; i++){
                            orgCode=empMonitoringSubscribedUsers[i];
                            // get the record which is associated with the orgcode
                            let getInstanceRecord=getRecord.find(i=>i.Org_Code===orgCode);
                            // check whether orgcode already exist in manager table
                            let isOrgCodeExist=(getInstanceRecord && Object.keys(getInstanceRecord).length);
                            // insert work schedule record in status table and update the data in manager table
                            let insertSummaryData=await checkMemberStatusAndInsertSummaryData(appmanagerDbConnection,orgCode,isOrgCodeExist,inputSource,getInstanceRecord);
                        }
                        // return to the step2 to process the summary process
                        let response={
                            nextStep:'Step2',
                            input:{'process':inputSource,'status':inputStatus},
                            message:'Record inserted successfully proceed the summarization process.'          
                        }
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        return response;
                    }
                    else{
                        console.log('There is no employee monitoring subscribed active user exist.So quit the execution.');
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'process':inputSource,'status':inputStatus},
                            message:'There is no employee monitoring subscribed active users.'
                        };
                        return response;
                    }
                }
                // If failed status exist in input then process only the failed daily summary records
                else if(inputStatus.toLowerCase()==='failed'){
                    console.log('Input contains failed status so execute only for failed instances in app manager table.');
                    // get all the failed records from app manager table
                    return(
                        appmanagerDbConnection(masterTable)
                        .select('*')
                        .where('Status','Failed')
                        .then(async (getRecords) =>{
                            // check whether record exist or not
                            if(getRecords.length>0){
                                // iterate all the failed users
                                for(let key of getRecords){
                                    orgCode=key.Org_Code;
                                    // based on source define the field name
                                    if(inputSource==='app'){
                                        summarizationDate=key.App_Summarization_Date;
                                    }
                                    else if(inputSource==='url'){
                                        summarizationDate=key.Url_Summarization_Date
                                    }
                                    else if(inputSource==='appTitle')
                                    {
                                        summarizationDate=key.App_Title_Summarization_Date; 
                                    }
                                    else{
                                        summarizationDate=key.Activity_Summarization_Date
                                    }
                                    // insert new record with open status for the failed summarization records
                                    let insertRecord=await insertRecordForFailedWSData(summarizationDate,summarizationStatusTable,inputSource,orgCode,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,appmanagerDbConnection)
                                    
                                    // once record inserted in organization table then update the open status in app manager table
                                    if(insertRecord){
                                        if(insertRecord==='No failed records found'){
                                            console.log('No failed records in status table so no need to process this record.');
                                        }
                                        else{
                                            let updateParams={
                                                Status : 'Open'
                                            }
                                            // function to update the status in manager table
                                            let updateStatus=await updateSummaryDataInMasterTable(appmanagerDbConnection,updateParams,orgCode,masterTable);
                                            if(updateStatus){
                                                console.log('Open status updated for the failed records in app manager table for '+orgCode+' instance.');
                                            }
                                            else{
                                                console.log('Error while updating the open status in app manager table for '+orgCode+' instance.');
                                            }
                                        }
                                    }
                                    else{
                                        console.log('Error while inserting new record in '+ orgCode +' database.');
                                    }
                                }
                                // return response to process the next step
                                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                                let response={
                                    nextStep:'Step2',
                                    input:{'process':inputSource,'status':inputStatus},
                                    message:'New Record inserted for previously failed instances.So proceed to step2'          
                                }
                                return response;                
                            }
                            // since no failed record exist so quit the step function
                            else{
                                console.log('No failed records found so quit the process');
                                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                                let response = 
                                {
                                    nextStep:'End',
                                    input:{'process':inputSource,'status':inputStatus},
                                    message:'No failed '+ inputSource +' records found.'
                                };
                                return response;
                            }
                        })
                    );
                }
                else{
                    console.log('Some other status record exists in input');
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'End',
                        input:{'process':inputSource,'status':inputStatus},
                        message:'Invalid input request'
                    };
                    return response;
                }
            }
            else{
                console.log('Error while creating app manager database connection in step1');
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response = 
                {
                    nextStep:'Error',
                    input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'summarizationDate':summarizationDate,'reason':'Error creating app manager database connection from step1.'},
                    message:'Error in creating app manager database connection.'
                };
                return response; 
            }
        }
    }
    catch (mainCatchError){
        console.log('Error in getEmpMonitoringSubscribedInstance function main catch block.', mainCatchError);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response = 
        {
            nextStep:'Error',
            input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error from step1 main catch block.'},
            message:'Error from step1 main catch block'
        };
        return response;
    }
};

/** function to check whether active members exist in organization or not.
 * If active user exists then insert the work schedule record in status table and update the date and status in master table */
async function checkMemberStatusAndInsertSummaryData(appmanagerDbConnection,orgCode,isOrgCodeExist,inputSource,instanceActivityDate){
    let orgDbConnection='';
    try{
        console.log('Inside checkMemberStatusAndInsertSummaryData function for '+ orgCode+ ' instance');
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
          if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
              
            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
            if(Object.keys(databaseConnection).length){
                orgDbConnection = knex(databaseConnection.OrganizationDb);
                let inputData=[];
                return(
                    orgDbConnection
                    .transaction(function(trx){
                        return(
                            // Check whether any active members exist in organization or not
                            orgDbConnection(ehrTables.teamMembers)
                            .count('Employee_Id as count')
                            .where('Member_Status','Active')
                            .transacting(trx)
                            .then(async (memberCount) =>{
                                // if members exist then insert the record in status table and update the status in master table
                                if (memberCount[0].count>0){
                                    // get the work schedule details
                                    let getWSDetails=await getWorkScheduleData(orgDbConnection,[]);
                                    if(getWSDetails && getWSDetails.length>0){
                                        // form the work scheduleId array
                                        let wsIdArray = getWSDetails.map(obj=>obj.WorkSchedule_Id);
                                        /** If orgcode already exist in manager table then form the work schedule data based on last processed date else insert the new record.
                                         * If orgcode does not exist then insert the work schedule data based on timezone and insert record in app manager table.
                                        */
                                        if(isOrgCodeExist){
                                            instanceActivityDate=instanceActivityDate.date;

                                            /** get the last processed date for the work scheduleIds */
                                            let statusQuery=orgDbConnection(summarizationStatusTable)
                                            .select('WorkSchedule_Id','TimeZone')
                                            .whereIn('WorkSchedule_Id',wsIdArray)
                                            .groupBy('WorkSchedule_Id')

                                            // based on source define the query
                                            if(inputSource==='app'){
                                                statusQuery=statusQuery
                                                .max('App_Summarization_Date as date')
                                            }
                                            else if(inputSource==='url'){
                                                statusQuery=statusQuery
                                                .max('Url_Summarization_Date as date')
                                            }
                                            else if(inputSource==='appTitle')
                                            {
                                                statusQuery=statusQuery
                                                .max('App_Title_Summarization_Date as date') 
                                            }
                                            else{
                                                statusQuery=statusQuery
                                                .max('Activity_Summarization_Date as date')
                                            }

                                            return(
                                                statusQuery
                                                .transacting(trx)
                                                .then(async (getPreviousRecord) =>{
                                                    let removeAlreadyProcessedWSIds=[];
                                                    if(getPreviousRecord.length){
                                                        // combine workschedule data and previously processed record
                                                        getWSDetails = getWSDetails.map(obj => {
                                                            let data = getPreviousRecord.find(item =>  item.WorkSchedule_Id === obj.WorkSchedule_Id);    
                                                            return {...obj, ...data}
                                                        });
                                                    }
                                                    // iterate for all the work schedule record
                                                    for(let record of getWSDetails){
                                                        // calculate the summarization date for the timezone
                                                        let dateBasedOnWorkSchedule=await getSummarizationDateBasedOnZone(record.TimeZone_Id);
                                                        // if date already exist in response then validate it with timezone based summarization date else use the calculated date
                                                        if(record.date){
                                                            if(record.date===dateBasedOnWorkSchedule){
                                                                console.log('Already processed date is same as summarization date for work scheduleId ',record.WorkSchedule_Id);
                                                                removeAlreadyProcessedWSIds.push(record.WorkSchedule_Id);
                                                            }
                                                            else if(record.date>dateBasedOnWorkSchedule){
                                                                console.log('Already processed date is greater than summarization date for work scheduleId ',record.WorkSchedule_Id);
                                                                removeAlreadyProcessedWSIds.push(record.WorkSchedule_Id);
                                                            }
                                                            else{
                                                                record.date=moment(record.date).add(1,'d').format('YYYY-MM-DD');
                                                            }
                                                        }
                                                        else{
                                                            record.date=dateBasedOnWorkSchedule;
                                                        }
                                                    }
                                                    console.log("getWSDetails2: ",getWSDetails);
                                                    console.log('removeAlreadyProcessedWSIds: ',removeAlreadyProcessedWSIds );
                                                    // if the summarization date for that work schedule is already processed then no need to insert new records
                                                    if(removeAlreadyProcessedWSIds.length>0){
                                                        getWSDetails = getWSDetails.filter(item => !removeAlreadyProcessedWSIds.includes(item.WorkSchedule_Id));
                                                    }

                                                    if(getWSDetails.length > 0){

                                                        inputData = getWSDetails.map(field => ({
                                                            WorkSchedule_Id:field.WorkSchedule_Id,
                                                            Regular_Work_Start_Time:field.Regular_Work_Start_Time,
                                                            Regular_Work_End_Time:field.Regular_Work_End_Time,
                                                            Shift_Margin_From:(field.Check_In_Consideration_Time)?(field.Check_In_Consideration_Time):0,
                                                            Shift_Margin_To:(field.Check_Out_Consideration_Time)?(field.Check_Out_Consideration_Time):0,
                                                            Twodays_Flag:field.Twodays_Flag,
                                                            TimeZone:field.TimeZone_Id,
                                                            Transaction_Status : 'Open',
                                                            Execution_Status:'Started',
                                                            date:field.date,
                                                            Summarization_Time:new Date()
                                                        }))

                                                        await Promise.all( inputData.map(async field=>{
                                                            if(inputSource==='app'){
                                                                field.App_Summarization_Date=field.date;
                                                            }
                                                            else if(inputSource==='url'){
                                                                field.Url_Summarization_Date=field.date;
                                                            }
                                                            else if(inputSource==='appTitle')
                                                            {
                                                                field.App_Title_Summarization_Date=await getSummarizationDateBasedOnZone(field.TimeZone);
                                                            }
                                                            else{
                                                                field.Activity_Summarization_Date=field.date;
                                                            }
                                                            delete field.date;
                                                        }));
                                                        return(
                                                            // insert work schedule data in status table
                                                            orgDbConnection(summarizationStatusTable)
                                                            .insert(inputData)
                                                            .transacting(trx)
                                                            .then(insertData=> {
                                                                console.log('Work schedule record inserted in organization database',insertData);
                                                                // update the manager table with open status
                                                                let updateParams={
                                                                    Status:'Open',
                                                                    [activityDate]:moment(instanceActivityDate).add(1,'d').format('YYYY-MM-DD')
                                                                }
                                                                return(
                                                                    appmanagerDbConnection(masterTable)
                                                                    .update(updateParams)
                                                                    .where('Org_Code',orgCode)
                                                                    .then(async (updateRecords) =>{
                                                                        console.log('Update status in manager table for '+orgCode+' instance -',updateRecords);
                                                                        return 'success';       
                                                                    })
                                                                )
                                                            })
                                                        );  
                                                    } else {
                                                        console.log('Summarization already completed for '+orgCode+' instance -');
                                                        return 'success'; 
                                                    }          
                                                })
                                            )
                                        }
                                        else{
                                            inputData = getWSDetails.map(field => ({
                                                WorkSchedule_Id:field.WorkSchedule_Id,
                                                Regular_Work_Start_Time:field.Regular_Work_Start_Time,
                                                Regular_Work_End_Time:field.Regular_Work_End_Time,
                                                Shift_Margin_From:(field.Check_In_Consideration_Time)?(field.Check_In_Consideration_Time):0,
                                                Shift_Margin_To:(field.Check_Out_Consideration_Time)?(field.Check_Out_Consideration_Time):0,
                                                Twodays_Flag:field.Twodays_Flag,
                                                TimeZone:field.TimeZone_Id,
                                                Transaction_Status : 'Open',
                                                Execution_Status:'Started',
                                                Summarization_Time:new Date()
                                            }))
                                            await Promise.all( inputData.map(async field=>{
                                                if(inputSource==='app'){
                                                    field.App_Summarization_Date=await getSummarizationDateBasedOnZone(field.TimeZone);
                                                }
                                                else if(inputSource==='url'){
                                                    field.Url_Summarization_Date=await getSummarizationDateBasedOnZone(field.TimeZone);
                                                }
                                                else if(inputSource==='appTitle')
                                                {
                                                    field.App_Title_Summarization_Date=await getSummarizationDateBasedOnZone(field.TimeZone);
                                                }
                                                else{
                                                    field.Activity_Summarization_Date=await getSummarizationDateBasedOnZone(field.TimeZone);
                                                }
                                            }));
                                            // insert work schedule data in status table
                                            return(
                                                orgDbConnection(summarizationStatusTable)
                                                .insert(inputData)
                                                .transacting(trx)
                                                .then(insertData=> {
                                                    console.log('Work schedule record inserted in organization database',insertData);
                                                    // insert record for this orgcode in manager table
                                                    let insertParams={
                                                        Status:'Open',
                                                        Org_Code:orgCode,
                                                        [activityDate]:moment().subtract(1,'d').format('YYYY-MM-DD')
                                                    }
                                                    return(
                                                        appmanagerDbConnection(masterTable)
                                                        .insert(insertParams)
                                                        .then(async (insertRecord) =>{
                                                            console.log('New record inserted in manager table for '+orgCode+' instance -',insertRecord);
                                                            return 'success';
                                                        })
                                                    )
                                                })
                                            );
                                        }
                                    }
                                    else{
                                        console.log('Work schedule details does not exists or error while getting the work schedule details');
                                        return 'Work schedule details does not exists.';
                                    }
                                }
                                else{
                                    console.log('There is no active members in '+orgCode+ ' instance.So remove the record in manager table and process the next record.');
                                    await removeUserFromAppManager(appmanagerDbConnection,orgCode,masterTable);
                                    return 'No active members found.';
                                }
                            })
                        );
                    })
                    .then(function (result) {
                        return result;
                    }) 
                    .catch(function (catchError) {
                        console.log('Error in checkMemberStatusAndInsertSummaryData function .catch block',catchError);
                        return 'error';
                    })
                    .finally(() => {
                        orgDbConnection.destroy(); // destroy database connection
                    })
                );
            }
            else{
                console.log('Error while connecting organization database '+orgCode+' instance');
                return 'error';
            }
        } else{
            console.log('Error while getting the data region for '+orgCode+' instance');
            return 'error';
        }
    }
    catch(error){
        console.log('Error in checkMemberStatusAndInsertSummaryData function main catch block',error);
        // destroy database connection
        orgDbConnection?orgDbConnection.destroy():null;
        return 'error';
    }
};
