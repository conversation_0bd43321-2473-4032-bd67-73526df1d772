// require aws sdk
const AWS = require('aws-sdk');
const stepfunctions = new AWS.StepFunctions();

//Function to trigger the step function
module.exports = { 
    triggerStepFunction: (stateMachineArn,processName,status,inputParams=null) => {
    try
    {
        let input={};
        if(inputParams){
            input =inputParams;
        }
        else{
            input={'source':processName,'status':status};
        }
        //formation of inputs for step functions
        const params = {
            stateMachineArn: stateMachineArn,
            input: JSON.stringify(input)  
        }
        // trigger step function with the input params
        return new Promise(function(resolve, reject) {
            stepfunctions.startExecution(params, function (error, data) {
                if (error) 
                { 
                    console.log("Error in initiating daily summary step function",error)
                    resolve(''); // Rejects the promise with `error` as the reason               
                }
                else
                { 
                    resolve(data);// Fulfills the promise with `data` as the value
                } 
            })
        })
    }
    catch(catchError){
        console.log('Error in triggerStepFunction main catch block',catchError);
        return '';
    }
}
};
