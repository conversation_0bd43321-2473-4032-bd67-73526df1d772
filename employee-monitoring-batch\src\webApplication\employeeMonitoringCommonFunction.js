// require table alias
const { ehrTables,appManagerTables } = require('../common/tableAlias');
const { formIds, formName, awsSesTemplates, urlEncryption, signInLinkUrl,defaultValues } = require('../common/appConstants');
const activityCommonFunctions = require('../common/activityCommonFunctions');
const stealthModeCommonFunctions = require('../common/stealthModeCommonFunctions');

// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// reqiure moment time-zone
const moment = require('moment-timezone')

async function updateLockAppTime( organizationDbConnection, date) {
    try {
        console.log('Inside updateLockAppTime', date);
        
        return (
            await organizationDbConnection(ehrTables.employeeActivityDailySummary+' AS EADS')
            .innerJoin(ehrTables.appActivityDailySummary+' AS AADS', function () {
                this.on('EADS.Employee_Id', '=', 'AADS.Employee_Id')
                .andOn('EADS.Activity_Date', '=', 'AADS.Activity_Date');
            })
            .where('AADS.Application_Name', 'LockApp')
            .where('EADS.Activity_Date', date)
            .update({
                'EADS.Lockapp_Time': organizationDbConnection.raw(`
                CASE
                    WHEN AADS.Total_Activity_Duration_In_Minutes < EADS.Total_Activity_Duration_In_Minutes
                    THEN AADS.Total_Activity_Duration_In_HHMMSS
                    ELSE EADS.Total_Activity_Duration
                END
                `),
                'EADS.Lockapp_Time_In_Minutes': organizationDbConnection.raw(`
                CASE
                    WHEN AADS.Total_Activity_Duration_In_Minutes < EADS.Total_Activity_Duration_In_Minutes
                    THEN AADS.Total_Activity_Duration_In_Minutes
                    ELSE EADS.Total_Activity_Duration_In_Minutes
                END
                `),
            }).then(async(lockAppDuration) => {
                return(
                    await organizationDbConnection(ehrTables.employeeActivityDailySummary)
                    .update({
                        System_Uptime_Without_Lockapp_Time_In_Minutes: organizationDbConnection.raw('Total_Activity_Duration_In_Minutes - Lockapp_Time_In_Minutes'),
                        System_Uptime_Without_Lockapp_Time: organizationDbConnection.raw(`
                        TIME_FORMAT(
                            SEC_TO_TIME(
                            TIME_TO_SEC(Total_Activity_Duration) - TIME_TO_SEC(Lockapp_Time)
                            ),
                            '%H:%i:%s'
                        )
                        `)
                    }).where('Activity_Date',date)
                    .then(async(systemUpTimeResult) => {
                        return true;
                    }).catch(catchError=>{
                        console.log('Error in updateLockAppTime() function .catch block', catchError);
                        return false;
                    })
                )
            }).catch(catchError=>{
                console.log('Error in updateLockAppTime() function .catch block', catchError);
                return false;
            })
        )
    } catch (err) {
        console.log('Error in updateLockAppTime() catch block', err);
        return false;
    }
}


// function to email notification through AWS SES
async function sendEmailNotifications(notificationParams){
    try {
        //  require aws-sdk 
        const AWS = require("aws-sdk");
        // create object for aws SES
        const ses = new AWS.SES({ region: process.env.sesTemplatesRegion });
        //  call function sendTemplatedEmailto send email
        let response = await ses.sendTemplatedEmail(notificationParams).promise();
        console.log('sendEmailNotifications response:', response); //Console Required
        // return response
        return true;
    } catch (sendEmailNotificationsError) {
        console.log('error while in sendEmailNotifications() function catch block', sendEmailNotificationsError);
        // return response
        return false;
    }
};
// get employee email
async function getEmployeeEmail(employeeId,organizationDbConnection){
    return new Promise((resolve,reject)=>{
        return(
            organizationDbConnection(ehrTables.empJob)
            .select("Employee_Id", "Emp_Email")
            .from(ehrTables.empJob)
            .whereIn("Employee_Id", employeeId)
            .then((response) => {
                resolve(response);
            })
            .catch(getEmployeeEmailError=>{
                console.log('Error in getEmployeeEmail() function .catch block', getEmployeeEmailError);
                reject(getEmployeeEmailError);
            })
        )
    })
}

/** function to convert seconds to HH:MM:SS format.If input contains isHoursMinutes param then return only HH:MM value*/
async function getTimeInHoursMinutesSeconds(seconds,isHoursMinutes=null){
    let totalSeconds = seconds;
    return new Promise((resolve,reject)=>{
        try {
            let time;
            // get hours from totalSeconds
            let hours = parseInt(totalSeconds / (60 * 60));
            // get remaining seconds from totalSeconds
            let reminingSeconds = (totalSeconds % (60 * 60));
            // get minutes from reminingSeconds
            let minutes = parseInt(reminingSeconds / 60);
            // if input contains isHoursMinutes then return time in HH:MM format
            if(isHoursMinutes){
                // result time example: 09:40
                time = ((parseInt(hours) > 9 ) ? parseInt(hours) : '0' + parseInt(hours))  + ':' + ((parseInt(minutes) > 9) ? (parseInt(minutes)) : ('0' + parseInt(minutes)));
            }
            else{
                // get seconds from reminingSeconds
                let seconds = (reminingSeconds % 60);
                // result time example: 09:40:59
                time = ((parseInt(hours) > 9 ) ? parseInt(hours) : '0' + parseInt(hours))  + ':' + ((parseInt(minutes) > 9) ? (parseInt(minutes)) : ('0' + parseInt(minutes))) + ':' + ((seconds > 9) ? seconds : '0' + seconds);
            }
            // return response to resolver function
            resolve(time);
           } catch (getTimeInHoursMinutesSecondsError) {
            console.log('Error in getTimeInHoursMinutesSeconds() function main catch block', getTimeInHoursMinutesSecondsError);
            // return response to resolver function
            reject(getTimeInHoursMinutesSecondsError);
        }
    })
}

// form UTC minimum time and maximum time for given date
function formMinMaxTimeByZone(date, offSet) {
    // global variable declration
    let response = {
        minTime:"",
        maxTime:""
    };
    try{

        let minTime = new Date(date);

        minTime.setMinutes(minTime.getMinutes() - offSet);

        let maxTime = new Date(minTime.getTime());

        maxTime.setDate(maxTime.getDate() + 1);

        minTime = minTime.toISOString().substring(0, 19).replace('T', ' '); // 2020-06-03T13:00:00.000Z, 2020-06-04T13:00:00.000Z

        maxTime = maxTime.toISOString().substring(0, 19).replace('T', ' ');

        return { // 2020-06-03 13:00:00, 2020-06-04 13:00:00
            minTime,
            maxTime
        }
    }catch(formMinMaxTimeByZoneError){
        console.log('Error in formMinMaxTimeByZone() function catch block', formMinMaxTimeByZoneError);
        return response;
    }
}

// function to get time zoen based on teh zone id
async function getTimeZone(zoneId,organizationDbConnection){
    // get time zone from timezone table
    let response = await organizationDbConnection(ehrTables.timezone)
                .select('TimeZone_Id').where('Zone_Id',zoneId)
                .then(response => {
                    return response[0] ? response[0].TimeZone_Id : "";
                })
                .catch(getTimeZoneError => {
                    console.log('Error in getTimeZone() .catch block.',getTimeZoneError);
                    return "";
                })
    // return response back to fucntion
    return response;
};

//form the date based employee time offset for Activity_Start_Date_Time and Activity_End_Date_Time
function getDateTimeBasedOnTimeZoneOffset(dateTime, offSet) {
    let [date, time] = dateTime.split(" ");

    date = new Date(date);

    let [h, m, s] = time.split(":");

    date.setUTCHours(h);
    date.setUTCMinutes(m);

    date.setMinutes(date.getMinutes() + offSet);
    date.setSeconds(date.getSeconds() + s);
    // return response back to function
    return date.toISOString().substring(0, 19).replace('T', ' ');
}

// function to get employee id based on input status and monitoring type
async function getEmployeeIds(organizationDbConnection,status,monitoringType=null){
    try{
        let subQuery=organizationDbConnection(ehrTables.teamMembers + ' as TM')
        .pluck('TM.Employee_Id')
        .whereIn('TM.Member_Status',status)
        // if the monitoring type is stealth then get the record only with assets mapped
        if(monitoringType && monitoringType.toLowerCase()==="stealth mode"){
            subQuery=subQuery
            .innerJoin(ehrTables.assetManagement + " as AM", "TM.Employee_Id", "AM.Employee_Id")
            .groupBy('TM.Employee_Id')
        }
        return(
            subQuery
            .then((response) => {
                return response;
            })
            .catch(getEmpIdError=>{
                console.log('Error in getEmployeeIds function .catch block', getEmpIdError);
                return [];
            })
        )
    }
    catch(error){
        console.log('Error in getEmployeeIds function main catch',error);
        return [];
    }
}

/** function to get admin,employee admin and manager employee ids based on input. adminManager - Optional input,
When input contain adminManager as 1 then list both admin and manager's ids else only the admin employee id
Input params :
    adminManager - Field to differentiate whether to return both admin and manager or only admin members. If it is 1 then return both admin/manager else if it 0 then return only admin members
    verifyUser - If it is 1 then return only verified users.
    Non-mandatory. If the source is  "employeeMonitoring" then get both inactive and active employees else only the active employees
*/
async function getAdminEmployeeIds(organizationDbConnection,adminManager=0,verifyUser=0,source=''){
    try{
        let adminEmployeeIds = await commonLib.func.getAdminEmployeeIds(organizationDbConnection,adminManager,verifyUser,source, [formIds.admin,formIds.productivityMonitoringAdmin])
        return adminEmployeeIds
    }
    catch (catchError) {
        console.log('Error in getAdminEmployeeIds function main catch block', catchError);
        return [];
    }
}

// function to get manager employee Ids
async function getAllManagerIds(organizationDbConnection,verifyUser=0,source=''){
    try{
        let subQuery=organizationDbConnection
        .pluck('EPI.Employee_Id')
        .from(ehrTables.empPersonalInfo+' as EPI')
        .innerJoin(ehrTables.empJob +' as EJ','EPI.Employee_Id', 'EJ.Employee_Id')
        .where('EPI.Is_Manager',1)

        if(verifyUser){
            subQuery
            .innerJoin(ehrTables.empUser + ' as EU','EJ.Employee_Id','EU.Employee_Id')
            .whereNot('EU.Firebase_Uid','')
            .whereNotNull('EU.Firebase_Uid')
        }
        // if the source is employeeMonitoring then get both active and inactive employees else only the active employees
        if(source==='employeeMonitoring'){
            subQuery
            .whereIn('EJ.Emp_Status',['Active','InActive']);
        }
        else{
            subQuery
            .where('EJ.Emp_Status','Active')
        }
        return(
            subQuery
            .then(getEmpId=>{
                return getEmpId;
            })
            .catch(error => {
                console.log('Error in getAllManagerIds function .catch block',error);
                return [];        
            })
        );
    }
    catch(error){
        console.log('Error in getAllManagerIds function catch block',error);
        return [];
    }
};

/** Function to calculate the duration based on input status and return response based on outputFormat
 * @param {JSON} organizationDbConnection - Organization Database connection object
 * @param {Number} employeeId - Employee Id
 * @param {Date} minDate - Activity start date
 * @param {Date} maxDate - Activity end date
 * @param {String} status - Activity status 
 * @param {String} outputFormat - Format based on which output will be returned
 * @returns {String} - Return response based on outputFormat
 */
function getDurationBasedOnStatus(employeeId,minDate, maxDate, organizationDbConnection,status,outputFormat){
    try {
        let subQuery= organizationDbConnection(ehrTables.empActivityDetails)
        .where('Employee_Id', employeeId)
        .where('Activity_Start_Date_Time', '>=', minDate)
        .where(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"), '<=', maxDate)
        // update the where condition based on activity status
        if(status==='Active'){
            subQuery= subQuery
            .where('Activity_Status','Active')
        }
        else if(status==='Not Active'){
            subQuery= subQuery
            .where('Activity_Status','Not Active')
        }
        else if(status==='Idle'){
            subQuery= subQuery
            .where('Activity_Status','Idle')
        }
        // return the response independent of status
        else{
            subQuery= subQuery
        }
        // If the outputFormat is minutes then convert the seconds to minutes
        if(outputFormat==='minutes'){
            subQuery= subQuery.select(
            organizationDbConnection.raw('CASE WHEN sum(Total_Activity_Duration) > 0 THEN ROUND((sum(Total_Activity_Duration)/60))  ELSE "0" END as totalActivityDuration'))
        }
        // If the outputFormat is timeFormat then convert the seconds to HH:MM:SS format
        else if(outputFormat==='timeFormat'){
            subQuery=subQuery.select(
            organizationDbConnection.raw('CASE WHEN sum(Total_Activity_Duration) > 0 THEN TIME_FORMAT(SEC_TO_TIME(SUM(Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as totalActivityDuration'))
        }
        // by default return it in seconds
        else{
            subQuery=subQuery.select('Total_Activity_Duration as totalActivityDuration')
        }
        return(
            subQuery
            .then(activityData => {
                if(activityData.length>0 && activityData[0].totalActivityDuration){
                    return (activityData[0].totalActivityDuration);
                }
                else{
                    return (outputFormat==='timeFormat')?('00:00:00'):('0');
                }
            })
            .catch(catchError=>{
                console.log('Error in getDurationBasedOnStatus function .catch block', catchError);
                return (outputFormat==='timeFormat')?('00:00:00'):('0');
            })
        )
    }
    catch(catchError){
        console.log('Error in getDurationBasedOnStatus function main catch block', catchError);
        return '0';
    }
};

// function to get the employee name based on employeeId
async function getEmployeeName(organizationDbConnection,employeeId){
    try{
        return(
            organizationDbConnection
            .select(organizationDbConnection.raw("CONCAT_WS(' ',Emp_First_Name,Emp_Middle_Name, Emp_Last_Name) as employeeName"))
            .from(ehrTables.empPersonalInfo)
            .where('Employee_Id', employeeId)
            .then(getDetails=>{
                return (getDetails.length>0)?(getDetails[0].employeeName):'';
            })
            .catch(error => {
                console.log('Error in getEmployeeName function .catch block.',error);
                return '';
            })
        );
    }
    catch (catchError) {
        console.log('Error in getEmployeeName function main catch block', catchError);
        return '';
    }
};


/** function to group the result based on input key attribute */
function groupDataBasedOnInputField(arrayList, attribute) {
    try{
        const map = new Map();
        arrayList.forEach((item) => {
            // key will be the value of the attribute.
            const key = attribute(item);
            // based on the key get the json object from the map function
            const collection = map.get(key);
            // If collection exist push the item otherwise set the item with that key
            if (!collection) {
                map.set(key, [item]);
            } else {
                collection.push(item);
            }
        });
        return map;
    }
    catch(error){
        console.log('Error in groupDataBasedOnInputField function catch block');
        return [];
    }
};


// get organization monitoring mode from settings table
async function getOrganizationMonitoringMode(organizationDbConnection){
    try{
        return(
            organizationDbConnection
            .select('Monitoring_Mode')
            .from(ehrTables.employeeMonitorSettings)
            .then(getDetails=>{
                return (getDetails.length>0)?(getDetails[0].Monitoring_Mode):'';
            })
            .catch(error => {
                console.log('Error in getOrganizationMonitoringMode function .catch block.',error);
                return '';
            })
        );
    }
    catch(error){
        console.log('Error in getOrganizationMonitoringMode function catch block',error);
        return '';
    }
};

/** function to get the custom groupId based on input employeeId and formId. 
In case of error return as empty and handle as organization settings in main function  */
async function getEmployeeAssociatedGroupId(organizationDbConnection,employeeId,formId){
    try{
        // get the groupId list which are associated with the employeeId
        return(
            organizationDbConnection
            .pluck('Group_Id')
            .from(ehrTables.customEmployeeGroupEmployees)
            .where('Employee_Id', employeeId)
            .then(getGroupDetails=>{
                if(getGroupDetails.length>0){
                    let groupIdArray=getGroupDetails;
                    // from the groupId list get record which is associated with formId
                    return(
                        organizationDbConnection
                        .select('Custom_Group_Id')
                        .first()
                        .from(ehrTables.customGroupAssociatedForms)
                        .whereIn('Custom_Group_Id', groupIdArray)
                        .where('Form_Id',formId)
                        .then(getId=>{
                            if(getId && Object.keys(getId).length>0){
                                return getId.Custom_Group_Id;
                            }
                            else{
                                return '';
                            }
                        })
                    );
                }
                else{
                    return '';
                }
            })
            .catch(error => {
                console.log('Error in getEmployeeAssociatedGroupId function .catch block.',error);
                return '';
            })
        );
    }
    catch(catchError){
        console.log('Error in getEmployeeAssociatedGroupId function main catch block',catchError);
        return '';
    }
}


/** Get workschedule week off details. If work scheduleId exist then get based on that else consider all the work schedule */
async function getWeekOffBasedOnWorkschedule(organizationDbConnection,workScheduleId=0, dayId=0, weekNo=0){
    try{
        let weekOffQuery = organizationDbConnection(ehrTables.workScheduleWeekoff)
            .select('WorkSchedule_Id as workScheduleId', 'Day_Id', 'Week_Number','Duration as weekOffDuration')
            .orderBy('WorkSchedule_Id', 'asc');
            if(workScheduleId > 0){
                weekOffQuery = weekOffQuery
                .where('WorkSchedule_Id',workScheduleId);
            }
            if(dayId && weekNo){
                weekOffQuery = weekOffQuery
                .where('Day_Id',dayId)
                .where('Week_Number',weekNo)
            }
            return(
                weekOffQuery
                .then(getWorkscheduleWeekOff=>{
                    return getWorkscheduleWeekOff;
                })
                .catch(getWeekOffError=>{
                    console.log('Error in getWeekOffBasedOnWorkschedule function .catch block', getWeekOffError);
                    throw 'EM0231';
                })
            )
    } catch(getWorkscheduleWeekOffError){
        console.log('Error in getWeekOffBasedOnWorkschedule function main catch block', getWorkscheduleWeekOffError);
        throw 'EM0231';
    }
}

// Find the week of the month for the input date
function getWeekOfMonth(date)
{
try{
    date = moment(date).date();
    let weekNo;

    if(date >= 1 && date <= 7)
    {
        weekNo = 1;
    }
    else if(date >= 8 && date <= 14)
    {
        weekNo = 2;
    }
    else if(date >= 15 && date <= 21)
    {
        weekNo = 3;
    }
    else if(date >= 22 && date <= 28)
    {
        weekNo = 4;
    }
    else
    {
        weekNo = 5;
    }
    return weekNo;
}
catch(error)
{
    console.log('Error in getWeekOfMonth function catch block',error);
    throw 'EM0242';
}
}

// function to get the weekoff duration based on the work schedule
async function getEmpWorkScheduleBasedWeekOffAndDuration(organizationDbConnection,dateRangeList,reportData,startDate=null,endDate=null){
try{
    let dateList=(dateRangeList && dateRangeList.length>0)?dateRangeList:[];
    // calculate date range list based on the start date and end date
    if(dateList.length===0 && startDate && endDate){
        dateList = [ ...Array(Date.parse(endDate)/86400000 - Date.parse(startDate)/86400000 + 1).keys()
            ].map(day =>  moment(startDate, "YYYY-MM-DD").add(day, 'days').format('YYYY-MM-DD')
        );
    }
    if(dateList.length>0){
        /** get the week off details for the work schedule  */
        let workscheduleWeekOffData=await getWeekOffBasedOnWorkschedule(organizationDbConnection);
        if(workscheduleWeekOffData.length>0){
            let weekOffData=[];
            /** iterate for the date list */
            for(let record of dateList){
                let dayNo = moment(record).day();
                // from moment package, it returns 0 if the day is sunday but we in our table we assign 7 for sunday
                if(Number(dayNo) === 0)
                    dayNo = 7;
                // Get the week number for a date
                let weekNo = getWeekOfMonth(record);
                // form the week off data
                weekOffData.push({
                    date:record,
                    weekNo:weekNo,
                    dayNo:dayNo
                })
            }
            /** form the report data by combining the response with week off data */
            reportData = reportData.map(obj => {
                let data = weekOffData.find(item => item.date === obj.activityDate)
                return {...obj, ...data}
            });
            // based on the work schedule,day and week number form the report data
            reportData= reportData.map(obj => {
                let data = workscheduleWeekOffData.find(item => item.workScheduleId === obj.workScheduleId && item.Day_Id === obj.dayNo && item.Week_Number === obj.weekNo)
                return {...obj, ...data}
            });
            /** when duration exist in the response then append weekoff as Yes else as No */
            reportData = reportData.map(activityData => {
                if(activityData.hasOwnProperty('weekOffDuration')){
                    activityData.isWeekOffDay='Yes';
                }
                else{
                    activityData.isWeekOffDay='No';
                    activityData.weekOffDuration=0;
                }
                return activityData
            });
        }
        else{
            console.log('No week off details exist');
            reportData = reportData.map(v => ({...v, isWeekOffDay: 'No',weekOffDuration:0}));
        }
        return reportData;
    }
    else{
        console.log('Date list does not exist.WeekOff need to formed based on the activity date list');
        throw 'EM0241';
    }
}
catch(error){
    console.log('Error in the getEmpWorkScheduleBasedWeekOffAndDuration function main catch block.',error);
    let errorCodeArray=['EM0231','EM0242'];
    throw errorCodeArray.includes(error)?error:'EM0241';
}
}

/** If the immediate reportees view only is 1 then get only the immediate reportees for the manager
 * If the immediate reportees view only is 0 then get the entire hierarchy for the manager.
* entire hierarchy means - Get the employees associated with the manager and if the associated employee is manager,
    then get the employees associated with that employee as well */
async function getManagerHierarchy(organizationDbConnection,logInEmpId,workScheduleId){
    try{
        return new Promise(function(resolve, reject) {
            organizationDbConnection(ehrTables.orgDetails)
            .select('Immediate_Reportees_View_Only')
            .then(orgDetails =>{
                if(orgDetails.length){
                    let immediateReporteesViewOnly = orgDetails[0].Immediate_Reportees_View_Only;
                    if(immediateReporteesViewOnly === 1){
                        let qryEmployeeId = organizationDbConnection(ehrTables.empJob)
                        .select('Employee_Id')
                        .where('Manager_Id', logInEmpId)
                            /**If the workschedule id is not empty then get the employees associated with the workschedule */
                        if(workScheduleId > 0){
                            qryEmployeeId = qryEmployeeId
                            .where('Work_Schedule',workScheduleId);
                        }
                        
                        qryEmployeeId.then(employeeDetails =>{
                            let employeeIds = [];
                            if(employeeDetails.length > 0){
                                for(let employee of employeeDetails){
                                    employeeIds.push(employee['Employee_Id']);
                                }
                            }
                            resolve(employeeIds);
                        })
                        .catch(catchError => {
                            console.log('Error while retrieving the employees associated to the manager', catchError);
                            reject('EM0239');
                        })
                    } else {
                        //Get employees associated to the manager upto 5 level for now, we need to find alternate solution for this.
                        organizationDbConnection.raw("SELECT A.Employee_Id as EmployeeA, B.Employee_Id as EmployeeB, C.Employee_Id as EmployeeC, D.Employee_Id as EmployeeD, E.Employee_Id as EmployeeE FROM emp_job AS A LEFT JOIN (SELECT N.Manager_Id, N.Employee_Id FROM emp_job as N where N.Emp_Status = 'Active' UNION ALL SELECT NULL, NULL) B ON B.Manager_Id = A.Employee_Id LEFT JOIN (SELECT N.Manager_Id, N.Employee_Id FROM emp_job as N where N.Emp_Status = 'Active' UNION ALL SELECT NULL, NULL) C ON C.Manager_Id = B.Employee_Id LEFT JOIN (SELECT N.Manager_Id, N.Employee_Id FROM emp_job as N where N.Emp_Status = 'Active' UNION ALL SELECT NULL, NULL) D ON D.Manager_Id = C.Employee_Id LEFT JOIN (SELECT N.Manager_Id, N.Employee_Id FROM emp_job as N where N.Emp_Status = 'Active' UNION ALL SELECT NULL, NULL) E ON E.Manager_Id = D.Employee_Id WHERE A.Employee_Id = "+logInEmpId+" and A.Emp_Status = 'Active'")   
                        .then(async(employeeDetails)=>{
                            //Need to remove this console after testing
                            let employeeIds = [];
                            for(let employee of employeeDetails[0]){
                                employee['EmployeeA'] ? employeeIds.push(employee['EmployeeA']) : null;
                                employee['EmployeeB'] ? employeeIds.push(employee['EmployeeB']) : null;
                                employee['EmployeeC'] ? employeeIds.push(employee['EmployeeC']) : null;
                                employee['EmployeeD'] ? employeeIds.push(employee['EmployeeD']) : null;
                                employee['EmployeeE'] ? employeeIds.push(employee['EmployeeE']) : null;
                            }
                            
                            uniqueArray = employeeIds.filter(function(elem, pos) {
                                return employeeIds.indexOf(elem) == pos;
                            })
                            //Need to remove this console after testing
                            console.log("uniqueArray",uniqueArray);
                            
                            resolve(uniqueArray);
                        })
                        .catch(getEmployeesError=>{
                            console.log('Error in retrieving the hierarchy of the employees associated to the manager.', getEmployeesError);
                            reject('EM0229');
                        })
                    }
                }
            })
            .catch(orgDetailsCatchError => {
                console.log('Error while retrieving the organization details .catch block', orgDetailsCatchError);
                throw('EM0240');
            })
        })
    }catch(getManagerHierarchyError){
        console.log('Error in getManagerHierarchy() function main catch block', getManagerHierarchyError);
        throw('EM0229');
    }
}


exports.getDateTimeBasedOnTimeZoneOffset = getDateTimeBasedOnTimeZoneOffset;
exports.getTimeInHoursMinutesSeconds = getTimeInHoursMinutesSeconds;
exports.formMinMaxTimeByZone = formMinMaxTimeByZone;
exports.getTimeZone = getTimeZone
exports.getEmployeeIds=getEmployeeIds;
exports.getAdminEmployeeIds=getAdminEmployeeIds;
exports.getAllManagerIds=getAllManagerIds;
exports.getDurationBasedOnStatus=getDurationBasedOnStatus;
exports.groupDataBasedOnInputField=groupDataBasedOnInputField;
exports.getOrganizationMonitoringMode=getOrganizationMonitoringMode;
exports.getEmployeeAssociatedGroupId=getEmployeeAssociatedGroupId;
exports.getWeekOffBasedOnWorkschedule=getWeekOffBasedOnWorkschedule;
exports.getWeekOfMonth=getWeekOfMonth;
exports.getEmpWorkScheduleBasedWeekOffAndDuration=getEmpWorkScheduleBasedWeekOffAndDuration;
exports.getManagerHierarchy=getManagerHierarchy;
module.exports.getEmployeeEmail = getEmployeeEmail;
module.exports.sendEmailNotifications = sendEmailNotifications;
module.exports.getEmployeeName=getEmployeeName;
module.exports.updateLockAppTime = updateLockAppTime;