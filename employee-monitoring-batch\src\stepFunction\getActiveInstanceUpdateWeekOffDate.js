'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex'); 
// require table alias function
const { appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { defaultValues } = require('../common/appConstants');

//this function is used to prepare data(active instance) in weekoff_dates_manager table.
module.exports.getActiveInstances  = async(event,context) =>{
    let appmanagerDbConnection;
    try{
        // get input data
        let inputStatus=event.status;
        if(inputStatus && inputStatus.toLowerCase()==='open' || inputStatus && inputStatus.toLowerCase==='failed')
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.
            Incase of input status as 'Open' or 'Failed' proceed to the purge process */
            console.log('Event triggered to process remaining records so move to step2');
            let response={
                nextStep:'Step2',
                input:{'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            // check whether connection exist or not
            if(Object.keys(databaseConnection).length){
                // form app manager database connection
                appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                //get all the active instances
                let activeSubscribedUsers= await commonLib.func.getInstancesBasedOnPlanStatus(appmanagerDbConnection,defaultValues.activeStatus);
                let insertDate=new Date();
                let inputData=[];
                let insertIntoWeekOffDateManagerTable;
                if(activeSubscribedUsers.length>0)
                {
                    for(let i=0;i<activeSubscribedUsers.length;i++)
                    {
                        inputData.push({
                            Org_Code: activeSubscribedUsers[i],
                            Update_Status: "Open",
                            Update_On: insertDate
                        })
                    }
                    insertIntoWeekOffDateManagerTable = await insertIntoWeekOffDateManager(appmanagerDbConnection,inputData);
                }
                if(insertIntoWeekOffDateManagerTable)
                {   
                    appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                    let response={
                        nextStep:'Step2',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next set of instances from getActiveInstances.'          
                    }
                    return response;
                }
                else{
                    appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                    let response ={
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Error Occured while Updating the table in getActiveInstances.'
                    };
                    return response;
                }
            }
            else{
                let response ={
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Occured while getting the database connection in getActiveInstances.'
                };
                return response;
            }
        }   
    }
    catch(e){
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        console.log("error occured while inserting in WeekOffDate manager table in getActiveInstances",e);
        let response= {
            nextStep:'Error',
            input:{'status':inputStatus},
            message:'Error occured while inserting in WeekOffDate manager table in getActiveInstances.'
        };
        return response;
    }
};
//insert into WeekOffDateManager table by first deleting all the data and then insert new instances
async function insertIntoWeekOffDateManager(appmanagerDbConnection,inputData)
{
    try{
        return(
            appmanagerDbConnection(appManagerTables.weekOffDatesManager)
            .del()
            .then(()=>{
                return(
                    appmanagerDbConnection(appManagerTables.weekOffDatesManager)
                    .insert(inputData)
                    .then(()=>{
                        return true;
                    })
                    .catch((e)=>{
                        console.log("error occured while inserting in the weekoff_dates_manager table appmanagerDbConnection",e);
                        return false;
                    })
                )
            })
            .catch((e)=>{
                console.log("error while deleting the record in weekoff_dates_manager table",e);
                return false;
            })
        )
    }
    catch(e){
        console.log("error occured while inserting in the  weekoff_dates_manager table main catch block",e);
        return false;
    }
}
