const formName = {
    ipWhitelist: 'IP Whitelisting',
    tax : 'Tax',
    employeeMonitorSettings:'Productivity Monitoring',
    teamMembers:'Members',
    attendance:'Attendance',
    myActivity: 'My Activity',
    myteamActivity: 'My Team Activity',
    employeeAdmin:'Employee Admin',
    leaves: 'Leaves',
    holidays:'Holidays',
    admin: 'Admin',
    payrollAdmin: 'Payroll Admin',
    employeeDetails:'Employee Details',
    activityTrackerReports: 'Reports',
    activityDashboard:'Activity Dashboard',
    assets:'Assets',
    workforceAnalytics:'Workforce Analytics',
    dataLossPrevention:'Data Loss Prevention',
    fileTransfers:'File Transfers' 
}

const formIds = {
    activityTracker: 197,
    myActivity: 198,
    myTeamActivity: 199,
    teamMembers: 200,
    empMonitoringReports: 204,
    empMonitorSettings: 196,
    leaves: 31,
    shortTimeOff:128,
    compensatoryOff:139,
    advanceSalary:53,
    bonus:46,
    commission:48,
    deductions:49,
    loan:54,
    reimbursement:50,
    shiftAllowance:56,
    attendance:29,
    resignation:34,
    timesheets:23,
    transfer:33,
    employeeTravel:35,
    incomeUnderSection24:192,
    performanceAssesment:84,
    deferredLoan:73,
    taxDeclaration:61,
    inbox:36,
    hraDeclarations:119,
    admin:22,
    superAdmin:147,
    activityDashboard:211,
    assets:212,
    employeeAdmin:148,
    billing: 213,
    pmWorkSchedule:221,
    workforceAnalytics:223,
    customEmployeeGroups:180,
    productivityMonitoringAdmin:225,
    dataLossPrevention:230,
    fileTransfers:229,
    ipWhitelisting:191,
    holidays:8,
    holidayTypes:136,
    employeeDataManagement:236
}

const roles = {
    roleAdd : 'Role_Add',
    roleUpdate : 'Role_Update',
    roleDelete : 'Role_Delete',
    roleView : 'Role_View'
}

const systemLogs = {
    roleAdd: 'Add',
    roleUpdate: 'Update',
    roleDelete: 'Delete'
}

const defaultValues = {
  s3LogoPath:'https://s3.ap-south-1.amazonaws.com',
  integrationUrlConstant:'subscription',
  reportUrlConstant :'hr-reports',
  departmentHierarchyUrlConstant :'departments',
  hrReportsUrlConstant: 'view-hrreports',
  yearStartDate:'01-01',
  yearEndDate:'12-31',
  activeStatus:['Active'],
  empMonitorUserCount:30,
  stealthS3DefaultPath:'activitytracker/agent',
  dashboardRedirectionUrl:'/productivity-monitoring/activity-dashboard',
  employeeMonitoringReasonId:16,
  defaultRetentionPeriod:3, // By default 3 months
  defaultIdleTimeSettings:20,
  activityStatusArray:['active','not active','idle'],
  internetStatusArray:['online','offline'],
  numberOfActivityDataForAdmin:5, // for admin we present 5 activity records in email template
  numberOfActivityDataForManager:10, // for manager we present 10 activity records in email template
  activityTrackerUrl:'/productivity-monitoring/activity-tracker',
  appUrlTemplateColorCodes:['#A08CFF', '#82F2DC', '#FF8C8C', '#EFD100', '#00EF8F'],
  defaultLoggingLevel:'Info',
  customGroupSettingsFormId:196,
  browserList:['chrome','Chrome','google chrome','Google Chrome','iexplore','Iexplore','firefox','Firefox',
  'opera','Opera','msedge','Msedge','Safari','safari','edge','Edge','GeckoMain','geckomain'],
  activeAndNotActiveStatus:['Active','Not Active'],
  defaultTimeZone:'Asia/Kolkata',
  empMonitorInstanceCount:30,
  activeInstanceToBeProcessed:30
}

const signInRestAPI = {
    signInAPI: 'https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=',
    refreshTokenAPI:'https://securetoken.googleapis.com/v1/token?key=',
    refreshTokenGrantType:'refresh_token',
    refreshTokenExpiresIn: 90 // In days, configurable and changable
    
}

const s3FileUpload = {
    contentType: 'image/png',
    contentEncoding:'base64',
    txtFileType: 'text/plain',
    defaultEncryption:'AES256',
    binaryFile:'application/octet-stream'
}

const awsSesTemplates = {
    activityTrackerInvite: 'ActivityTrackerInvite',
    notifyEmployee:'notifyEmployee',
    hrmsOnboardMember:'HrmsOnboardMember',
    adminInsightsNotification:'adminInsightsNotification',
    employeeInsightsNotification:'employeeInsightsNotification'
}

const urlEncryption = {
    encryptionKey : 'pD!m*LsZQmT4'
}

const employeeMonitorDefaultValues = {
    activityDataExpiresIn : 30 //days
}

const signInLinkUrl = {
    localFirebaseSigninLink: 'http://localhost/hrapponline/auth?signinEmailLink=1&inviteData='
}

const empMonitoringVerificationLink = {
    localVerificationLink: 'http://localhost/hrapponline/auth?inviteData='
}

module.exports.formName = formName;
module.exports.roles = roles;
module.exports.systemLogs = systemLogs;
module.exports.defaultValues = defaultValues;
module.exports.signInRestAPI = signInRestAPI;
module.exports.s3FileUpload = s3FileUpload;
module.exports.awsSesTemplates = awsSesTemplates;
module.exports.formIds = formIds;
module.exports.urlEncryption = urlEncryption;
module.exports.employeeMonitorDefaultValues = employeeMonitorDefaultValues;
module.exports.signInLinkUrl = signInLinkUrl;
module.exports.empMonitoringVerificationLink = empMonitoringVerificationLink;