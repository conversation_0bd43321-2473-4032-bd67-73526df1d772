// require table alias
const tables = require('../common/tableAlias');
let ehrTables = tables.ehrTables;
let appManagerTables = tables.appManagerTables;


// function to retrieve organization subscribed plan
async function getOrganizationSubscribedPlan(appManagerConnection,orgCode){
    try{
        let subscribedDashboard='';
        return(
            // get the subscribed dashboard for the instance from plan details
            appManagerConnection(ehrTables.hrappRegisterUser+' as HRU')
            .select('HPD.Dashboard')
            .innerJoin(ehrTables.billingRate + ' as BR', 'HRU.Billing_Id', 'BR.Billing_Id')
            .innerJoin(appManagerTables.hrappPlanDetails + ' as HPD', 'BR.Plan_Id', 'HPD.Plan_Id')
            .where('HRU.Org_Code',orgCode)
            .then(async(getData) => {
                if(getData.length>0){
                    subscribedDashboard=getData[0].Dashboard;
                }
                else{
                    console.log('No plan details associated for '+orgCode+' instance');
                }
                return subscribedDashboard;
            }).catch(function (catchErrror) {
                console.log('Error in getOrganizationSubscribedPlan function .catch block', catchErrror);
                throw 'DB0030';
            })
        );
    }catch(mainCatchError){
        console.log('Error in getOrganizationSubscribedPlan function main catch block', mainCatchError);
        throw 'DB0030';       
    }
}

module.exports.getOrganizationSubscribedPlan = getOrganizationSubscribedPlan;