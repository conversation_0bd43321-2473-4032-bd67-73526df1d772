'use strict';
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require common function
const commonFunction=require('./commonFunctions');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let appManagerDbConnection='';
let orgDbConnection='';

/** function to get the record based on frequency. And process the each record and move to step2 */
module.exports.getRecordBasedOnFrequency  = async(event, context) =>{
    try{
        console.log("Inside getRecordBasedOnFrequency function ", event)
        let frequency='';
        let jobId='';
        let orgCode='';
        let firstCall=0;
        let triggerType = 1;
        let schedule = 0;

        // get the frequency from the input params
        if(event){
            if(event.frequency){
                frequency=(event.frequency).toLowerCase();
                firstCall=1;
                triggerType = (event.triggerType)
                schedule = event.schedule
            }
            else{
                frequency=(event.input)?(event.input.frequency).toLowerCase():'';
                triggerType = event.input && event.input.triggerType ? (event.input.triggerType) : 1
            }
        }
        if(frequency){
            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            // check whether connection exist or not
            if(Object.keys(databaseConnection).length){
                // get app manager database connection
                appManagerDbConnection=knex(databaseConnection.AppManagerDb);
                /** This block is executed during the first trigger. We need to updated the status as 'Open'
                 *  in schedule table based on frequency */
                if(firstCall){

                    let jobIds = [];
                    if(triggerType === 1 && (schedule===2 || schedule===3)){
                        let dataSchedulerDetails = await appManagerDbConnection(appManagerTables.dataIntegrationSchedule)
                        .select('Org_Code', appManagerDbConnection.raw("GROUP_CONCAT(Job_Id) as Job_Id"))
                        .where('Frequency',frequency).andWhere('Trigger_Type',triggerType).groupBy('Org_Code')

                        if(dataSchedulerDetails && dataSchedulerDetails.length > 0){
                            for(let dataScheduler of dataSchedulerDetails){
                                let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection, dataScheduler.Org_Code);
                                if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                                    let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                                    // make organization database connection
                                    let organizationDbConnection = await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region, dataScheduler.Org_Code, 0, additionalHeaders);
                                    if(Object.keys(organizationDbConnection).length){
                                        orgDbConnection=knex(organizationDbConnection.OrganizationDb);
                                        let result = commonFunction.calculateActivityDateBasedOnZone(orgDbConnection, schedule, 2);
                                        if(result){
                                            dataScheduler.Job_Id.split(',').forEach(jobId => jobIds.push(jobId.trim()))
                                        }
                                    }
                                }
                            }
                        }
                    }

                    await appManagerDbConnection(appManagerTables.dataIntegrationSchedule)
                        .update({
                            Status:'Open',
                            Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                        })
                        .where('Frequency',frequency)
                        .andWhere('Trigger_Type',triggerType)
                        .where(qb => {
                            if(jobIds && jobIds.length > 0){
                                qb.whereIn('Job_Id', jobIds);
                            }
                        })
                        .then(()=>{
                            console.log('Open status updated for '+frequency+' frequency instances');
                        })
                        .catch(function (catchError) {
                            console.log('Error while updating open status in schedule table',catchError);
                            throw catchError;
                        })
                }
                // Get the record based on the frequency from schedule table
                return(
                    appManagerDbConnection
                    .select('*')
                    .first()
                    .from(appManagerTables.dataIntegrationSchedule)
                    .where('Status','Open')
                    .where('Frequency',frequency)
                    .andWhere('Trigger_Type',triggerType)
                    .then(async(getDetails)=>{
                        // check data exist or not
                        if(getDetails && Object.keys(getDetails).length>0){
                            jobId=getDetails.Job_Id;
                            let orgCode=getDetails.Org_Code;
                            let processType=getDetails.Data_Push_Pull;
                            //Form additional headers
                            let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection,orgCode);
                            if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                                

                                // make organization database connection
                                let organizationDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                                if(Object.keys(organizationDbConnection).length){
                                    orgDbConnection=knex(organizationDbConnection.OrganizationDb);
                                    let updateParams={
                                        Status:'Inprogress',
                                        Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                    }
                                    // Update the job status as inprogress in schedule table before starting the execution
                                    let updateStatus=await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection,updateParams,jobId);
                                    console.log('Update inprogress status in schedule table for '+orgCode+' instance -',updateStatus);
                                    if(updateStatus==='success'){
                                        // get the current time based on mainbranch location
                                        let getOrgTimeZone = await commonLib.func.getMainBranchLocationTimeZone(orgDbConnection);
                                        getOrgTimeZone ? getOrgTimeZone : 'Asia/Kolkata';
                                        let currentDate=moment().tz(getOrgTimeZone).format('YYYY-MM-DD');
                                        // form the start and end date based on frequency
                                        let dateRange= await commonFunction.formDateRangeBaseOnFrequency(currentDate,frequency);
                                        if(Object.keys(dateRange).length>0){
                                            // insert the new log for the job in data integration log table
                                            return(
                                                orgDbConnection(ehrTables.dataIntegrationLog)
                                                .insert({
                                                    Job_Id:jobId,
                                                    Start_Date:dateRange.startDate,
                                                    End_Date:dateRange.endDate,
                                                    Updated_On:moment().format('YYYY-MM-DD HH:mm:ss'),
                                                    Reason: 'Started'
                                                })
                                                .then(async(insertLogData) =>{
                                                    let logId=insertLogData[0];
                                                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                    orgDbConnection?orgDbConnection.destroy():null;
                                                    // based on the process type define the next step
                                                    if(processType.toLowerCase()==='push')
                                                    {
                                                        let response = 
                                                        {
                                                            nextStep:'Step2',
                                                            input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':logId, 'triggerType': triggerType},
                                                            message:'Process data and upload it based on integration type.'
                                                        };
                                                        return response;
                                                    }
                                                    else{
                                                        let response = 
                                                        {
                                                            nextStep:'Step3',
                                                            input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':logId, 'triggerType': triggerType},
                                                            message:'Download the file and process data based on integration type.'
                                                        };
                                                        return response;
                                                    }
                                                })
                                                .catch(async(error) => {
                                                    console.log('Error while inserting log table in getRecordBasedOnFrequency function .catch block',error);
                                                    await updateFailureInScheduleTable(appManagerDbConnection,jobId);
                                                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                    orgDbConnection?orgDbConnection.destroy():null
                                                    let response = 
                                                    {
                                                        nextStep:'End',
                                                        input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                                                        message:'Error from step1 .catch block'
                                                    };
                                                    return response;
                                                })
                                            );
                                        }
                                        // When error in calculating the start and end range then update the failure status in schedule table
                                        else{
                                            await updateFailureInScheduleTable(appManagerDbConnection,jobId);
                                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                            orgDbConnection?orgDbConnection.destroy():null;
                                            let response = 
                                            {
                                                nextStep:'End',
                                                input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                                                message:'Error while calculating date range based on frequency.'
                                            };
                                            return response;
                                        }
                                    }
                                    else{
                                        console.log('Error in updating status in schedule table');
                                        await updateFailureInScheduleTable(appManagerDbConnection,jobId);
                                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                        orgDbConnection?orgDbConnection.destroy():null;
                                        let response = 
                                        {
                                            nextStep:'End',
                                            input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                                            message:'Error while updating the status.'
                                        };
                                        return response;
                                    }
                                }
                                else{
                                    console.log('Error while making organization database connection');
                                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                    orgDbConnection?orgDbConnection.destroy():null
                                    let response = 
                                    {
                                        nextStep:'End',
                                        input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                                        message:'Error while making organization database connection'
                                    };
                                    return response;
                                }
                            } else{
                                console.log('Error while getting the organization data region');
                                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                orgDbConnection?orgDbConnection.destroy():null
                                let response = 
                                {
                                    nextStep:'End',
                                    input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                                    message:'Error while getting the organization data region'
                                };
                                return response;
                            }
                        }
                        else{
                            console.log('No records to process');
                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                            orgDbConnection?orgDbConnection.destroy():null
                            let response = 
                            {
                                nextStep:'End',
                                input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                                message:'No records to process. So stop the execution.'
                            };
                            return response;
                        }
                    })
                    .catch(error => {
                        console.log('Error in getRecordBasedOnFrequency function .catch block',error);
                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                        orgDbConnection?orgDbConnection.destroy():null
                        let response = 
                        {
                            nextStep:'End',
                            input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                            message:'Error from step1 .catch block'
                        };
                        return response;
                    })
                );
            }
            else{
                console.log('Error while making appmanager database connection.');
                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                orgDbConnection?orgDbConnection.destroy():null
                let response = 
                {
                    nextStep:'End',
                    input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                    message:'Error while making appmanager database connection.'
                };
                return response;
            }
        }
        else{
            console.log('Input Frequency is empty. So stop the execution');
            appManagerDbConnection ? appManagerDbConnection.destroy():null;
            orgDbConnection?orgDbConnection.destroy():null
            let response = 
            {
                nextStep:'End',
                input:{'frequency':frequency,'jobId':jobId,'orgCode':orgCode,'logId':''},
                message:'Input is empty so stop the execution.'
            };
            return response;
        }
    }
    catch(error){
        console.log('Error in getRecordBasedOnFrequency function main catch block.', error);
        appManagerDbConnection ? appManagerDbConnection.destroy():null;
        orgDbConnection?orgDbConnection.destroy():null
        let response = 
        {
            nextStep:'End',
            input:{'frequency':'','jobId':'','orgCode':'','logId':''},
            message:'Error in step1 main catch block.'
        };
        return response;
    }
};

// function to update failure status in schedule table
async function updateFailureInScheduleTable(appManagerDbConnection,jobId){
    try{
        console.log('Inside updateFailureInScheduleTable function');
        let updateFailureParams={
            Status:'Failure',
            Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
        }
        await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection,updateFailureParams,jobId);
        return 'success';
    }
    catch(error){
        console.log('Error in updateFailureInScheduleTable function main catch block.', error);
        return 'error';
    }
};
