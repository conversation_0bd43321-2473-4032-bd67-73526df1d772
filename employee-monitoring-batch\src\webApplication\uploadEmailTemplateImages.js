// This is the API to upload images which is used for email templates
module.exports.uploadEmailTemplateImages = (event, context, callback) => {
    try{
        // require aws-sdk to use aws services
        const AWS = require('aws-sdk');
        // require constants file
        const { s3FileUpload } = require('../common/appConstants');
        // Create object for s3 bucket
        const s3 = new AWS.S3({ region: process.env.region });
        // Parse and get event
        event = JSON.parse(event.body);
        // get event values
        let { base64Content, imagePath } = event;
        // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
        s3.headObject({ Bucket: process.env.logoBucket, Key: imagePath },function(err,data){
            // if image with the name already exist then no need to allow them to upload file in s3
            if(data){
                console.log('Image with name already exists. Please try with some other names');
                callback(null,'Image with name already exists. Please try with some other names');
            }else{
                // remove unwanted characters from bodyContent
                base64Content = new Buffer.from(base64Content.replace(/^data:image\/\w+;base64,/, ""), 'base64');

                let params = {
                    Body: base64Content,
                    Bucket: process.env.logoBucket,
                    Key: imagePath,
                    ACL: 'public-read',
                    ContentEncoding: s3FileUpload.contentEncoding,
                    ContentType: s3FileUpload.contentType,
                };
                s3.upload(params, function (s3FileError, s3FileSuccess) {
                    if (s3FileError) {
                        console.log('Error while uploading files in uploadEmailTemplateImages() function if block.', s3FileError); // an error occurred
                        callback(null,'Error while uploading the email template images in s3 bucket');
                    }
                    else {
                        console.log('Inside uploadEmailTemplateImages() file uploaded successfully in AWS S3 bucket.');
                        callback(null,'Image uploaded successfully.');
                    }
                });
            }
        });
    }catch(uploadEmailTemplateImagesError){
        console.log('Error in uploadEmailTemplateImages() function catch block.',uploadEmailTemplateImagesError);
        callback(null,'Could not able to upload email template images');
    }
};