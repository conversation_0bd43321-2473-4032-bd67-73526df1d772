'use strict';
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require constant value
const { defaultValues }=require('../common/appConstants');
// require common function
const { updateSummaryDataInMasterTable,updateSummaryStatusBasedOnId }=require('./commonFunctions');
const { updatekeyBasedOnInputCategory,calculateUrlAndBrowserTime,calculateAppAndBrowserTime,calculateProductivityBasedOnAppUrlData }=require('../common/activityCommonFunctions');
const {groupDataBasedOnInputField, updateLockAppTime } = require('../webApplication/employeeMonitoringCommonFunction');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let summarizationStatusTable;
let inputStatus;
let orgCode='';
let inputSource;
let organizationDbConnection='';
let appmanagerDbConnection='';

// function to trigger daily productivity summary
module.exports.triggerProductivitySummary  = async(event, context) =>{
    try{
        console.log('Inside triggerProductivitySummary function',event);
        // get input data
        let inputData=event;
        inputStatus=inputData.status;
        orgCode=inputData.orgCode;
        inputSource=inputData.process;
        summarizationStatusTable=ehrTables.appurlActivitySummarizationStatus;
        let currentDateBasedOnTimeZone;
        
        let getDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appmanagerDbConnection=knex(getDbConnection.AppManagerDb);
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
        
        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
            

            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length>0){
                organizationDbConnection = knex(databaseConnection.OrganizationDb);
                
                // get the summarization details from status table
                return (
                    organizationDbConnection(summarizationStatusTable)
                    .select('AppUrl_Summarization_Id as appUrlSummarizationId','AppUrl_Summarization_Date as appUrlSummarizationDate')
                    .where('Transaction_Status','Inprogress')
                    .then(async(getSummarizationDetails) =>{
                        if(getSummarizationDetails.length>0){
                            let appUrlSummarizationId=getSummarizationDetails[0].appUrlSummarizationId;
                            let appUrlSummarizationDate=getSummarizationDetails[0].appUrlSummarizationDate;
                            // calculate the date based on default timezone
                            currentDateBasedOnTimeZone= moment.tz(moment(),defaultValues.defaultTimeZone).format('YYYY-MM-DD');
                            console.log('currentDateBasedOnTimeZone',currentDateBasedOnTimeZone,appUrlSummarizationDate)
                            // calculate the date difference between current date and the last summary date(IST)
                            let dateDifference = moment(currentDateBasedOnTimeZone).diff(moment(appUrlSummarizationDate), 'days');
                            console.log('Difference between current date and last summarization date for '+ orgCode +' instance',dateDifference);
                            if(dateDifference>=0){
                                try{
                                    // get the activity date list based on summarization date
                                    let activityDateList=await getActivityDateList(organizationDbConnection,appUrlSummarizationDate);
                                    if(activityDateList.length>0){
                                        let numberOfRecords=activityDateList.length;
                                        let productivitySummaryArray=[];
                                        let count=0;
                                        // iterate the loop for the activity date list
                                        for(let i=0;i<numberOfRecords;i++){
                                            count++;
                                            // get the activity date
                                            let activityDate=activityDateList[i].Activity_Date;
                                            console.log('Processing for activity date : ',activityDate)

                                            // get the employeeId array for the activity date in app summary table
                                            let employeeIdArray=await organizationDbConnection(ehrTables.appActivityDailySummary)
                                                                .pluck('Employee_Id').where('Activity_Date',activityDate).groupBy('Employee_Id')
                                                                                    
                                            if(employeeIdArray.length>0){
                                                /** validate whether summary processed for this activity date or not */
                                                let getProcessedEmployeeIds=await organizationDbConnection(ehrTables.appurlActivityDailySummary)
                                                .pluck('Employee_Id').where('Activity_Date',activityDate)

                                                if(getProcessedEmployeeIds.length>0){
                                                    console.log('Summary data already exist for this activity date', getProcessedEmployeeIds);
                                                    /** form the employeeId array for which summary is not processed already */
                                                    let idArray = employeeIdArray.filter(x => !getProcessedEmployeeIds.includes(x));
                                                    employeeIdArray=idArray
                                                }
                                            
                                                if(employeeIdArray.length>0){
                                                    // get the app and url summary details
                                                    let appSummary=await getApplicationDetails(organizationDbConnection,activityDate,employeeIdArray);
                                                    let urlSummary=await getUrlDetails(organizationDbConnection,activityDate,employeeIdArray);
                                                    let resultAppUrlData=[];
                                                    // combine the app and url summary based on employeeId and activity date
                                                    if(appSummary.length>0 || urlSummary.length>0){
                                                        resultAppUrlData = appSummary.map(obj => {
                                                            let data = urlSummary.find(item =>  item.employeeId === obj.employeeId && item.activityDate === obj.activityDate);    
                                                            return {...obj, ...data};
                                                        });
                                                    }
                                                    // based on the app and url data calculate the productivity details
                                                    if(resultAppUrlData.length>0){
                                                        let fixedDailyWorkHours = await organizationDbConnection(ehrTables.employeeMonitorSettings)
                                                                            .select('Fixed_Daily_Work_Hours')
                                                                            .then(async(fixedWorkHours) =>{
                                                                                return fixedWorkHours && fixedWorkHours.length > 0 ? fixedWorkHours[0].Fixed_Daily_Work_Hours : 0;
                                                                            }).catch(function (catchError) {
                                                                                console.log('Error while getting the fixed daily work hours settings .catch block', catchError);
                                                                                return 0;
                                                                            });
                                                        console.log("fixedDailyWorkHours: ", fixedDailyWorkHours, fixedDailyWorkHours*3600);

                                                        resultAppUrlData=await calculateProductivityBasedOnAppUrlData(resultAppUrlData, fixedDailyWorkHours);
                                                    }
                                                    productivitySummaryArray=[...productivitySummaryArray,...resultAppUrlData];
                                                }
                                                else{
                                                    console.log('Activity date '+ activityDate +  ' is processed already so process for next date',productivitySummaryArray);
                                                    /** if all the activity date record processed already then update the status else process the remaining records */
                                                    if(numberOfRecords===count && productivitySummaryArray.length===0){
                                                        await updateLockAppTime(organizationDbConnection, activityDateList[i].Activity_Date);
                                                        throw 'Data already processed.';
                                                    }
                                                }
                                            }
                                            else{
                                                await updateLockAppTime(organizationDbConnection, activityDateList[i].Activity_Date);
                                                console.log('EmployeeIds does not exist in app summary table.');
                                                throw 'App/url productivity details does not exists.';
                                            }
                                            //call function to update the lock app time and system uptime without lock app time
                                            await updateLockAppTime(organizationDbConnection, activityDateList[i].Activity_Date);
                                        }

                                        if(productivitySummaryArray.length>0){
                                            // insert the productivity summary details
                                            let insertData=await insertProductivitySummary(organizationDbConnection,productivitySummaryArray,appUrlSummarizationId)
                                            if(insertData==='success'){
                                                await updateSuccessStatusInMasterTable(currentDateBasedOnTimeZone,appUrlSummarizationDate,orgCode);
                                                console.log('Success status updated in manager table in '+orgCode+' instance');
                                                organizationDbConnection?organizationDbConnection.destroy():null;
                                                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;        
                                                return 'success';
                                            }
                                        }
                                        else{
                                            console.log('Error in calculating productivity details - ',productivitySummaryArray);
                                            throw 'Error in calculating productivity details.';
                                        }
                                    }
                                    else{
                                        console.log('No app summary data exists for the date - ',appUrlSummarizationDate);
                                        await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,'productivity','Success',appUrlSummarizationId,'App summary details does not exist');
                                        console.log('Success status updated in org status table in '+orgCode+' instance');   
                                        await updateSuccessStatusInMasterTable(currentDateBasedOnTimeZone,appUrlSummarizationDate,orgCode);
                                        organizationDbConnection?organizationDbConnection.destroy():null;
                                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;        
                                        return 'success';
                                    }
                                }
                                catch(error){
                                    console.log('Error in processing summarization process',error);
                                    if(error==='App/url productivity details does not exists.' || error==='Data already processed.'){
                                        // update the success status with reason in status table
                                        await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,'productivity','Success',appUrlSummarizationId,error);
                                        console.log('Success status updated in org status table in '+orgCode+' instance');
                                        await updateSuccessStatusInMasterTable(currentDateBasedOnTimeZone,appUrlSummarizationDate,orgCode);
                                        organizationDbConnection?organizationDbConnection.destroy():null;
                                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;        
                                        return 'success';
                                    }
                                    else{
                                        let errorMessages=['Error in getting the summary details','EM0174','EM0175','Error in calculating productivity details.','Error while inserting productivity summary details','Error in getting the activity date.',
                                        'Error in calculating productive url details.','Error in calculating app and browser details.'];
                                        let failureReason=errorMessages.includes(error)?error:'Error in processing summarization process';
                                        // update the failure status in status table
                                        await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,'productivity','Failed',appUrlSummarizationId,failureReason);
                                        console.log('Failed status updated in org status table in '+orgCode+' instance');
                                        // update the failure status in manager table
                                        let updateManagerTableInputs={
                                            Status:'Failed'
                                        }
                                        await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,appManagerTables.appurlActivitySummarizationManager);
                                        console.log('Failed status updated in app manager table for '+orgCode+' instance');
                                        organizationDbConnection?organizationDbConnection.destroy():null;
                                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                                        return 'success';
                                    }
                                }
                            }
                            else{
                                console.log('Productivity summary is processed already',dateDifference);
                                let updateManagerTableInputs={
                                    Status:'Success'
                                }
                                await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,'productivity','Success',appUrlSummarizationId,'No records to process');
                                console.log('Success status with reason updated in org status table in '+orgCode+' instance');
                                await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,appManagerTables.appurlActivitySummarizationManager);
                                console.log('Success status updated in app manager table for '+orgCode+' instance');
                                organizationDbConnection?organizationDbConnection.destroy():null;
                                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                                return 'success';    
                            }
                        }
                        else{
                            console.log('No records in inprogress status. So update the status and quit the process.');
                            let updateManagerTableInputs={
                                Status:'Success'
                            }
                            await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,appManagerTables.appurlActivitySummarizationManager);
                            console.log('Success status updated in app manager table for '+orgCode+' instance');
                            organizationDbConnection?organizationDbConnection.destroy():null;
                            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                            return 'success';
                        }
                    })
                )
            }
            else{
                console.log('Error in organization database connection for '+orgCode+' instance');
                organizationDbConnection?organizationDbConnection.destroy():null;
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                return 'error';
            }
        }
        else{
            console.log('Error in getting the data region for '+orgCode+' instance');
            organizationDbConnection?organizationDbConnection.destroy():null;
            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
            return 'error';
        }

    }catch (mainCatchError){
        console.log('Error in triggerProductivitySummary function main catch block.', mainCatchError);
        organizationDbConnection?organizationDbConnection.destroy():null;
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        return 'error';
    }
};

// function to get the activity details based on summarization date
async function getActivityDateList(organizationDbConnection,summarizationDate){
    try{
        // form the start and end date time
        let startDateTime=summarizationDate+' 00:00:00';
        let endDateTime=summarizationDate+' 23:59:59';
        return(
            organizationDbConnection(ehrTables.appActivityDailySummary)
            .select('Activity_Date')
            .whereBetween('Load_TimeStamp',[startDateTime,endDateTime])
            .groupBy('Activity_Date')
            .then(async(getActivityDate) => {
                if(getActivityDate.length>0){
                    return getActivityDate;
                }
                else{
                    console.log('Activity date does not exist')
                    return [];
                }
            })
            .catch(error=>{
                console.log('Error in getActivityDateList function .catch block -',error);
                throw 'Error in getting the activity date.';
            })
        )
    }
    catch(catchError){
        console.log('Error in getActivityDateList function main catch block -',catchError);
        throw 'Error in getting the activity date.';
    }
}

// function to get the application details based on employeeId and activity date
async function getApplicationDetails(organizationDbConnection,activityDate,employeeIdArray){
    try{
        return(
            organizationDbConnection(ehrTables.appActivityDailySummary)
            .select('Employee_Id as employeeId','Application_Id as applicationId','Application_Name as applicationName','Category as appCategory','Activity_Date as activityDate')
            .sum('Total_Activity_Duration as appTimeSpent')
            .whereIn('Employee_Id',employeeIdArray)
            .where('Activity_Date',activityDate)
            .groupBy('Employee_Id')
            .groupBy('Application_Id')
            .then(async(getAppDetails) => {
                if(getAppDetails.length>0){
                    // based on the category update the key name
                    let appData=await updatekeyBasedOnInputCategory(getAppDetails,'app');
                    // group the app details based on employeeId
                    let groupEmployeeWiseAppDetails = groupDataBasedOnInputField(appData, data => data.employeeId);
                    // get the employeeId list
                    let appUsedEmployeeIds=Array.from(groupEmployeeWiseAppDetails.keys());
                    let resultAppData=[];
                    for(let k=0;k<appUsedEmployeeIds.length;k++)
                    {
                        let groupAppData={};
                        // get the app details based on employeeId
                        let getAppDetails=groupEmployeeWiseAppDetails.get(appUsedEmployeeIds[k]);
                        // check whether app details exist for the employee
                        if(getAppDetails && getAppDetails.length>0){
                            groupAppData=await calculateAppAndBrowserTime(getAppDetails);
                        }
                        if(Object.keys(groupAppData).length>0){
                            resultAppData.push(groupAppData);
                        }
                        else{
                            throw 'Error in calculating app and browser details.';
                        }
                    }
                    if(resultAppData.length>0){
                        // append the activity date in the response
                        resultAppData = resultAppData.map(v => ({...v, activityDate: activityDate}));
                    }
                    return resultAppData;
                }
                else{
                    console.log('App summary details does not exist.');
                    return [];
                }
            })
            .catch(error=>{
                console.log('Error in getApplicationDetails function .catch block.', error);
                throw 'EM0174';
            })
        )

    }catch (catchError){
        console.log('Error in getApplicationDetails function main catch block.', catchError);
        throw (catchError==='Error in calculating app and browser details.')?(catchError):'EM0174';
    }
}

// function to get the url details based on employeeId and activity date
async function getUrlDetails(organizationDbConnection,activityDate,employeeIdArray){
    try{
        return(
            organizationDbConnection(ehrTables.urlActivityDailySummary)
            .select('Employee_Id as employeeId','Domain_Id as domainId','Domain_Name as domainName',
            'Category as urlCategory','Activity_Date as activityDate')
            .sum('Total_Activity_Duration as urlTimeSpent')
            .whereIn('Employee_Id',employeeIdArray)
            .where('Activity_Date',activityDate)
            .groupBy('Employee_Id')
            .groupBy('Domain_Id')
            .then(async(getUrlDetails) => {
                if(getUrlDetails.length>0){
                    let urlData=await updatekeyBasedOnInputCategory(getUrlDetails,'url');
                    // group the url details based on employeeId
                    let groupEmployeeWiseUrlDetails = groupDataBasedOnInputField(urlData, data => data.employeeId);
                    let urlUsedEmployeeIds=Array.from(groupEmployeeWiseUrlDetails.keys());
                    let resultUrlData=[];
                    for(let k=0;k<urlUsedEmployeeIds.length;k++)
                    {
                        let groupUrlData={};
                        // get the url details based on employeeId
                        let getUrlDetails=groupEmployeeWiseUrlDetails.get(urlUsedEmployeeIds[k]);
                        // check whether app details exist for the employee
                        if(getUrlDetails && getUrlDetails.length>0){
                            groupUrlData=await calculateUrlAndBrowserTime(getUrlDetails);
                        }
                        if(Object.keys(groupUrlData).length>0){
                            resultUrlData.push(groupUrlData);
                        }
                        else{
                            throw 'Error in calculating productive url details.';
                        }
                    }
                    if(resultUrlData.length>0){
                        // append the activity date in the response
                        resultUrlData = resultUrlData.map(v => ({...v, activityDate: activityDate}));
                    }
                    return resultUrlData;
                }
                else{
                    console.log('Url summary details does not exist.');
                    return [];
                }
            })
            .catch(error=>{
                console.log('Error in getUrlDetails function .catch block.', error);
                throw 'EM0175';
            })
        )

    }catch (catchError){
        console.log('Error in getUrlDetails function main catch block.', catchError);
        throw (catchError==='Error in calculating productive url details.')?catchError:'EM0175';
    }
}

// function to insert the productivity summary details
async function insertProductivitySummary(organizationDbConnection,summaryDataArray,appUrlSummarizationId){
    try{
        let updatedOn=moment.tz(moment(), defaultValues.defaultTimeZone).format('YYYY-MM-DD HH:mm:ss');
        // form the input params
        const promises = summaryDataArray.map(async field=>({
            Employee_Id: field.employeeId,
            Productive_Duration_In_Seconds:field.productiveTimeInSeconds,
            Productive_Duration_In_Minutes:field.productiveTimeInMinutes,
            Productive_Duration_In_HHMMSS:field.productiveTime,
            Unproductive_Duration_In_Seconds:field.unProductiveTimeInSeconds,
            Unproductive_Duration_In_Minutes:field.unProductiveTimeInMinutes,
            Unproductive_Duration_In_HHMMSS:field.unProductiveTime,
            Neutral_Duration_In_Seconds:field.neutralTimeInSeconds,
            Neutral_Duration_In_Minutes:field.neutralTimeInMinutes,
            Neutral_Duration_In_HHMMSS:field.neutralTime,
            User_Productive_Duration_In_Seconds:field.userProductiveDurationInSeconds,
            User_Productive_Duration_In_Minutes:field.userProductiveDurationInMinutes,
            User_Productive_Duration_In_HHMMSS:field.userProductiveDuration,
            User_Productivity_Percentage:field.userProductivityPercentageProductiveAndNeutral,
            User_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours:field.userProductivityPercentageBasedOnFixedDailyWorkHours,
            User_Productivity_Percentage_Productive_Only:field.userProductivityPercentageProductiveOnly,
            User_Prod_Percentage_Productive_Only_Based_On_Fixed_Hours:field.userProdPercentageProductiveOnlyBasedOnFixedHours,
            Activity_Date:field.activityDate,
            Load_Timestamp: updatedOn
        }));
        let summaryDetailsArray = await Promise.all(promises);

        return (
            organizationDbConnection
            .transaction(function (trx) {
                // insert the summary details
                return (
                    organizationDbConnection(ehrTables.appurlActivityDailySummary)
                    .insert(summaryDetailsArray)
                    .transacting(trx)
                    .then(async(insertSummaryData)=>{
                        console.log('Summary details inserted successfully',insertSummaryData)
                        return insertSummaryData;
                    })
                )
            })
            .then(async(result) => {
                let updateSuccessStatus=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,'productivity','Success',appUrlSummarizationId);
                console.log('Success status updated in org status table in '+orgCode+' instance',updateSuccessStatus);
                return (updateSuccessStatus)?(updateSuccessStatus):'';
            })
            .catch(async(catchError) => {
                console.log('Error in insertProductivitySummary function .catch block -',catchError);
                throw 'Error while inserting productivity summary details';
            })
        )
    }
    catch(error){
        console.log('Error in insertProductivitySummary function main catch block -',error);
        throw 'Error while inserting productivity summary details';
    }
}

// function to insert new summary status record
async function insertNewStatusRecord(organizationDbConnection,newSummarizationDate){
    try{
        console.log('Insert record for -',newSummarizationDate);
        return(
            organizationDbConnection(ehrTables.appurlActivitySummarizationStatus)
            .insert({
                AppUrl_Summarization_Date:newSummarizationDate,
                Transaction_Status:'Open',
                Summarization_Time:new Date()
            })
            .then(()=>{
                return 'success'
            })
            .catch(error=>{
                console.log('Error in insertNewStatusRecord function .catch block -',error);
                return 'error';
            })
        )
    }
    catch(catchError){
        console.log('Error in insertNewStatusRecord function main catch block -',catchError);
        return 'error';
    }
}

// function to update success status
async function updateSuccessStatusInMasterTable(currentDateBasedOnTimeZone,appUrlSummarizationDate,orgCode){
    try{
        let updateManagerTableInputs={};
        let maxSummaryDateBasedOnTimeZone= moment(currentDateBasedOnTimeZone).subtract(1,'d').format('YYYY-MM-DD');
        // calculate the date difference based on the last processed summary date with the current date
        let summaryDateDifference = moment(maxSummaryDateBasedOnTimeZone).diff(moment(appUrlSummarizationDate), 'days');
        // console.log('summaryDateDifference',summaryDateDifference,'maxSummaryDateBasedOnTimeZone',maxSummaryDateBasedOnTimeZone,'appUrlSummarizationDate',appUrlSummarizationDate);
        /** If the date difference is equal or greater than 0 then insert the new record else update the status  */
        if(summaryDateDifference>=0){
            // check whether any records are in either open or inprogress status and calculate the min date
            let getData=await organizationDbConnection(ehrTables.appurlActivitySummarizationStatus)
            .count('AppUrl_Summarization_Id as summaryRecordCount')
            .min('AppUrl_Summarization_Date as minDate')
            .whereIn('Transaction_Status',['Open','Inprogress'])
            /** If record does not exist then insert the new open record in status table */
            if(getData[0].summaryRecordCount===0){
                // form the new summarization date
                let newSummarizationDate=moment(appUrlSummarizationDate).add(1,'d').format('YYYY-MM-DD');
                // console.log('newSummarizationDate',newSummarizationDate);
                // function to insert new status record in status table
                let insertRecord=await insertNewStatusRecord(organizationDbConnection,newSummarizationDate);
                console.log('New record inserted in status table in '+orgCode+' instance.',insertRecord);
                updateManagerTableInputs.Status='Open';
                updateManagerTableInputs.AppUrl_Summarization_Date=newSummarizationDate;
                await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,appManagerTables.appurlActivitySummarizationManager);
                console.log('Update the open status and date in app manager table for '+orgCode+' instance');
            }
            else{
                console.log('There are some records in open or inprogress status. So no need to insert new records.');
                updateManagerTableInputs.Status='Open';
                // update the mininum date in the app manager table
                (getData[0].minDate)?updateManagerTableInputs.AppUrl_Summarization_Date=getData[0].minDate:'';
                await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,appManagerTables.appurlActivitySummarizationManager);
            }
            return 'success';    
        }
        else{
            updateManagerTableInputs.Status='Success';
            await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,appManagerTables.appurlActivitySummarizationManager);
            console.log('Success status updated in app manager table for '+orgCode+' instance');
            return 'success';
        }
    }
    catch(catchError){
        console.log('Error in updateSuccessStatusInMasterTable function main catch block -',catchError);
        return 'error';
    }
}
