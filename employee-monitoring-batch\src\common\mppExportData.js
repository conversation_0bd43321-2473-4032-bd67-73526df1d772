const { ehrTables } = require('../common/tableAlias');


async function getPositionMaster(organizationDbConnection, startDate, endDate){
    try {
        return organizationDbConnection(ehrTables.SFWPOrganizationStructure)
        .select('Originalpos_Id', 'Company_Id', 'Pos_Code', 'Pos_Name', 'Approved_Position', 'Warm_Bodies', 
            'Parent_Id', 'Parent_Path', 'Type', 'Is_Update', 'Status', 'Department_Code', 'Head_Division_Code', 'Org_Level', 
            'Job_Status', 'Job_Title_Code', 'Cost_Code', 'Lst_Work_Location', 'Lst_Grade_Code', 'Position_Level', 'Global_Grade')
    }catch(err){
        console.error('Error in getPositionMaster function main catch block', err);
        throw err;
    }
}

async function getHiringForeCastList(organizationDbConnection, startDate, endDate){

    try {
        return organizationDbConnection( ehrTables.mppHiringForecast + ' as MHF')
        .select('MHF.Original_Position_Id', 'MHF.Position_Title', 'MHF.Forecast_Year', 'MHF.Forecast_Month', 'MHF.No_Of_Position', 
            'GRP.Pos_Code as Group_Code', 'DIV.Pos_Code as Division_Code','DEPT.Pos_Code as Department_Code', 'SEC.Pos_Code as Section_Code',
            'GRP.Pos_Name as Group_Name', 'DEPT.Pos_Name as Department_Name', 'DIV.Pos_Name as Division_Name', 'SEC.Pos_Name as Section_Name',
            'MHF.Added_On', 'EJ.User_Defined_EmpId as Added_By_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By"),
            'MHF.Updated_On', 'EJ2.User_Defined_EmpId as Updated_By_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By"),
            )
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DIV','DIV.Originalpos_Id', 'MHF.Division_Id')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as SEC','SEC.Originalpos_Id', 'MHF.Section_Id')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DEPT','DEPT.Originalpos_Id', 'MHF.Department_Id')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as GRP','GRP.Originalpos_Id', 'MHF.Group_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'MHF.Added_By')
        .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'MHF.Updated_By')
        .leftJoin(ehrTables.empJob + ' as EJ2', 'EJ2.Employee_Id', 'EPI2.Employee_Id')
    }catch(err){
        console.error('Error in getHiringForeCastList function main catch block', err)
        throw err;
    }
    
}

async function getMppRecruimentRequest(organizationDbConnection, startDate, endDate){

    try {
        return organizationDbConnection( ehrTables.mppRecruitmentRequest +  ' as MRR')
        .select('MRR.Recruitment_Id', 'MRR.Original_Position_Id', 'MRR.Group_Code', 'MRR.Division_Code', 'MRR.Department_Code', 'MRR.Section_Code', 'MRR.Cost_Center', 'PL.Position_Level',
            'GRP.Pos_Name as Group_Name','DIV.Pos_Name as Division_Name','DEPT.Pos_Name as Department_Name','SEC.Pos_Name as Section_Name', 'ET.Employee_Type', 'MRR.No_Of_Position', 'MRR.Status', 'MRR.Replacement_For', 'MRR.Reason_For_Replacement',
            'MRR.Added_On', 'EJ.User_Defined_EmpId as Added_By_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By"),
            'MRR.Updated_On', 'EJ2.User_Defined_EmpId as Updated_By_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By"),
            'MRR.Approved_On', 'EJ3.User_Defined_EmpId as Approver_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as Approved_By"),
        )
        .leftJoin(ehrTables.empType + ' as ET', 'ET.EmpType_Id', 'MRR.Employee_Type')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'MRR.Added_By')
        .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'MRR.Updated_By')
        .leftJoin(ehrTables.empJob + ' as EJ2', 'EJ2.Employee_Id', 'EPI2.Employee_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI3', 'EPI3.Employee_Id', 'MRR.Approver_Id')
        .leftJoin(ehrTables.empJob + ' as EJ3', 'EJ3.Employee_Id', 'EPI3.Employee_Id')
        .leftJoin(ehrTables.positionLevel + ' as PL', 'PL.Position_Level_Id', 'MRR.Position_Level')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DIV','DIV.Pos_Code', 'MRR.Division_Code')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as SEC','SEC.Pos_Code', 'MRR.Section_Code')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DEPT','DEPT.Pos_Code', 'MRR.Department_Code')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as GRP','GRP.Pos_Code', 'MRR.Group_Code')
    }catch(err){
        console.error('Error in getMppRecruimentRequest function main catch block', err)
        throw err;
    }
}

async function getMppNewPositionRequest(organizationDbConnection, startDate, endDate){

    try {
        return organizationDbConnection( ehrTables.mppPositionRequest + ' as MPR')
        .select('MPR.Position_Request_Id', 'MPR.Original_Position_Id', 'MPR.Request_Type', 'MPR.Position_Title',
            'MPR.Group_Code', 'MPR.Division_Code', 'MPR.Department_Code', 'MPR.Section_Code', 'MPR.Reason_For_Request',
            'GRP.Pos_Name as Group_Name','DIV.Pos_Name as Division_Name','DEPT.Pos_Name as Department_Name','SEC.Pos_Name as Section_Name', 'ET.Employee_Type', 'MPR.No_Of_Position', 'MPR.Comments', 'MPR.Status', 'MPR.Cost_Center',
            'MPR.License_Certificate', 'MPR.License_Certificate_Details', 'PL.Position_Level', 'MPR.License_Certificate', 'MPR.License_Certificate_Details',
            organizationDbConnection.raw("CASE WHEN MPR.Internal_Operating_Network IS NOT NULL THEN REPLACE(REPLACE(REPLACE(MPR.Internal_Operating_Network, '[\"', ''), '\"]', ''), '\", \"', ',') ELSE '' END as Internal_Operating_Network"),
            organizationDbConnection.raw("CASE WHEN MPR.External_Operating_Network IS NOT NULL THEN REPLACE(REPLACE(REPLACE(MPR.External_Operating_Network, '[\"', ''), '\"]', ''), '\", \"', ',') ELSE '' END as External_Operating_Network"),
            'MPR.Added_On', 'EJ.User_Defined_EmpId as Added_By_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By"),
            'MPR.Updated_On', 'EJ2.User_Defined_EmpId as Updated_By_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By"),
            'MPR.Approved_On', 'EJ3.User_Defined_EmpId as Approver_Id', organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as Approved_By"))

        .leftJoin(ehrTables.empType + ' as ET', 'ET.EmpType_Id', 'MPR.Employee_Type')
        .leftJoin(ehrTables.positionLevel + ' as PL', 'PL.Position_Level_Id', 'MPR.Position_Level')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'MPR.Added_By')
        .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'MPR.Updated_By')
        .leftJoin(ehrTables.empJob + ' as EJ2', 'EJ2.Employee_Id', 'EPI2.Employee_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI3', 'EPI3.Employee_Id', 'MPR.Approver_Id')
        .leftJoin(ehrTables.empJob + ' as EJ3', 'EJ3.Employee_Id', 'EPI3.Employee_Id')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DIV','DIV.Pos_Code', 'MPR.Division_Code')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as SEC','SEC.Pos_Code', 'MPR.Section_Code')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DEPT','DEPT.Pos_Code', 'MPR.Department_Code')
        .leftJoin(ehrTables.SFWPOrganizationStructure + ' as GRP','GRP.Pos_Code', 'MPR.Group_Code')
    }catch(err){
        console.error('Error in getMppNewPositionRequest function main catch block', err)
        throw err;
    }
}

async function getMppWorkingConditions(organizationDbConnection, startDate, endDate){
    
    try {
        return organizationDbConnection( ehrTables.mppWorkingConditions)
        .select('Position_Request_Id', 'Working_Area', 'Time_Spent as Percent_Of_Time_Spent')
    }catch(err){
        console.error('Error in getMppWorkingConditions function main catch block', err)
        throw err;
    }
}


async function getMppExperienceRequirements(organizationDbConnection, startDate, endDate){
    
    try {
        return organizationDbConnection( ehrTables.mppExperienceRequirements)
        .select('Position_Request_Id', 'Type_Of_Jobs', 'Months', 'Years')
    }catch(err){
        console.error('Error in getMppExperienceRequirements function main catch block', err)
        throw err;
    }
}

async function getMppEducationRequirementsDescriptions(organizationDbConnection, startDate, endDate){
    
    try {
        return organizationDbConnection( ehrTables.mppEducationRequirementsDescriptions + ' as MERD')
        .select('MERD.Position_Request_Id', 'MER.Education_Type', 'MERD.Description')
        .innerJoin(ehrTables.mppEducationRequirements + ' as MER', 'MER.Mpp_Education_Requirements_Id', 'MERD.Mpp_Education_Requirements_Id')
    }catch(err){
        console.error('Error in getMppEducationRequirementsDescriptions function main catch block', err)
        throw err;
    }
}

async function getPositionLevel(organizationDbConnection, startDate, endDate){
    
    try {
        return organizationDbConnection( ehrTables.positionLevel)
        .select('Position_Level_Id', 'Position_Level')
    }catch(err){
        console.error('Error in getPositionLevel function main catch block', err)
        throw err;
    }
}

async function getMppDutiesResponsibilities(organizationDbConnection, startDate, endDate){
    
    try {
        return organizationDbConnection( ehrTables.mppDutiesResponsibilities)
        .select('Position_Request_Id', 'Regular_Duties', 'No_Of_Hours_Period as No_Of_Hours_Performed_per_Period', 
            'Period', 'Competencies_Required', 'Competency', 'Rating_Of_Competency')
    }catch(err){
        console.error('Error in getMppDutiesResponsibilities function main catch block', err)
        throw err;
    }
}

module.exports.getPositionMaster = getPositionMaster;
module.exports.getHiringForeCastList = getHiringForeCastList;
module.exports.getMppRecruimentRequest = getMppRecruimentRequest;
module.exports.getMppNewPositionRequest = getMppNewPositionRequest;
module.exports.getMppWorkingConditions = getMppWorkingConditions;
module.exports.getMppExperienceRequirements = getMppExperienceRequirements;
module.exports.getMppEducationRequirementsDescriptions = getMppEducationRequirementsDescriptions;
module.exports.getPositionLevel = getPositionLevel;
module.exports.getMppDutiesResponsibilities = getMppDutiesResponsibilities;