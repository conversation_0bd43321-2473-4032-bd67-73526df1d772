'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// Imports the Google Cloud client library
const { Storage } = require('@google-cloud/storage');
// Import xlsx library
const XLSX = require('xlsx');
const fs = require('fs');
// require moment-timezone
const moment = require('moment-timezone');
// require file to access constant values
const { s3FileUpload } = require('../common/appConstants');
// require common function
const commonFunction = require('./commonFunctions');
// require table names
const tables = require('../common/tableAlias');
let ehrTables = tables.ehrTables;
// AWS S3 client to download the attendance file from S3
const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3');
// streamPipeline is a utility function to pipeline streams
const { pipeline } = require("stream");
// promisify is a utility function to convert callbacks to promises
const { promisify } = require("util");
const streamPipeline = promisify(pipeline);

// variable declarations
let appManagerDbConnection = '';
let orgDbConnection = '';
let orgCode = '';
let errorData = [];

/* function to download the attendance file from the cloud storage
  * and import the attendance records to the attendance import table */
module.exports.attendanceDataIntegration = async (event, context) => {
  // variable declarations
  let frequency = '';
  let logId = '';
  let jobId = '';
  let triggerType = 1;
  try {
    // get input params
    if (event && event.input) {
      frequency = (event.input.frequency).toLowerCase();
      orgCode = event.input.orgCode;
      logId = event.input.logId;
      jobId = event.input.jobId;
      triggerType = event.input.triggerType;
    }

    let startDate;
    let s3FileName;

    if (frequency && orgCode && logId && jobId && triggerType) {
      let getAppDbConnection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName, process.env.region, '', 1);
      appManagerDbConnection = knex(getAppDbConnection.AppManagerDb);
      let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection, orgCode);
      if (orgRegionDetails && Object.keys(orgRegionDetails).length > 0) {
        let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
        //Get database connection
        let getDbConnection = await commonLib.stepFunctions.getConnection(process.env.stageName, process.env.dbPrefix, process.env.dbSecretName, process.env.region, orgCode, 0, additionalHeaders);

        if (Object.keys(getDbConnection).length) {
          orgDbConnection = knex(getDbConnection.OrganizationDb);
          let updateParams = {
            Status: 'Inprogress',
            Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
          }
          // Update the job status as inprogress in data integration log table
          let updateStatus = await commonFunction.updateDataIntegrationLog(orgDbConnection, updateParams, logId);
          // get the data integration schedule details based on jobId
          let scheduleDetails = await commonFunction.getIntegrationDetails(appManagerDbConnection, jobId);
          // check whether data exist or not
          if (Object.keys(scheduleDetails).length > 0) {
            let reportType = scheduleDetails.Report_Type;
            let fileFormat = scheduleDetails.File_Format;
            let storageType = scheduleDetails.Storage_Platform;

            // function to get the log details based on logId
            let getLogData = await commonFunction.getLogDetails(orgDbConnection, logId);
            // check log data exist or not
            if (Object.keys(getLogData).length > 0) {
              startDate = getLogData.Start_Date;
              // form s3 filename. Example infotech_HRAR_daily.xlsx
              s3FileName = orgCode + '_' + reportType + '_' + frequency + '.' + fileFormat;
              let downloadFilePath = '/tmp/' + s3FileName;

              try {
                // get the secret details
                let secretData = await commonFunction.getSecretDetails(process.env.region, orgCode, appManagerDbConnection);
                // check whether secret data exist or not
                if (secretData && Object.keys(secretData).length > 0) {
                  let secretKeys = (secretData[orgCode]) ? JSON.parse(secretData[orgCode]) : {};
                  if (Object.keys(secretKeys).length > 0) {
                    // if storage type is gcp
                    if (storageType.toLowerCase() === 'gcp') {
                      let projectId = (secretKeys.project_id).trim();
                      let clientEmail = (secretKeys.client_email).trim();
                      let privateKey = (secretKeys.private_key).trim();
                      let bucketName = (secretKeys.bucket_name).trim();

                      if (projectId && clientEmail && privateKey && bucketName) {
                        // Creates a cloud storage client
                        const storage = new Storage({
                          projectId: projectId,
                          credentials: {
                            'client_email': clientEmail,
                            'private_key': privateKey.replace(/\\n/g, '\n')
                          }
                        });

                        const options = {
                          destination: downloadFilePath,
                        };
                        // get the global resourceId from the org details table
                        return (
                          orgDbConnection(ehrTables.orgDetails)
                            .select('Use_Global_Resource_Id')
                            .then(async (orgDetails) => {
                              if (orgDetails.length) {
                                let globalResourceId = orgDetails[0].Use_Global_Resource_Id;
                                // function to download the file from cloud storage
                                await fileDownload(options, bucketName, startDate, s3FileName, storage)
                                /** Call function to read the downloaded file and insert the attendance data in import table */
                                let processAttendanceData = await importAttendanceData(orgDbConnection, appManagerDbConnection, downloadFilePath, logId, jobId, frequency, globalResourceId);
                                if (processAttendanceData === 'success') {

                                  await orgDbConnection(ehrTables.empAttendanceImport)
                                    .select(orgDbConnection.raw('COUNT(Attendance_Id) as newcount'))
                                    .then(async (countAfterInsert) => {
                                      // update success status in log table
                                      // console.log("count after insert..", countAfterInsert)
                                    }).catch(catchError => {
                                      console.log('Error in countAfterInsert .catch block.', catchError);
                                    })
                                  // form folder path
                                  let s3FilePath = process.env.domainName + '/' + orgCode + '/' + startDate + '/' + s3FileName;

                                  // upload file in s3
                                  let pushFile = await commonFunction.uploadFileInS3(downloadFilePath, process.env.dataIntegrationBucket, s3FilePath);
                                  if (pushFile === 'success') {
                                    // update success status in log table
                                    await updateSuccessStatus(orgDbConnection, appManagerDbConnection, downloadFilePath, s3FileName, logId, jobId, "");

                                    //create error log and upload it into the user bucket and our s3 bucket
                                    if (errorData.length > 0) {
                                      try {
                                        let errorFileName = orgCode + '_' + reportType + '_' + frequency + '_Erorr_Report.csv';
                                        let errorFilePath = '/tmp/' + errorFileName;
                                        let workSheet = XLSX.utils.json_to_sheet(errorData, {
                                          skipHeader: 0,
                                        });

                                        let workBook = XLSX.utils.book_new();
                                        XLSX.utils.book_append_sheet(workBook, workSheet, `response`);
                                        XLSX.writeFile(workBook, errorFilePath);

                                        // form folder path
                                        let s3ErrorFilePath = process.env.domainName + '/' + orgCode + '/' + startDate + '/' + errorFileName;
                                        // upload file in s3
                                        await commonFunction.uploadFileInS3(errorFilePath, process.env.dataIntegrationBucket, s3ErrorFilePath);

                                        /** Upload file in google cloud storage */
                                        await storage.bucket(bucketName).upload(errorFilePath, {
                                          destination: startDate + '/' + errorFileName,
                                        }).then(async function () {
                                          // console.log('file uploaded in gcp successfully', startDate+'/'+errorFileName);
                                        });

                                        await deleteTempFolderFiles(errorFilePath);

                                      }
                                      catch (error) {
                                        console.log('Error in creating and uploading the error log file', error);
                                      }
                                    }

                                    let response =
                                    {
                                      nextStep: 'Step1',
                                      input: { 'frequency': frequency, 'triggerType': triggerType },
                                      message: 'Execution completed so process the next record.'
                                    };
                                    return response;
                                  } else {
                                    console.log('Error in uploading file in s3 bucket');
                                    await updateSuccessStatus(orgDbConnection, appManagerDbConnection, downloadFilePath, "", logId, jobId, "");
                                    let response =
                                    {
                                      nextStep: 'Step1',
                                      input: { 'frequency': frequency, 'triggerType': triggerType },
                                      message: 'Error while processing the data.So process for the next instance.'
                                    };
                                    return response;
                                  }
                                }
                                else {
                                  await updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, processAttendanceData);
                                  await deleteTempFolderFiles(downloadFilePath);
                                  appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                                  orgDbConnection ? orgDbConnection.destroy() : null;
                                  let response =
                                  {
                                    nextStep: 'Step1',
                                    input: { 'frequency': frequency, 'triggerType': triggerType },
                                    message: 'Error while processing the data.So process for the next instance.'
                                  };
                                  return response;
                                }
                              }
                              else {
                                console.log('Organization details does not exists');
                                throw 'Organization details does not exists';
                              }
                            })
                            .catch(async (catchError) => {
                              console.log('Error while getting global resourceId function .catch block.', catchError);
                              let errorMessages = ['Error while downloading the file from Cloud Storage.', 'Organization details does not exists'];
                              let failureReason = errorMessages.includes(catchError) ? catchError : 'Error uploading file in s3';
                              await updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, failureReason);
                              await deleteTempFolderFiles(downloadFilePath);
                              appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                              orgDbConnection ? orgDbConnection.destroy() : null;
                              let response =
                              {
                                nextStep: 'Step1',
                                input: { 'frequency': frequency, 'triggerType': triggerType },
                                message: 'Error while processing the data.So process for the next instance.'
                              };
                              return response;
                            })
                        )
                      } else {
                        throw 'Secret key is missing.';
                      }
                    }
                    else if (storageType.toLowerCase() === 'aws') {
                      if (reportType.toUpperCase() === 'POSMASTER') {
                        const downloadFilePath = '/tmp/API_Data_Export.xlsx';
                        let response = await downloadFileFromS3(downloadFilePath, orgCode, secretKeys);
                        if (response && !response.success) {
                          if (response.failedData && response.failedData.length) {
                            await orgDbConnection(ehrTables.dataIntegrationFailureLogs)
                              .insert(response.failedData)
                          } else {
                            let failedData = {
                              Integration_Type: 'Entomo - HRAPP',
                              Entity_Type: 'Position Master',
                              Primary_Id: null,
                              Failure_Data: null,
                              Failure_Reason: response.message ? response.message : 'Error while processing the data.',
                              Integration_Date: moment().utc().format('YYYY-MM-DD'),
                            }
                            await orgDbConnection(ehrTables.dataIntegrationFailureLogs)
                              .insert(failedData);
                          }
                        }
                        //Form Email notification
                        if (scheduleDetails && (scheduleDetails.Email_TO || scheduleDetails.Email_CC || scheduleDetails.Email_BCC)) {
                          let failedData = response.failedData ? response.failedData : [];
                          let subject = 'Entomo RMS data sync status'
                          let emailParams = {
                            mailContent: response.message
                          }
                          await sendFailedLogs(failedData, scheduleDetails, emailParams, subject);
                        }
                        if (response && !response.success) {
                          let errMessage = response.plainMessage ? response.plainMessage : response.message;
                          await updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, errMessage);
                        } else {
                          await updateSuccessStatus(orgDbConnection, appManagerDbConnection, downloadFilePath, 'API_Data_Export.xlsx', logId, jobId);
                        }
                        let nextResponse =
                        {
                          nextStep: 'Step1',
                          input: { 'frequency': frequency, 'triggerType': triggerType },
                          message: 'Execution completed so process the next record.'
                        };
                        return nextResponse;
                      }
                    }
                    else {
                      // need to implement for other types. Currently we implement for gcp so this block is handled as error
                      throw 'Invalid storage type';
                    }
                  }
                  else {
                    throw 'Secret not exist for this instance';
                  }
                }
                else {
                  throw 'Error in getting secret details from secret manager.';
                }
              }
              catch (error) {
                console.log('Error after calling get secret details function', error);
                let errorMessages = ['Secret not exist for this instance',
                  'Invalid storage type', 'Error in getting secret details from secret manager.', 'Secret key is missing.',
                  'Error while downloading the file from Cloud Storage.'];
                let failureReason = errorMessages.includes(error) ? error : 'Error uploading file in s3';
                await updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, failureReason);
                await deleteTempFolderFiles(downloadFilePath);
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                orgDbConnection ? orgDbConnection.destroy() : null;
                let response =
                {
                  nextStep: 'Step1',
                  input: { 'frequency': frequency, 'triggerType': triggerType },
                  message: 'Error while processing the data.So process for the next instance.'
                };
                return response;
              }
            }
          } else {
            console.log('Error in getting the schedule details for ' + orgCode + ' instance');
            await updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, 'Error while getting the schedule details.');
            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            orgDbConnection ? orgDbConnection.destroy() : null;
            let response =
            {
              nextStep: 'Step1',
              input: { 'frequency': frequency, 'triggerType': triggerType },
              message: 'Error while getting schedule details.'
            };
            return response;
          }
        } else {
          console.log('Error while making organization database connection.');
          await updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, 'Error while making database connection.');
          appManagerDbConnection ? appManagerDbConnection.destroy() : null;
          orgDbConnection ? orgDbConnection.destroy() : null;
          let response =
          {
            nextStep: 'Step1',
            input: { 'frequency': frequency, 'triggerType': triggerType },
            message: 'Error in making database connection.Process the next record'
          };
          return response;
        }
      } else {
        console.log('Error while getting the organization data region.');
        await updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, 'Error while getting the data region.');
        appManagerDbConnection ? appManagerDbConnection.destroy() : null;
        orgDbConnection ? orgDbConnection.destroy() : null;
        let response =
        {
          nextStep: 'Step1',
          input: { 'frequency': frequency, 'triggerType': triggerType },
          message: 'Error in getting the data region. Process the next record'
        };
        return response;
      }
    } else {
      console.log('Input value is empty. So process the next record');
      appManagerDbConnection ? appManagerDbConnection.destroy() : null;
      orgDbConnection ? orgDbConnection.destroy() : null;
      let response =
      {
        nextStep: 'Step1',
        input: { 'frequency': frequency, 'triggerType': triggerType },
        message: 'Invalid input. Process the next record.'
      };
      return response;
    }
  } catch (error) {
    console.log('Error in attendanceDataIntegration function main catch block.', error);
    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
    orgDbConnection ? orgDbConnection.destroy() : null;
    let response =
    {
      nextStep: 'Step1',
      input: { 'frequency': frequency, 'triggerType': triggerType },
      message: 'Error in step3 main catch block.'
    };
    return response;
  }
};

/** Function to read the download file from the Cloud storage and
  * and import the attendance records to the attendance import table */
async function importAttendanceData(orgDbConnection, appManagerDbConnection, downloadFilePath, logId, jobId, frequency, useGlobalResourceId) {
  try {
    let attendanceImportDetails = [];
    let employeeId;
    /** Read downloaded file */
    let workbook = XLSX.readFile(downloadFilePath, { cellDates: true });
    let sheet_name_list = workbook.SheetNames;
    let xlData = XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]]);
    /** get the value of Use_Global_Resource_Id */
    if (useGlobalResourceId) {
      /** Check whether data exists in the file or not */
      if (xlData.length > 0) {
        for (let attendanceRecord of xlData) {
          let attendanceDate = attendanceRecord["Added On Date/Time"];
          /** When we read the date value from the excel file we will get the previous day value,
           * So we should add 1 day */
          // attendanceDate = moment(attendanceDate).add(1, 'days').format('YYYY-MM-DD 00:00:00');
          attendanceDate = moment(attendanceDate).format('YYYY-MM-DD 00:00:00');
          /** If the Use_Global_Resource_Id is 1 then get the External_EmpId associated to the Global_Resource_Id */
          if (useGlobalResourceId.toLowerCase() === 'yes') {
            employeeId = await getEmployeeId(orgDbConnection, attendanceRecord["Employee Id"], attendanceDate);
          } else {
            /**While processing the attendance we will check whether this is External or Self employee id */
            employeeId = attendanceRecord["Employee Id"];
          }

          if (employeeId) {
            /** Form the file we will get only the attendance date, so we should get workschedule / shift details of the employee */
            let getGraceTimeDetails = await commonLib.employees.getGraceTimeDetails(employeeId, null, attendanceDate, orgDbConnection);

            if (getGraceTimeDetails && getGraceTimeDetails['regularFrom'] && getGraceTimeDetails['regularTo']) {
              /** We should consider the regularForm getGraceTimeDetailstime as checkin and regularTo as checkout  */
              let attendanceCheckinRecord = {
                'Employee_Id': employeeId,
                'Employee_Name': '',
                'Schema_Id': 1,
                'Added_On': getGraceTimeDetails['regularFrom'].toJSON(),
                'Attendance_Status': 'C/In',
                'Work_Place': attendanceRecord['Work Place'],
                'Rollup_Flag': 0
              };

              let attendanceCheckoutRecord = {
                'Employee_Id': employeeId,
                'Employee_Name': '',
                'Schema_Id': 1,
                'Added_On': getGraceTimeDetails['regularTo'].toJSON(),
                'Attendance_Status': 'C/Out',
                'Work_Place': attendanceRecord['Work Place'],
                'Rollup_Flag': 0
              };

              attendanceImportDetails.push(attendanceCheckinRecord);
              attendanceImportDetails.push(attendanceCheckoutRecord);
            } else {
              /** if the shift is not scheduled for the employee then add only checkin record with time 00:00:00 
               * While processing this record, it will be considered as invalid
              */
              let attendanceCheckinRecord = {
                'Employee_Id': employeeId,
                'Employee_Name': '',
                'Schema_Id': 1,
                'Added_On': moment(attendanceDate).format("YYYY-MM-DD"),
                'Attendance_Status': 'C/In',
                'Work_Place': attendanceRecord['Work Place'],
                'Rollup_Flag': 0
              };
              attendanceImportDetails.push(attendanceCheckinRecord);
              errorData.push({ "Employee Id": attendanceRecord["Employee Id"], "Attendance Date": attendanceRecord["Added On Date/Time"], "Reason": "Shift not scheduled" });
            }
          } else {
            /** If the employee id is not valid, then process the next record */
            console.log('Error while getting the employee id', employeeId);
            errorData.push({ "Employee Id": attendanceRecord["Employee Id"], "Attendance Date": attendanceRecord["Added On Date/Time"], "Reason": "Biometric Integration id does not found" });
          }
        }

        await orgDbConnection(ehrTables.empAttendanceImport)
          .select(orgDbConnection.raw('COUNT(Employee_Id) as oldcount'))
          .then(async (countBeforeInsert) => {
            // update success status in log table
            // console.log("count before insert", countBeforeInsert)
          }).catch(catchError => {
            console.log('Error in countBeforeInsert .catch block.', catchError);
          })

        /** Insert the attendance records in the emp_attendance_import table */
        if (attendanceImportDetails.length > 0) {
          return (
            orgDbConnection
              .transaction(function (trx) {
                return (
                  orgDbConnection(ehrTables.empAttendanceImport)
                    .insert(attendanceImportDetails)
                    .transacting(trx)
                    .then(async (attendanceImportDetails) => {
                      // update success status in log table
                      return 'success';
                    }).catch(catchError => {
                      console.log('Error in inserting attendance import data .catch block.', catchError);
                      throw 'Error while inserting the attendance data.';
                    })
                )
              })
          )
        } else {
          console.log('No attendance record exists..');
          await updateSuccessStatus(orgDbConnection, appManagerDbConnection, downloadFilePath, '', logId, jobId, "Attendance data does not exist.");
          return 'success';
        }
      } else {
        console.log('No data exists in the file');
        await updateSuccessStatus(orgDbConnection, appManagerDbConnection, downloadFilePath, '', logId, jobId, "Attendance data integration file is empty.");
        return 'success';
      }
    } else {
      throw 'Error while getting the Use_Global_Resource_Id value';
    }
  } catch (importAttendanceDataError) {
    console.log('Error in importAttendanceData() function catch block.', importAttendanceDataError);
    let errorMessages = ['Error while getting the external employee id.', 'Error while inserting the attendance data.',
      'Organization details does not exists.'];
    let failureReason = errorMessages.includes(importAttendanceDataError) ? importAttendanceDataError : 'Error while processing the attendance data.';
    return failureReason;
  }
}

/** function to get the external employee id if exist */
async function getEmployeeId(orgDbConnection, globalResourceId, attDate) {
  try {
    let attendanceDate = new Date(attDate);
    /**If the Use_Global_Resource_Id is 1 then get the employee id associated to the Global_Resource_Id,
     * If the same employee is cloned multiple times then we may have multiple records
     * So, get the employee id based on the attendance date
     * First check the active record of the employee, that is, Date_Of_Join <= attendanceDate and Emp_InActive_Date is NULL
     * if active record does not exist then get the inactive employee record which attendanceDate is between Date_Of_Join and Emp_InActive_Date  
    */
    //  console.log('inside getEmployeeId function', globalResourceId, attendanceDate);
    //  console.log('select External_EmpId from emp_job where Date_Of_Join <='+'"'+attendanceDate+'" and Emp_InActive_Date is NULL and Global_Resource_Id = '+globalResourceId);
    return (
      orgDbConnection(ehrTables.empJob)
        .select('External_EmpId')
        .where('Date_Of_Join', '<=', attendanceDate)
        .where('Emp_InActive_Date', null)
        .where('Global_Resource_Id', globalResourceId)
        .then(employeeIdResult => {
          // console.log('active employee id', employeeIdResult);
          if (employeeIdResult.length)
            return employeeIdResult[0].External_EmpId;
          else {
            // console.log('select External_EmpId from emp_job where Date_Of_Join <='+'"'+attendanceDate+'" and Emp_InActive_Date >='+'"'+attendanceDate+'" and Global_Resource_Id = '+globalResourceId);
            return (
              orgDbConnection(ehrTables.empJob)
                .select('External_EmpId')
                .where('Date_Of_Join', '<=', attendanceDate)
                .where('Emp_InActive_Date', '>=', attendanceDate)
                .where('Global_Resource_Id', globalResourceId)
                .then(employeeIdResult => {
                  if (employeeIdResult.length)
                    return employeeIdResult[0].External_EmpId;
                  else {
                    errorData.push({ "Employee Id": globalResourceId, "Attendance Date": attDate, "Reason": "Employee id not found" });
                    return 0;
                  }
                })
            )
          }
        })
    )
  } catch (getEmployeeIdError) {
    console.log('Error in getEmployeeId() function catch block.', getEmployeeIdError);
    return 0;
  }
}

async function updateFailureStatus(orgDbConnection, appManagerDbConnection, logId, jobId, reason) {
  try {
    let updateFailureParams = {
      Status: 'Failure',
      Reason: reason,
      S3_File_Name: null,
      Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
    }
    await commonFunction.updateDataIntegrationLog(orgDbConnection, updateFailureParams, logId);

    let updateFailureStatus = {
      Status: 'Failure',
      Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
    }
    await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection, updateFailureStatus, jobId);
    return 'success';
  }
  catch (error) {
    console.log('Error in updateFailureStatus function catch block.', error);
    return 'error';
  }
}

async function updateSuccessStatus(orgDbConnection, appManagerDbConnection, downloadFilePath, s3FileName, logId, jobId, reason) {
  try {
    let updateParams = {
      Status: 'Success',
      Reason: reason,
      S3_File_Name: s3FileName,
      Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
    }
    // Update the job status as success in data integration log table
    await commonFunction.updateDataIntegrationLog(orgDbConnection, updateParams, logId);
    let updateStatusParams = {
      Status: 'Success',
      Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
    }
    // Update the job status as success in data schedule table
    await commonFunction.updateIntegrationScheduleStatus(appManagerDbConnection, updateStatusParams, jobId);
    await deleteTempFolderFiles(downloadFilePath);
    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
    orgDbConnection ? orgDbConnection.destroy() : null;

    return 'success';
  }
  catch (error) {
    console.log('Error in updateSuccessStatus function catch block.', error);
    await deleteTempFolderFiles(downloadFilePath);
    appManagerDbConnection ? appManagerDbConnection.destroy() : null;
    orgDbConnection ? orgDbConnection.destroy() : null;
    return 'error';
  }
}

// function to delete the file created in temp folder
async function deleteTempFolderFiles(fileName) {
  try {
    fs.unlink(fileName, (error) => {
      if (error) {
        console.log('Error in deleting file in tmp folder', error);
      }
      else {
        // console.log('File deleted successfully');
      }
    });
    return 'success'; // In case of error also we return as success
  }
  catch (error) {
    console.log('Error in deleteTempFolderFiles function catch block.', error);
    return 'error';
  }
};

// Download the file from cloud storage
async function fileDownload(options, bucketName, startDate, s3FileName, storage) {
  try {
    await storage.bucket(bucketName).file(startDate + '/' + s3FileName).download(options)
      .then(async function () {
        return 'success';
      }).catch(function (downloadError) {
        console.log('Error while downloading the file from Cloud Storage.', downloadError);
        throw 'Error while downloading the file from Cloud Storage.';
      })
  }
  catch (error) {
    console.log('Error in fileDownload function main catch block.', error);
    throw 'Error while downloading the file from Cloud Storage.';
  }
};

/**
 * Downloads a file from S3 given a download path and secret keys
 * @param {string} downloadPath - The path to download the file to
 * @param {string} orgCode - The organization code
 * @param {object} secretKeys - The secret keys for s3
 * @param {string} secretKeys.access_key - The access key id
 * @param {string} secretKeys.secret_key - The secret access key
 * @param {string} secretKeys.bucket_name - The name of the bucket
 * @param {string} secretKeys.region - The region of the bucket
 * @returns {Promise<object>} - The result of the download. If the file is not found,
 *                              returns an object with success set to false and a message
 *                              to be sent as an email
 */
async function downloadFileFromS3(downloadPath, orgCode, secretKeys,) {
  try {
    console.log('Inside downloadFileFromS3');
    let accessKeyId = secretKeys.access_key ? (secretKeys.access_key).trim() : null;
    let secretAccessKey = secretKeys.secret_key ? (secretKeys.secret_key).trim() : null;
    let bucketName = secretKeys.bucket_name ? (secretKeys.bucket_name).trim() : null;
    let region = secretKeys.region ? (secretKeys.region).trim() : null;
    if (accessKeyId && secretAccessKey && bucketName) {
      const s3Client = new S3Client({
        region: region,
        credentials: {
          accessKeyId: accessKeyId,
          secretAccessKey: secretAccessKey,
        },
      });

      const objectKey = orgCode + '/employeedata/' + moment().format('YYYYMMDD') + '/API_Data_Export.xlsx';

      const path = require("path");
      const dirPath = path.dirname(downloadPath);

      // Ensure the directory exists, create it if it doesn't
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      // Construct the command to download the file from S3
      const command = new GetObjectCommand({ Bucket: bucketName, Key: objectKey });

      // Download the file from S3
      const response = await s3Client.send(command);

      if (!response.Body) {
        console.log('Empty body response')
        return {
          success: false,
          message: 'The expected file (API_Data_Export.xlsx) for data synchronization could not be found in the storage.'
        }
      }

      // Stream the file from S3 to the local file system
      await streamPipeline(response.Body, fs.createWriteStream(downloadPath));

      let result = await insertPositionMasterData();

      return result;
    }
    else {
      console.log('Either client accessKeyId or secretAccessKey or bucketName details is missing');
      throw 'Secret key is missing.';
    }
  } catch (error) {
    console.log('Error in downloadFileFromS3:', error);
    if (error.Code === 'NoSuchKey') {
      return {
        success: false,
        message: 'The expected file (API_Data_Export.xlsx) for data synchronization could not be found in the storage.'
      }
    } else {
      throw error;
    }
  }
}

/**
 * Fetches active employees from the organization database.
 * Retrieves employee IDs along with their designation and department codes
 * for employees who have active status and have completed their form.
 * 
 * @returns {Promise<object>} An object containing two Sets:
 *                            - activeDesignationMap: Set of unique designation codes
 *                            - activeDepartmentMap: Set of unique department codes
 * @throws Will throw an error if there is an issue with the database query
 */
async function fetchActiveEmployees() {
  try {
    // Query the database for active employees along with their designation and department codes
    const employees = await orgDbConnection(ehrTables.empJob + " as EJ")
      .select("EJ.Employee_Id", "D.Designation_Code", "DP.Department_Code")
      .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EJ.Employee_Id", "EPI.Employee_Id")
      .leftJoin(ehrTables.designation + " as D", "D.Designation_Id", "EJ.Designation_Id")
      .leftJoin(ehrTables.department + " as DP", "DP.Department_Id", "EJ.Department_Id")
      .whereNotNull("D.Designation_Code")
      .whereNotNull("DP.Department_Code")
      .where('EPI.Form_Status', 1)
      .where("EJ.Emp_Status", "Active");

    // Store designations and departments with active employees in Sets for fast lookup
    const activeDesignationMap = new Set();
    const activeDepartmentMap = new Set();

    // Iterate over each employee and add their designation and department codes to the Sets
    employees.forEach(emp => {
      if (emp.Designation_Code) activeDesignationMap.add(emp.Designation_Code);
      if (emp.Department_Code) activeDepartmentMap.add(emp.Department_Code);
    });

    // Return the maps of active designations and departments
    return { activeDesignationMap, activeDepartmentMap };
  } catch (error) {
    // Log and rethrow any errors encountered during the process
    console.log('Error in fetchActiveEmployees function catch block.', error);
    throw error;
  }
};

async function insertPositionMasterData() {
  try {
    console.log('Inside insertPositionMasterData');

    // Read the Excel file
    const workbook = XLSX.readFile('/tmp/API_Data_Export.xlsx');

    let failureLogs = [];

    // Process ORG_DATA Sheet
    const orgSheetName = 'ORG_DATA';
    let organizationData = [], designationData = [], departmentData = [];

    if (!workbook.Sheets[orgSheetName]) {
      console.log(`Sheet "${orgSheetName}" not found in the Excel file.`);
      failureLogs.push({
        Integration_Type: 'Entomo - HRAPP',
        Entity_Type: 'Position Master',
        Primary_Id: null,
        Failure_Data: null,
        Failure_Reason: 'Sheet ORG_DATA not found',
        Integration_Date: moment().utc().format('YYYY-MM-DD'),
      });
    } else {
      const worksheet = workbook.Sheets[orgSheetName];
      // Convert the sheet data to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData && jsonData.length > 0) {
        // Define the mapping between Excel sheet fields and database columns
        const fieldMapping = {
          JOB_STATUS: 'Job_Status',
          COST_CODE: 'Cost_Code',
          COMPANY_ID: 'Company_Id',
          ORG_LEVEL: 'Org_Level',
          POS_CODE: 'Pos_Code',
          PARENT_ID: 'Parent_Id',
          HEADDIV_CODE: 'Head_Division_Code',
          LSTGRADECODE: 'Lst_Grade_Code',
          JOBTITLE_CODE: 'Job_Title_Code',
          DEPT_CODE: 'Department_Code',
          STATUS: 'Status',
          ISUPDATE: 'Is_Update',
          TYPE: 'Type',
          POS_NAME: 'Pos_Name',
          LSTWORKLOCATION: 'Lst_Work_Location',
          PARENT_PATH: 'Parent_Path',
          ORIGINALPOS_ID: 'Originalpos_Id',
        };

        jsonData.forEach((row) => {
          const type = row.TYPE ? row.TYPE.toLowerCase() : '';

          // Prepare data for SFWP_Organization_Structure (mappedData equivalent)
          const organizationRow = {};
          Object.entries(fieldMapping).forEach(([excelField, dbColumn]) => {
            let value = row[excelField] || null;

            // Special transformations
            if (dbColumn === 'Pos_Name' && typeof value === 'string') {
              value = value.replace(/>/g, ''); // Replace ">" with ""
            }

            if (dbColumn?.toLowerCase() === 'status' && value) {
              value = value === 'Y' ? 'Active' : 'InActive';
            }

            organizationRow[dbColumn] = value;
          });

          organizationData.push(organizationRow);

          if (type === 'jobpos') {
            designationData.push({
              Designation_Code: organizationRow.Pos_Code,
              Designation_Name: organizationRow.Pos_Name,
              [organizationRow.Is_Update === 'Y' ? 'Updated_On' : 'Added_On']: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
              [organizationRow.Is_Update === 'Y' ? 'Updated_By' : 'Added_By']: 1
            });
          } else if (type === 'orgunit') {
            departmentData.push({
              Department_Code: organizationRow.Pos_Code,
              Department_Name: organizationRow.Pos_Name,
              Organization_Type_Id: 1,
              BonusType_Id: null,
              [organizationRow.Is_Update === 'Y' ? 'Updated_On' : 'Added_On']: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
              [organizationRow.Is_Update === 'Y' ? 'Updated_By' : 'Added_By']: 1
            });
          }
        });

        // **Step 1: Insert/Update Designations & Departments (WITHOUT status validation)**
        await Promise.all([
          // Insert/Update Organization Structure
          organizationData.length &&
          orgDbConnection(ehrTables.SFWPOrganizationStructure)
            .insert(organizationData)
            .onConflict('Pos_Code')
            .merge(),

          // Insert/Update Designations
          designationData.length &&
          orgDbConnection(ehrTables.designation)
            .insert(designationData)
            .onConflict('Designation_Code')
            .merge(),

          // Insert/Update Departments
          departmentData.length &&
          orgDbConnection(ehrTables.department)
            .insert(departmentData)
            .onConflict('Department_Code')
            .merge()
        ]);

      } else {
        failureLogs.push({
          Integration_Type: 'Entomo - HRAPP',
          Entity_Type: 'Position Master',
          Primary_Id: null,
          Failure_Data: null,
          Failure_Reason: 'Sheet ORG_DATA is empty',
          Integration_Date: moment().utc().format('YYYY-MM-DD'),
        })
      }
    }

    // **Step 2: Process Employee_Details Sheet and Update Employee Records**
    const employeeSheetName = 'EMPLOYEE_DETAILS';

    if (!workbook.Sheets[employeeSheetName]) {
      console.log(`Sheet "${employeeSheetName}" not found in the Excel file.`);
      failureLogs.push({
        Integration_Type: 'Entomo - HRAPP',
        Entity_Type: 'Employee',
        Primary_Id: null,
        Failure_Data: null,
        Failure_Reason: 'Sheet Employee_Details not found',
        Integration_Date: moment().utc().format('YYYY-MM-DD'),
      });
    } else {
      const employeeWorksheet = workbook.Sheets[employeeSheetName];
      // Convert the sheet data to JSON
      const jsonDataEmployee = XLSX.utils.sheet_to_json(employeeWorksheet);
      if (jsonDataEmployee?.length > 0) {
        failureLogs = await syncEmployeeData(jsonDataEmployee);
      } else {
        failureLogs.push({
          Integration_Type: 'Entomo - HRAPP',
          Entity_Type: 'Employee',
          Primary_Id: null,
          Failure_Data: null,
          Failure_Reason: 'Sheet Employee_Details is empty',
          Integration_Date: moment().utc().format('YYYY-MM-DD'),
        })
      }
    }

    // **Step 3: Fetch Active Employees for Validation**
    const { activeDesignationMap, activeDepartmentMap } = await fetchActiveEmployees();

    let designationStatusData = [], departmentStatusData = [];

    designationData.forEach(designation => {
      if (activeDesignationMap.has(designation.Designation_Code) && designation.Status?.toLowerCase() === 'n') {
        failureLogs.push({
          Integration_Type: 'Entomo - HRAPP',
          Entity_Type: 'Designation',
          Primary_Id: null,
          Failure_Data: JSON.stringify(designation),
          Failure_Reason: 'Active employee assigned to inactive designation',
          Integration_Date: moment().utc().format('YYYY-MM-DD'),
        });
      } else {
        designationStatusData.push({
          Designation_Code: designation.Designation_Code,
          Designation_Status: designation.Status?.toLowerCase() === 'n' ? 'InActive' : 'Active',
        });
      }
    });

    departmentData.forEach(department => {
      if (activeDepartmentMap.has(department.Department_Code) && department.Status?.toLowerCase() === 'n') {
        failureLogs.push({
          Integration_Type: 'Entomo - HRAPP',
          Entity_Type: 'Department',
          Primary_Id: null,
          Failure_Data: JSON.stringify(department),
          Failure_Reason: 'Active employee assigned to inactive department',
          Integration_Date: moment().utc().format('YYYY-MM-DD'),
        });
      } else {
        departmentStatusData.push({
          Department_Code: department.Department_Code,
          Department_Status: department.Status?.toLowerCase() === 'n' ? 'InActive' : 'Active',
        });
      }
    });

    // **Step 4: Update Designation and Department Status**
    await Promise.all([
      designationStatusData.length &&
      orgDbConnection(ehrTables.designation)
        .insert(designationStatusData)
        .onConflict('Designation_Code')
        .merge(),

      departmentStatusData.length &&
      orgDbConnection(ehrTables.department)
        .insert(departmentStatusData)
        .onConflict('Department_Code')
        .merge()
    ]);

    // **Step 5: Process WL_DATA Sheet and Update Work Location Records**
    console.log('Inside insertPositionMasterData - WL_DATA');
    const workLocationSheetName = 'WL_DATA';

    if (!workbook.Sheets[workLocationSheetName]) {
      console.log(`Sheet "${workLocationSheetName}" not found in the Excel file.`);
      failureLogs.push({
        Integration_Type: 'Entomo - HRAPP',
        Entity_Type: 'Location',
        Primary_Id: null,
        Failure_Data: null,
        Failure_Reason: 'Sheet WL_DATA not found',
        Integration_Date: moment().utc().format('YYYY-MM-DD'),
      });
    } else {
      const locationWorksheet = workbook.Sheets[workLocationSheetName];
      // Convert the sheet data to JSON
      const jsonDataLocation = XLSX.utils.sheet_to_json(locationWorksheet);
      if (jsonDataLocation?.length > 0) {
        const locationData = [];
        jsonDataLocation.forEach(row => {
          const locationRow = {
            Location_Name: row.WORKLOCATION_NAME,
            Location_Code: row.WORKLOCATION_CODE,
            Location_Type: 'SubBranch',
            Street1: '',
            Phone: '',
            Currency_Symbol: 'PHP',
            City_Id: row.CITY_ID,
            State_Id: row.STATE_ID,
            Country_Code: "PH",
            Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            Added_By: 1,
            Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            Updated_By: 1
          };
          locationData.push(locationRow);
        });

        // Insert/Update Locations
        await orgDbConnection(ehrTables.location)
          .insert(locationData)
          .onConflict('Location_Code')
          .merge();
      } else {
        failureLogs.push({
          Integration_Type: 'Entomo - HRAPP',
          Entity_Type: 'Location',
          Primary_Id: null,
          Failure_Data: null,
          Failure_Reason: 'Sheet WL_DATA is empty',
          Integration_Date: moment().utc().format('YYYY-MM-DD'),
        })
      }
    }

    // **Step 6: Process CC_DATA Sheet and Update Cost Center Records**
    console.log('Inside insertPositionMasterData - CC_DATA');
    const businessUnitSheetName = 'CC_DATA';

    if (!workbook.Sheets[businessUnitSheetName]) {
      console.log(`Sheet "${businessUnitSheetName}" not found in the Excel file.`);
      failureLogs.push({
        Integration_Type: 'Entomo - HRAPP',
        Entity_Type: 'Business Unit',
        Primary_Id: null,
        Failure_Data: null,
        Failure_Reason: 'Sheet CC_DATA not found',
        Integration_Date: moment().utc().format('YYYY-MM-DD'),
      });
    } else {
      const businessUnitWorksheet = workbook.Sheets[businessUnitSheetName];
      // Convert the sheet data to JSON
      const jsonDataBusinessUnit = XLSX.utils.sheet_to_json(businessUnitWorksheet);
      if (jsonDataBusinessUnit?.length > 0) {
        const businessUnit = [];
        jsonDataBusinessUnit.forEach(row => {
          const businessUnitRow = {
            Business_Unit: row.COSTCENTERNAMEEN,
            Business_Unit_Code: row.WORKLOCATION_CODE,
            Business_Unit_Status: row.STATUS === 1 ? 'Active' : 'InActive',
            Description: '',
            Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            Added_By: 1,
            Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            Updated_By: 1
          };
          businessUnit.push(businessUnitRow);
        });

        // Insert/Update Business Units
        await orgDbConnection(ehrTables.businessUnit)
          .insert(businessUnit)
          .onConflict('Business_Unit_Code')
          .merge();
      } else {
        failureLogs.push({
          Integration_Type: 'Entomo - HRAPP',
          Entity_Type: 'Business Unit',
          Primary_Id: null,
          Failure_Data: null,
          Failure_Reason: 'Sheet CC_DATA is empty',
          Integration_Date: moment().utc().format('YYYY-MM-DD'),
        })
      }
    }

    if (failureLogs.length) {
      return {
        success: false,
        message: formEmailContent(failureLogs),
        failedData: failureLogs
      };
    } else {
      return {
        success: true,
        message: formEmailContent(failureLogs)
      };
    }
  } catch (error) {
    console.error('Error in insertPositionMasterData', error);
    throw error;
  }
}

/**
 * Synchronizes employee data from the provided JSON data.
 *
 * @param {Array<Object>} jsonData - Array of employee data objects to be synchronized.
 * @param {string} jsonData[].POS_CODE - Position code of the employee.
 * @param {string} jsonData[].ORGANIZATION_UNIT_CODE - Department code of the employee.
 * @param {string} jsonData[].EMPLOYMENTSTATUS_CODE - Employment status code of the employee.
 * @param {string} jsonData[].JOBSTATUS_CODE - Job status code of the employee.
 * @param {string} jsonData[].COMPANY_ID - Company ID of the employee.
 * @param {string} jsonData[].SUPERVISORID - Supervisor ID of the employee.
 * @param {string} jsonData[].CURRENT_EMP_NO - Current employee number.
 * @param {string} jsonData[].EMAIL - Email of the employee.
 * @param {string} jsonData[].EFFECTIVEDT - Effective date of the employee's job information.
 * @param {string} jsonData[].JOIN_DATE - Join date of the employee.
 * @param {string} jsonData[].USER_STATUS - User status of the employee.
 * @param {string} jsonData[].FIRST_NAME - First name of the employee.
 * @param {string} jsonData[].MIDDLE_NAME - Middle name of the employee.
 * @param {string} jsonData[].LAST_NAME - Last name of the employee.
 *
 * @returns {Promise<Array<Object>>} - Returns a promise that resolves to an array of failed log objects.
 * @throws {Error} - Throws an error if there is an issue during the synchronization process.
 */
async function syncEmployeeData(jsonData) {
  try {
    console.log('Inside syncEmployeeData');
    let failedLogs = []
    let customGroupRefreshEmployees = []
    let dateOfJoinChangedEmployees = []
    const acceptableCodes = ['JF_DEPT', 'JF_DIV', 'JF_GRP', 'JFL_DIV', 'JFL_DEPT', 'JFL_GRP']

    const currentDate = moment.utc().format('YYYY-MM-DD');

    // Extract unique values from jsonData to minimize redundant queries
    const posCodes = [...new Set(jsonData.map(row => row.POS_CODE).filter(Boolean))];
    const deptCodes = [...new Set(jsonData.map(row => row.ORGANIZATION_UNIT_CODE).filter(Boolean))];
    const empTypes = [...new Set(jsonData.map(row => row.EMPLOYMENTSTATUS_CODE).filter(Boolean))];
    const jobStatuses = [...new Set(jsonData.map(row => row.JOBSTATUS_CODE).filter(Boolean))];
    const companyIds = [...new Set(jsonData.map(row => row.COMPANY_ID).filter(Boolean))];
    const supervisorIds = [...new Set(jsonData.map(row => row.SUPERVISORID).filter(Boolean))];
    const empNos = [...new Set(jsonData.map(row => row.CURRENT_EMP_NO).filter(Boolean))];

    // Pre-fetch mapping data from DB in parallel
    const [designationData, departmentData, empTypeData, serviceProviderData, organizationGroupData, managerData, emailData] = await Promise.all([
      posCodes?.length ? orgDbConnection(ehrTables.designation).select('Designation_Id', 'Designation_Code').whereIn('Designation_Code', posCodes) : [],
      deptCodes?.length ? orgDbConnection(ehrTables.department).select('Department_Id', 'Department_Code').whereIn('Department_Code', deptCodes) : [],
      empTypes?.length ? orgDbConnection(ehrTables.empType).select('EmpType_Id', 'Employee_Type_Code').whereIn('Employee_Type_Code', empTypes) : [],
      jobStatuses?.length ? orgDbConnection(ehrTables.serviceProvider).select('Service_Provider_Id', 'Service_Provider_Code').whereIn('Service_Provider_Code', jobStatuses) : [],
      companyIds?.length ? orgDbConnection(ehrTables.organizationGroup).select('Organization_Group_Id', 'Organization_Group_Code').whereIn('Organization_Group_Code', companyIds) : [],
      supervisorIds?.length ? orgDbConnection(ehrTables.empJob).select('Employee_Id', 'User_Defined_EmpId').whereIn('User_Defined_EmpId', supervisorIds) : [],
      orgDbConnection(ehrTables.empJob).select('Emp_Email', 'Employee_Id').where('Emp_Status', 'Active')
    ]);

    // Convert query results into lookup maps
    const designationMap = new Map(designationData.map(d => [d.Designation_Code, d.Designation_Id]));
    const departmentMap = new Map(departmentData.map(d => [d.Department_Code, d.Department_Id]));
    const empTypeMap = new Map(empTypeData.map(e => [e.Employee_Type_Code, e.EmpType_Id]));
    const serviceProviderMap = new Map(serviceProviderData.map(s => [s.Service_Provider_Code, s.Service_Provider_Id]));
    const organizationGroupMap = new Map(organizationGroupData.map(o => [o.Organization_Group_Code, o.Organization_Group_Id]));
    const managerMap = new Map(managerData.map(m => [m.User_Defined_EmpId, m.Employee_Id]));
    const emailMap = new Map(emailData.map(e => [e.Emp_Email, e.Employee_Id]));

    // Fetch employee data in a single query
    const employeeData = await orgDbConnection(ehrTables.empJob + ' as EJ')
      .select(
        'EJ.Employee_Id as employeeId',
        'EJ.User_Defined_EmpId',
        'EJ.Emp_Email'
      )
      .whereIn('EJ.User_Defined_EmpId', empNos);

    const employeeMap = new Map(employeeData.map(emp => [emp.User_Defined_EmpId, emp]));

    // Start database transaction
    await orgDbConnection.transaction(async trx => {
      for (let el of jsonData) {
        let currentData = employeeMap.get(el.CURRENT_EMP_NO);

        if (!currentData) {
          if (acceptableCodes.includes(el.JF_CODE)) {
            failedLogs.push({
              Integration_Type: 'Entomo - HRAPP',
              Entity_Type: 'Employee',
              Primary_Id: null,
              Failure_Data: JSON.stringify(el),
              Failure_Reason: 'Employee not found',
              Integration_Date: currentDate,
            })
          }
          continue;
        }

        // Validation 1: Same Email Exists
        if (el.EMAIL && emailMap.has(el.EMAIL) && emailMap.get(el.EMAIL) !== currentData.employeeId) {
          failedLogs.push({
            Integration_Type: 'Entomo - HRAPP',
            Entity_Type: 'Employee',
            Primary_Id: null,
            Failure_Data: JSON.stringify(el),
            Failure_Reason: 'Email already exists',
            Integration_Date: currentDate,
          });
          continue;
        }

        const jobInfoData = {
          Manager_Id: managerMap.get(el.SUPERVISORID) ? managerMap.get(el.SUPERVISORID) : 0,
          Manager_Id_Effective_Date: moment(el.EFFECTIVEDT, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD'),
          Designation_Id: designationMap.get(String(el.POS_CODE)),
          Designation_Id_Effective_Date: moment(el.EFFECTIVEDT, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD'),
          Department_Id: departmentMap.get(String(el.ORGANIZATION_UNIT_CODE)),
          Department_Id_Effective_Date: moment(el.EFFECTIVEDT, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD'),
          EmpType_Id: empTypeMap.get(String(el.EMPLOYMENTSTATUS_CODE)),
          EmpType_Id_Effective_Date: moment(el.EFFECTIVEDT, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD'),
          Service_Provider_Id: serviceProviderMap.get(String(el.JOBSTATUS_CODE)),
          Organization_Group_Id: organizationGroupMap.get(String(el.COMPANY_ID)),
          Date_Of_Join: moment(el.JOIN_DATE, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD'),
          Emp_Status: el.USER_STATUS === 'Active' ? 'Active' : 'InActive',
          Emp_Email: el.EMAIL
        }

        //Validate if the data exits
        if (!jobInfoData.Designation_Id || !jobInfoData.Department_Id || !jobInfoData.EmpType_Id || !jobInfoData.Service_Provider_Id || !jobInfoData.Organization_Group_Id) {
          failedLogs.push({
            Integration_Type: 'Entomo - HRAPP',
            Entity_Type: 'Employee',
            Primary_Id: null,
            Failure_Data: JSON.stringify(el),
            Failure_Reason: `Data not found in the database. 
            POS_CODE (${el.POS_CODE}): ${jobInfoData.Designation_Id ? 'Available' : 'Not Available'}, 
            ORGANIZATION_UNIT_CODE (${el.ORGANIZATION_UNIT_CODE}): ${jobInfoData.Department_Id ? 'Available' : 'Not Available'}, 
            EMPLOYMENTSTATUS_CODE (${el.EMPLOYMENTSTATUS_CODE}): ${jobInfoData.EmpType_Id ? 'Available' : 'Not Available'}, 
            JOBSTATUS_CODE (${el.JOBSTATUS_CODE}): ${jobInfoData.Service_Provider_Id ? 'Available' : 'Not Available'},
            COMPANY_ID (${el.COMPANY_ID}): ${jobInfoData.Organization_Group_Id ? 'Available' : 'Not Available'}`,
            Integration_Date: moment().utc().format('YYYY-MM-DD'),
          })
          continue;
        }

        // Validate if DOJ is changed
        if (moment(el.JOIN_DATE, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') !== moment(currentData.Date_Of_Join).format('YYYY-MM-DD')) {
          dateOfJoinChangedEmployees.push(currentData.employeeId);
          // Validate if any job details changed
        } else if (jobInfoData.Designation_Id !== currentData.Designation_Id || jobInfoData.Department_Id !== currentData.Department_Id || jobInfoData.EmpType_Id !== currentData.EmpType_Id) {
          customGroupRefreshEmployees.push(currentData.employeeId);
        }

        // Execute updates in parallel inside the transaction
        await Promise.all([
          // Update Timestamp
          trx(ehrTables.employeeInfoTimestampLog).insert({
            Employee_Id: currentData.employeeId,
            Action: 'Update',
            Log_Timestamp: moment.utc().format("YYYY-MM-DD HH:mm:ss"),
          }),

          // Update Employee Job
          trx(ehrTables.empJob)
            .where('Employee_Id', currentData.employeeId)
            .update(jobInfoData),

          // Update Is_Manager for the Manager_Id if it exists
          jobInfoData.Manager_Id && trx(ehrTables.empPersonalInfo)
            .where('Employee_Id', jobInfoData.Manager_Id)
            .update({
              Is_Manager: 1
            }),

          // Update Employee Personal Info
          trx(ehrTables.empPersonalInfo)
            .where('Employee_Id', currentData.employeeId)
            .update({
              Emp_First_Name: el.FIRST_NAME,
              Emp_Middle_Name: el.MIDDLE_NAME,
              Emp_Last_Name: el.LAST_NAME,
            }),
        ]);

      }
      console.log('Custom group, DOJ', customGroupRefreshEmployees, dateOfJoinChangedEmployees);
      await initiateCustomGroupRefresh(customGroupRefreshEmployees, dateOfJoinChangedEmployees);
    })


    return failedLogs;
  }
  catch (error) {
    console.error('Error in syncEmployeeData function', error);
    throw error;
  }
}

/**
 * Initiates the custom group refresh process for employees.
 * 
 * This function triggers the step functions to refresh custom groups and update the date of joining for employees.
 * 
 * @param {Array} customGroupRefreshEmployees - List of employee IDs that require custom group refresh.
 * @param {Array} dateOfJoinChangedEmployees - List of employee IDs whose date of joining has changed.
 * @returns {Promise<void>} - A promise that resolves when the step functions are triggered.
 * @throws {Error} - Throws an error if the step functions fail to trigger.
 */
async function initiateCustomGroupRefresh(customGroupRefreshEmployees, dateOfJoinChangedEmployees) {
  try {
    if (customGroupRefreshEmployees.length || dateOfJoinChangedEmployees.length) {
      if (customGroupRefreshEmployees.length > 0) {
        let customGroupInputParams = {
          orgCode: orgCode,
          employeeId: customGroupRefreshEmployees,
          logInEmpId: 1,
          isCustomGroupRefresh: 1,
        }
        await commonLib.stepFunctions.triggerStepFunction(process.env.refreshCustomGroupStateMachineArn, 'refreshCustomGroup', '', customGroupInputParams);
      }

      if (dateOfJoinChangedEmployees.length > 0) {
        let dateOfJoinInputParams = {
          orgCode: orgCode,
          employeeId: dateOfJoinChangedEmployees,
          logInEmpId: 1,
          isDOJUpdated: true,
        }
        await commonLib.stepFunctions.triggerStepFunction(process.env.refreshCustomGroupStateMachineArn, 'refreshCustomGroup', '', dateOfJoinInputParams);
      }
    }
  }
  catch (error) {
    console.log('Error in initiateCustomGroupRefresh function catch block.', error);
    throw error;
  }
}

/**
 * Sends an email with the failed logs attached.
 * 
 * @param {Array<Object>} failedData - Array of failed log objects.
 * @param {Object} scheduleDetails - Details of the schedule.
 * @param {Object} [emailParams={}] - Additional email parameters.
 * @param {string} [emailParams.mailContent] - The content of the email.
 * @param {string} [subject='Sync Data Report'] - The subject of the email.
 * @returns {Promise<void>} - A promise that resolves when the email is sent.
 */
async function sendFailedLogs(failedData, scheduleDetails, emailParams = {}, subject = 'Sync Data Report') {
  const nodemailer = require('nodemailer');
  const path = require('path');
  const handlebars = require('handlebars');
  const AWS = require("aws-sdk");
  AWS.config.update({ region: process.env.sesTemplatesRegion });

  try {
    // Read the HTML template
    const emailTemplate = path.join(__dirname, '/notificationEmail.html');
    const templateData = await fs.promises.readFile(emailTemplate, 'utf8');

    // Compile the template
    const template = handlebars.compile(templateData);

    // Generate the populated HTML
    const populatedHtml = template(emailParams);

    const transporter = nodemailer.createTransport({
      SES: new AWS.SES({ apiVersion: '2010-12-01' })
    });

    //Initialize attachments
    let attachments = [];

    // Initialize Workbook
    const workbook = XLSX.utils.book_new();

    const orgFailedData = failedData.filter((item) => item.Entity_Type === 'Designation' || item.Entity_Type === 'Department' || item.Entity_Type === 'Position Master');
    const employeeFailedData = failedData.filter((item) => item.Entity_Type === 'Employee');
    const locationFailedData = failedData.filter((item) => item.Entity_Type === 'Location');
    const businessUnitFailedData = failedData.filter((item) => item.Entity_Type === 'Business Unit');

    // Process Org Structure Failed Data (if exists)
    if (orgFailedData && orgFailedData.length > 0) {
      if (orgFailedData.length > 1 || (orgFailedData.length === 1 && orgFailedData[0].Failure_Data)) {
        const orgResult = orgFailedData.map(item => ({
          ...JSON.parse(item.Failure_Data),
          Failure_Reason: item.Failure_Reason,
        }));
        const orgWorksheet = XLSX.utils.json_to_sheet(orgResult);
        XLSX.utils.book_append_sheet(workbook, orgWorksheet, 'ORG_DATA');
      }
    }

    // Process Employee Failed Data (if exists)
    if (employeeFailedData && employeeFailedData.length > 0) {
      if (employeeFailedData.length > 1 || (employeeFailedData.length === 1 && employeeFailedData[0].Failure_Data)) {
        const empResult = employeeFailedData.map(item => ({
          ...JSON.parse(item.Failure_Data),
          Failure_Reason: item.Failure_Reason,
        }));
        const empWorksheet = XLSX.utils.json_to_sheet(empResult);
        XLSX.utils.book_append_sheet(workbook, empWorksheet, 'EMPLOYEE_DATA');
      }
    }

    // Process Location Failed Data (if exists)
    if (locationFailedData && locationFailedData.length > 0) {
      if (locationFailedData.length > 1 || (locationFailedData.length === 1 && locationFailedData[0].Failure_Data)) {
        const locationResult = locationFailedData.map(item => ({
          ...JSON.parse(item.Failure_Data),
          Failure_Reason: item.Failure_Reason,
        }));
        const locationWorksheet = XLSX.utils.json_to_sheet(locationResult);
        XLSX.utils.book_append_sheet(workbook, locationWorksheet, 'WL_DATA');
      }
    }

    // Process Business Unit Failed Data (if exists)
    if (businessUnitFailedData && businessUnitFailedData.length > 0) {
      if (businessUnitFailedData.length > 1 || (businessUnitFailedData.length === 1 && businessUnitFailedData[0].Failure_Data)) {
        const businessUnitResult = businessUnitFailedData.map(item => ({
          ...JSON.parse(item.Failure_Data),
          Failure_Reason: item.Failure_Reason,
        }));
        const businessUnitWorksheet = XLSX.utils.json_to_sheet(businessUnitResult);
        XLSX.utils.book_append_sheet(workbook, businessUnitWorksheet, 'CC_DATA');
      }
    }

    // Only add attachment if there's data
    if (orgFailedData.length > 0 || employeeFailedData.length > 0 || locationFailedData.length > 0 || businessUnitFailedData.length > 0) {

      if (workbook && workbook.SheetNames?.length > 0) {
        // Generate XLSX Buffer
        const xlsxBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

        // Add XLSX file to attachments
        attachments.push({
          filename: 'API_Data_Export.xlsx',
          content: xlsxBuffer,
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
      }
    }

    const mailOptions = {
      from: process.env.emailFrom,
      to: scheduleDetails.Email_TO ? JSON.parse(scheduleDetails.Email_TO).join(', ') : null,
      bcc: scheduleDetails.Email_BCC ? JSON.parse(scheduleDetails.Email_BCC).join(', ') : null,
      cc: scheduleDetails.Email_CC ? JSON.parse(scheduleDetails.Email_CC).join(', ') : null,
      subject: subject,
      html: populatedHtml,
      attachments: attachments
    };

    // Send Email
    await transporter.sendMail(mailOptions)

  } catch (error) {
    console.log('Error in sendFailedLogs function main catch block.', error);
    throw error;
  }
}

/**
 * Generates the HTML content for an email based on the provided failure logs.
 *
 * @param {Array<Object>} failureLogs - An array of failure log objects.
 * @param {string} failureLogs[].Entity_Type - The type of entity that failed (e.g., 'Designation', 'Department', 'Position Master', 'Employee').
 * @param {string} [failureLogs[].Failure_Data] - The data associated with the failure, if any.
 * @returns {string} The HTML content for the email.
 * @throws Will throw an error if there is an issue generating the email content.
 */
function formEmailContent(failureLogs) {
  console.log('Inside formEmailContent', failureLogs[0]);
  try {
    const positionMasterFailed = failureLogs.filter(f =>
      f.Entity_Type === 'Designation' || f.Entity_Type === 'Department' || f.Entity_Type === 'Position Master');
    const employeeDetailsFailed = failureLogs.filter(f => f.Entity_Type === 'Employee');
    const workLocationFailed = failureLogs.filter(f => f.Entity_Type === 'Location');
    const costCentreFailed = failureLogs.filter(f => f.Entity_Type === 'Business Unit');
    const dataExists = failureLogs.some(f => f.Failure_Data);

    const positionMasterMessage = positionMasterFailed.length === 1 && !positionMasterFailed[0].Failure_Data?.length ? 'Data not found' : 'Failure';
    const employeeDetailsMessage = employeeDetailsFailed.length === 1 && !employeeDetailsFailed[0].Failure_Data?.length ? 'Data not found' : 'Failure';
    const workLocationMessage = workLocationFailed.length ? 'Data not found' : 'Success';
    const costCentreMessage = costCentreFailed.length ? 'Data not found' : 'Success';

    return `
        <p style="font-size: 16px; font-weight: bold; line-height: 1.5; mso-line-height-alt: 21px; margin: 0;">
          Please find here the data sync status
        </p>
        <ul style="font-size: 14px; line-height: 1.6; margin: 10px 0; padding-left: 20px;">
          <li style="color: ${positionMasterFailed.length ? 'red' : 'green'}; font-weight: bold;">
            Position Master - ${positionMasterFailed.length ? positionMasterMessage : 'Success'}
          </li>
          <li style="color: ${workLocationFailed.length ? 'red' : 'green'}; font-weight: bold;">
            Work Location - ${workLocationMessage}
          </li>
          <li style="color: ${employeeDetailsFailed.length ? 'red' : 'green'}; font-weight: bold;">
            Employee Details - ${employeeDetailsFailed.length ? employeeDetailsMessage : 'Success'}
          </li>
          <li style="color: ${costCentreFailed.length ? 'red' : 'green'}; font-weight: bold;">
            Cost Centre - ${costCentreMessage}
          </li>
        </ul>
        ${dataExists ? `<p style="font-size: 14px; font-weight: bold; line-height: 1.5; mso-line-height-alt: 21px; margin: 0;">
          The attached file contains failed records.
        </p>` : ''}
    `;
  } catch (error) {
    console.log('Error in formEmailContent function main catch block.', error);
    throw error;
  }
}
