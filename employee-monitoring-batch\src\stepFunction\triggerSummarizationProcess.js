'use strict';
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require constant value
const { defaultValues }=require('../common/appConstants');
// require common function
const { getSummarizationDateBasedOnZone,updateSummaryStatusBasedOnId,updateSummaryDataInMasterTable,updateStatusInOrganizationStatusTable,insertNewWorkScheduleData,calculateActivityBasedOnActiveStatus }=require('./commonFunctions');
const { getEmployeeIdsBasedOnWorkSchedule,formMinAndMaxTimeBasedOnWorkSchedule,getGroupIdForEmployeeIds }=require('../common/activityCommonFunctions');
const {getDateTimeBasedOnTimeZoneOffset,getDurationBasedOnStatus,getEmployeeAssociatedGroupId,getTimeInHoursMinutesSeconds } = require('../webApplication/employeeMonitoringCommonFunction');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let summarizationStatusTable;
let masterTable;
let inputSource;
let inputStatus;
let orgCode='';
let dailySummaryTable;
let organizationDbConnection='';
let appmanagerDbConnection='';

// function to trigger daily summary process for app/url/employee activites
module.exports.triggerSummarizationProcess  = async(event, context) =>{
    try{
        console.log('Inside triggerSummarizationProcess function');
        // get input data
        let inputData=event;
        inputSource=inputData.process;
        inputStatus=inputData.status;
        orgCode=inputData.orgCode;
        let managerStatus='Success';
        let minSummarizationDate;
        let activityDate;
        let idField;
        let updateManagerTableInputs={};

        // Same logic is used for different input source so define the table and field name
        if(inputSource==='app'){
            summarizationStatusTable=ehrTables.appActivitySummarizationStatus;
            dailySummaryTable=ehrTables.appActivityDailySummary;
            masterTable=appManagerTables.appActivitySummarizationManager;
            idField='App_Summarization_Id';
            activityDate='App_Summarization_Date';
        }
        else if(inputSource==='url'){
            summarizationStatusTable=ehrTables.urlActivitySummarizationStatus;
            dailySummaryTable=ehrTables.urlActivityDailySummary;
            masterTable=appManagerTables.urlActivitySummarizationManager;
            idField='Url_Summarization_Id';
            activityDate='Url_Summarization_Date';
        }
        else if(inputSource==='appTitle')
        {
            summarizationStatusTable=ehrTables.appTitleActivitySummarizationStatus;
            dailySummaryTable=ehrTables.appTitleActivityDailySummary;
            masterTable=appManagerTables.appTitleActivitySummarizationManager;
            idField='App_Title_Summarization_Id';
            activityDate='App_Title_Summarization_Date';   
        }
        else{
            summarizationStatusTable=ehrTables.employeeActivitySummarizationStatus;
            dailySummaryTable=ehrTables.employeeActivityDailySummary;
            masterTable=appManagerTables.employeeActivitySummarizationManager;
            idField='Activity_Summarization_Id';
            activityDate='Activity_Summarization_Date';
        };
        
        let getDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        appmanagerDbConnection=knex(getDbConnection.AppManagerDb);
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
        
        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
            

            // make organization database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length){
                organizationDbConnection = knex(databaseConnection.OrganizationDb);

                if(inputSource === "url"){
                    await processOrganizationUrlDomainWhitelisting(organizationDbConnection);
                    await processCustomGroupUrlDomainWhitelisting(organizationDbConnection);
                    await processOrganizationBlockedDomains(organizationDbConnection);
                    await processEmployeeBlockedDomains(organizationDbConnection);  
                }
                
                // get the records with execution status as 'Executing'
                let subQuery=organizationDbConnection(summarizationStatusTable)
                .select('WorkSchedule_Id','Regular_Work_Start_Time','Regular_Work_End_Time','Twodays_Flag','Shift_Margin_From as Check_In_Consideration_Time','Shift_Margin_To as Check_Out_Consideration_Time','TimeZone')
                .where('Execution_Status','Executing')
                if(inputSource==='app'){
                    subQuery=subQuery
                    .select('App_Summarization_Id','App_Summarization_Date')
                }
                else if(inputSource==='appTitle')
                {
                    subQuery=subQuery
                    .select('App_Title_Summarization_Id','App_Title_Summarization_Date')
                }
                else if(inputSource==='url'){
                    subQuery=subQuery
                    .select('Url_Summarization_Id','Url_Summarization_Date')
                }
                else{
                    subQuery=subQuery
                    .select('Activity_Summarization_Id','Activity_Summarization_Date')
                }
                return(
                    subQuery
                    .then(async(getRecordToBeProcessed) => {
                        if(getRecordToBeProcessed.length>0){
                            let failedWSArray=[];
                            let summaryDateArray=[];
                            let summarizationPendingIds=[];
                            // iterate the function for all the work schedule records
                            for(let record of getRecordToBeProcessed){
                                let WorkScheduleId=record.WorkSchedule_Id;
                                let getTimeZone=record.TimeZone;
                                let id=record[idField];
                                let wsActivityDate=record[activityDate];
                                // form the summarization date based on workschedule timezone
                                let summarizationDateBasedOnZone=await getSummarizationDateBasedOnZone(getTimeZone);
                                // calculate the date difference between current date and the last summary date
                                let dateDifference = moment(summarizationDateBasedOnZone).diff(moment(wsActivityDate), 'days');
                                // if there is date difference then update the status in manager table as Open else as Success/Failed
                                if(dateDifference>0){
                                    managerStatus='Open'
                                    summarizationPendingIds.push(WorkScheduleId);
                                }
                                // form the date array and find the minimum value of all the work scheduleId
                                summaryDateArray.push(new Date(wsActivityDate));
                                minSummarizationDate= new Date(Math.min.apply(null,summaryDateArray));
                                minSummarizationDate = moment(minSummarizationDate).format('YYYY-MM-DD');

                                try{
                                    // get the employeeId list associated for the workscheduleId
                                    let employeeIdList=await getEmployeeIdsBasedOnWorkSchedule(organizationDbConnection,WorkScheduleId,['Active','Inactive']);
                                    if(employeeIdList.length>0){
                                        // calculate the min and max time for the work schedule
                                        let { minDateTime, maxDateTime, minDateTimeByZone, maxDateTimeByZone }= await formMinAndMaxTimeBasedOnWorkSchedule(organizationDbConnection,WorkScheduleId,wsActivityDate,record.TimeZone,[record])
                                        // summarize the data based on source and time range
                                        let addData=await summarizeDataBasedOnSource(organizationDbConnection,summarizationStatusTable,inputSource,minDateTime,maxDateTime,employeeIdList,getTimeZone,wsActivityDate,id,WorkScheduleId, minDateTimeByZone, maxDateTimeByZone);
                                        
                                    }
                                    else{
                                        let updateSuccessWithReason=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id,'No employees found in this workschedule.');
                                    }
                                }
                                catch(error){
                                    console.log('Error in summarization process-',error);
                                    failedWSArray.push(WorkScheduleId);
                                }
                            }

                            // based on source define the field name
                            if(inputSource==='app'){
                                updateManagerTableInputs.App_Summarization_Date = (managerStatus==='Success')?(minSummarizationDate):(moment(minSummarizationDate).add(1,'d').format('YYYY-MM-DD'));
                            }
                            else if(inputSource==='appTitle')
                            {
                                updateManagerTableInputs.App_Title_Summarization_Date = (managerStatus==='Success')?(minSummarizationDate):(moment(minSummarizationDate).add(1,'d').format('YYYY-MM-DD'));
                            }
                            else if(inputSource==='url'){
                                updateManagerTableInputs.Url_Summarization_Date=(managerStatus==='Success')?(minSummarizationDate):(moment(minSummarizationDate).add(1,'d').format('YYYY-MM-DD'));
                            }
                            else{
                                updateManagerTableInputs.Activity_Summarization_Date=(managerStatus==='Success')?(minSummarizationDate):(moment(minSummarizationDate).add(1,'d').format('YYYY-MM-DD'));
                            }
                            /** When value exist in failed work scheduled array then update the failed status. 
                             * When input status is failed then we update it as Unsuccessful(after retrying) */
                            if(failedWSArray.length>0){
                                if(inputStatus==='Failed'){
                                    updateManagerTableInputs.Status="Unsuccessful";
                                }
                                else{
                                    updateManagerTableInputs.Status="Failed";
                                }
                                // update failed status in manager table
                                await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,masterTable);
                                console.log('Failed status updated in manager table for '+orgCode+' instance');
                                organizationDbConnection?organizationDbConnection.destroy():null;
                                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                                return '';
                            }
                            else{
                                // if there is date difference in summarization date then insert the work schedule data in status table
                                if(managerStatus==='Open'){
                                    await insertNewWorkScheduleData(organizationDbConnection,summarizationStatusTable,inputSource,summarizationPendingIds);
                                }
                                updateManagerTableInputs.Status=managerStatus;
                                // update the status in manager table
                                await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,masterTable);
                                
                                organizationDbConnection?organizationDbConnection.destroy():null;
                                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                                return 'success';
                            }
                        }
                        else{
                        // console.log('No records are in inprogress status so quit the process after updating the status.');
                        updateManagerTableInputs.Status=managerStatus;
                        // update the status in manager table
                        await updateSummaryDataInMasterTable(appmanagerDbConnection,updateManagerTableInputs,orgCode,masterTable);
                        console.log('Success status updated in manager table for '+orgCode+' instance');
                        organizationDbConnection?organizationDbConnection.destroy():null;
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        return 'success';
                        }
                    })
                    .catch(async(catchError) => {
                        console.log('Error in triggerSummarizationProcess function .catch block.', catchError);
                        organizationDbConnection?organizationDbConnection.destroy():null;
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        await updateFailedStatus('Error while processing the daily summarization process.');
                        return '';
                    })
                )
            }
            else{
                console.log('Error in organization database connection for '+orgCode+' instance');
                await updateFailedStatus('Error in creating database connection while summarizing the daily activities.');
                organizationDbConnection?organizationDbConnection.destroy():null;
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                return '';
            }
        }
        else{
            console.log('Error in getting the data region for '+orgCode+' instance');
            await updateFailedStatus('Error in getting the data region while summarizing the daily activities.');
            organizationDbConnection?organizationDbConnection.destroy():null;
            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
            return '';
        }
    } catch (mainCatchError){
        console.log('Error in triggerSummarizationProcess function main catch block.', mainCatchError);
        await updateFailedStatus('Error while processing the daily summarization process .');
        organizationDbConnection?organizationDbConnection.destroy():null;
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        return '';
    }
};

// function to summarize the data based on source and insert into summary table
async function summarizeDataBasedOnSource(organizationDbConnection,summarizationStatusTable,inputSource,minTime,maxTime,employeeIdArray,getTimeZone,date,id,workScheduleId, minDateTimeByZone, maxDateTimeByZone){
    try{
        let updatedOn=moment.tz(moment(), 'Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss')// store time in ist format
        // when input source is activity

        let alreadyProcessedEmpIds = await organizationDbConnection(dailySummaryTable)
                                .select('Employee_Id')
                                .whereIn('Employee_Id',employeeIdArray)
                                .where('Activity_Date', date)
                                .then(async(existIds) =>{
                                    return existIds && existIds.length > 0 ? existIds : [];
                                }).catch(function (catchError) {
                                    console.log('Error while getting the already processed employee ids.', catchError);
                                    return [];
                                });

        /** Remove already processed employee ids */
        employeeIdArray = employeeIdArray.filter(value => !alreadyProcessedEmpIds.includes(value));
        
        if(inputSource==='activity'){
            let fixedDailyWorkHours = await organizationDbConnection(ehrTables.employeeMonitorSettings)
                                .select('Fixed_Daily_Work_Hours')
                                .then(async(fixedWorkHours) =>{
                                    return fixedWorkHours && fixedWorkHours.length > 0 ? fixedWorkHours[0].Fixed_Daily_Work_Hours : 0;
                                }).catch(function (catchError) {
                                    console.log('Error while getting the fixed daily work hours settings .catch block', catchError);
                                    return 0;
                                });
            
            // find the offset for the zone
            let offSet = moment.tz(moment.utc(), getTimeZone).utcOffset();
            return(
             await organizationDbConnection(ehrTables.empActivityDetails)
                .select('EAD.Employee_Id',
                    organizationDbConnection.raw('CASE WHEN sum(EAD.Total_Activity_Duration) > 30 then ROUND((100*sum(EAD.Productive_Activity_Duration)/sum(EAD.Total_Activity_Duration)),2)  ELSE "0.00" END as systemProductivityPercentageIncludesIdleAndNotActiveTime'),
                    organizationDbConnection.raw('CASE WHEN sum(EAD.Total_Activity_Duration) > 0 then TIME_FORMAT(SEC_TO_TIME(SUM(EAD.Total_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as totalActivityDuration'),
                    organizationDbConnection.raw('CASE WHEN sum(EAD.Total_Activity_Duration) > 0 then ROUND(SUM(Total_Activity_Duration)/60) ELSE "0" END as totalActivityDurationInMinutes'),
                    organizationDbConnection.raw('CASE WHEN sum(EAD.Productive_Activity_Duration) > 0 then ROUND(SUM(EAD.Productive_Activity_Duration)/60) ELSE "0" END as productiveActivityDurationInMinutes'),
                    organizationDbConnection.raw('CASE WHEN sum(EAD.Productive_Activity_Duration) > 0 then TIME_FORMAT(SEC_TO_TIME(SUM(EAD.Productive_Activity_Duration)),"%H:%i:%s")  ELSE "00:00:00" END as totalProductivityDuration'),
                    organizationDbConnection.raw(`
                    CASE
                    WHEN COUNT(DISTINCT CASE WHEN Work_Location IN ('Office', 'Remote') THEN Work_Location END) = 2 THEN 'Hybrid'
                    WHEN MAX(CASE WHEN Work_Location = 'Office' THEN 1 ELSE 0 END) = 1
                    AND MAX(CASE WHEN Work_Location = 'Remote' THEN 1 ELSE 0 END) = 0 THEN 'Office'
                    WHEN MAX(CASE WHEN Work_Location = 'Remote' THEN 1 ELSE 0 END) = 1
                    AND MAX(CASE WHEN Work_Location = 'Office' THEN 1 ELSE 0 END) = 0 THEN 'Remote'
                    ELSE ''
                    END AS Work_Location`),
                    organizationDbConnection.raw('SUM(Productive_Activity_Duration) as totalProductiveDuration'))
                .from(ehrTables.empActivityDetails + ' as EAD')
                .whereIn('EAD.Employee_Id',employeeIdArray)
                .where('EAD.Activity_Start_Date_Time', '>=', minTime)
                .where(organizationDbConnection.raw("DATE_FORMAT(EAD.Activity_End_Date_Time, '%Y-%m-%d %H:%i')"), '<=', maxTime)
                .groupBy('EAD.Employee_Id')
                .then(async(dailySummaryDetails) => {
                    // Check dailySummaryDetails array is not empty
                    if (dailySummaryDetails.length>0) {
                        // get the checkIn/checkOut time and activeDuration based on the activity status and combine it with summary details
                        let getActiveActivityData=await calculateActivityBasedOnActiveStatus(organizationDbConnection,employeeIdArray,minTime,maxTime);
                        
                        if(getActiveActivityData.length>0){
                            dailySummaryDetails = dailySummaryDetails.map(obj => {
                                let data = getActiveActivityData.find(item => item.Employee_Id === obj.Employee_Id);    
                                return {...obj, ...data}
                            });
                        }

                        /** iterate the data and calculate the system productivity % exclude idle time */
                        for(let record of dailySummaryDetails){
                            if(record.activeDuration && record.activeDuration>30 && record.totalProductiveDuration){
                                record.systemProductivityPercentageExcludesIdleAndNotActiveTime=Math.round((record.totalProductiveDuration/record.activeDuration)*100).toFixed(2);
                            }
                            else{
                                record.systemProductivityPercentageExcludesIdleAndNotActiveTime='0.00';
                            }
                        }

                        // Form input data
                        const promises = dailySummaryDetails.map(async field=>({
                            Employee_Id: field.Employee_Id,
                            Activity_Summarization_Id:id,
                            WorkSchedule_Start_Time:minTime,
                            WorkSchedule_End_Time:maxTime,
                            Activity_Start_Hour: (field.checkInStartTime)?(getDateTimeBasedOnTimeZoneOffset(field.checkInStartTime, offSet)):null,
                            Activity_End_Hour: (field.checkOutEndTime)?(getDateTimeBasedOnTimeZoneOffset(field.checkOutEndTime, offSet)):null,
                            Timezone: getTimeZone,
                            Activity_Date: date,
                            Total_Activity_Duration: field.totalActivityDuration,
                            Total_Activity_Duration_In_Minutes:field.totalActivityDurationInMinutes,
                            Active_Duration_In_Minutes:await getDurationBasedOnStatus(field.Employee_Id,minTime, maxTime, organizationDbConnection,'Active','minutes'),
                            Active_Duration:await getDurationBasedOnStatus(field.Employee_Id,minTime, maxTime, organizationDbConnection,'Active','timeFormat'),
                            Not_Active_Duration_In_Minutes:await getDurationBasedOnStatus(field.Employee_Id,minTime, maxTime, organizationDbConnection,'Not Active','minutes'),
                            Not_Active_Duration:await getDurationBasedOnStatus(field.Employee_Id,minTime, maxTime, organizationDbConnection,'Not Active','timeFormat'),
                            Idle_Duration_In_Minutes:await getDurationBasedOnStatus(field.Employee_Id,minTime, maxTime, organizationDbConnection,'Idle','minutes'),
                            Idle_Duration:await getDurationBasedOnStatus(field.Employee_Id,minTime, maxTime, organizationDbConnection,'Idle','timeFormat'),
                            Productive_Activity_Duration: field.totalProductivityDuration,
                            Productive_Activity_Duration_In_Minutes:field.productiveActivityDurationInMinutes,
                            Sys_Productivity_Percentage_With_IdleTime: field.systemProductivityPercentageIncludesIdleAndNotActiveTime ? Math.round(field.systemProductivityPercentageIncludesIdleAndNotActiveTime).toFixed(2) : 0.00,
                            Sys_Productivity_Percentage_Without_IdleTime:field.systemProductivityPercentageExcludesIdleAndNotActiveTime,
                            Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours: fixedDailyWorkHours > 0 ? (Math.round((field.totalProductiveDuration/(fixedDailyWorkHours*3600))*100).toFixed(2)) : 0.00,
                            Load_Timestamp: updatedOn,
                            Work_Location:field.Work_Location,
                        })
                        
                        );
                        let summaryDetailsArray = await Promise.all(promises);
                        
                        return (
                            organizationDbConnection
                            .transaction(function (trx) {
                                // insert the summary data
                                return (
                                    organizationDbConnection(dailySummaryTable)
                                    .insert(summaryDetailsArray)
                                    .transacting(trx)
                                    .then(async(insertSummaryData)=>{
                                        await migrateToAttendanceMaster(organizationDbConnection, summaryDetailsArray, minDateTimeByZone, maxDateTimeByZone, orgCode);
                                        return insertSummaryData;
                                    })
                                )
                            })
                            .then(async(result) => {
                                let updateSuccessStatus=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id);
                                console.log('Success status updated in org status table in '+orgCode+' instance - ',updateSuccessStatus);
                                return (updateSuccessStatus)?(updateSuccessStatus):'';
                            })
                            .catch(async(catchError) => {
                                console.log('Error in summarizeDataBasedOnSource function .catch block -',catchError);
                                throw 'Error while inserting activity details';
                            })
                        )
                    }
                    else{
                        console.log('Activity details does not exist');
                        let updateSuccessWithReason=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id,'No '+inputSource+' details exist');
                        console.log('Success status updated in organization status table in '+orgCode+' instance - ',updateSuccessWithReason);
                        return (updateSuccessWithReason)?(updateSuccessWithReason):'';
                    }
                })
                .catch(async(catchError) => {
                    console.log('Error in processing activity details in summarizeDataBasedOnSource function .catch block -',catchError);
                    throw (catchError==='Error while inserting activity details')?'Error while inserting activity details':'Error while retrieving activity details';
                })
            )
        }
        else{
            // variable declarations
            let groupIdList=[];
            let customGroupEmployees=[];
            let orgLevelEmployeeIds=[];

            // get the employeeIds which has activity data within the min and max range
            employeeIdArray = await organizationDbConnection(ehrTables.empActivityDetails+ ' as EAD')
            .pluck('EAD.Employee_Id')
            .where('EAD.Activity_Start_Date_Time', '>=', minTime)
            .where(organizationDbConnection.raw("DATE_FORMAT(EAD.Activity_End_Date_Time, '%Y-%m-%d %H:%i')"), '<=', maxTime)
            .whereIn('EAD.Employee_Id', employeeIdArray)
            .groupBy('EAD.Employee_Id')

            // get the employeeIds along with custom groupId list
            let employeeGroupData=await getGroupIdForEmployeeIds(organizationDbConnection,employeeIdArray,defaultValues.customGroupSettingsFormId);
            
            /** If custom group data exist then get the groupId and custom group employees list separately. 
            * In this case instance may have both custom group and organization level settings. So get the employee list separately */
            if(employeeGroupData.length>0){
                // get the groupId list
                groupIdList = employeeGroupData.map(res=>res.Group_Id);
                // get the unique groupId without duplicates
                groupIdList=[...new Set(groupIdList)];
                // get the custom group employeeId list
                customGroupEmployees = employeeGroupData.map(res=>res.employeeId);
                // get the unique custom group employeeIds without duplicates
                customGroupEmployees=[...new Set(customGroupEmployees)];
                // check whether inputs employees have organization level settings or not
                orgLevelEmployeeIds = employeeIdArray.filter(function(obj) { return customGroupEmployees.indexOf(obj) == -1; });
            }
            
            // check whether input source is app
            if(inputSource==='app'){
                try{
                    let resultAppArray=[];
                    // check whether custom group data exist
                    if(employeeGroupData.length>0){
                        
                        let orgLevelArray=[];
                        let customGroupArray=[];
                        // if organization level employeeIds exist then get the app details along with category
                        if(orgLevelEmployeeIds.length>0){
                            orgLevelArray=await getAppDetailsBasedOnInput(orgLevelEmployeeIds,'orgLevel',minTime,maxTime);
                        }
                        if(customGroupEmployees.length>0){
                            // if custom group level employeeIds exist then get the app details
                            customGroupArray=await getAppDetailsBasedOnInput(customGroupEmployees,'group',minTime,maxTime);
                            // combine custom group details with the app details
                            customGroupArray = customGroupArray.map(obj => {
                                let data = employeeGroupData.find(item => item.employeeId === obj.employeeId);    
                                return {...obj, ...data}
                            });
                            // get the app settings for all the groupIds
                            let customGroupAppSettings=await organizationDbConnection(ehrTables.customGroupAppSettings)
                            .select('Application_Id as applicationId','Group_Id',
                            organizationDbConnection.raw('CASE WHEN Category THEN Category ELSE "Uncategorized" END as category'))
                            .whereIn('Group_Id',groupIdList)
                            
                            /** if the custom group based app settings exist then consider category based on that */
                            if(customGroupAppSettings.length>0){
                                // combine the app details with app settings data
                                customGroupArray = customGroupArray.map(obj => {
                                    let data = customGroupAppSettings.find(item => item.Group_Id === obj.Group_Id && item.applicationId===obj.applicationId);    
                                    return {...obj, ...data}
                                });
                            }
                            else{
                                console.log('No app categories exist for the custom groupIds',customGroupAppSettings);
                            }
                        }
                        // combine both organization and custom group app details
                        resultAppArray=[...orgLevelArray,...customGroupArray];
                    }
                    else{
                        resultAppArray=await getAppDetailsBasedOnInput(employeeIdArray,'orgLevel',minTime,maxTime);
                    }
                    if(resultAppArray.length>0){
                        // form the input params
                        let insertAppParams = resultAppArray.map(async field=>({
                            Employee_Id:field.employeeId,
                            App_Summarization_Id:id,
                            WorkSchedule_Start_Time:minTime,
                            WorkSchedule_End_Time:maxTime,
                            Application_Id:field.applicationId,
                            Application_Name:field.applicationName,
                            Category:(field.category)?(field.category):'Uncategorized',
                            Total_Activity_Duration:field.duration,
                            Total_Activity_Duration_In_Minutes:await Math.round(parseInt(field.duration)/60),
                            Total_Activity_Duration_In_HHMMSS:await getTimeInHoursMinutesSeconds(field.duration),
                            Activity_Date:date,
                            Load_TimeStamp:updatedOn
                        }));
                        insertAppParams = await Promise.all(insertAppParams);
                        return (
                            organizationDbConnection
                            .transaction(function (trx) {
                                // insert the app summary data
                                return (
                                    organizationDbConnection(dailySummaryTable)
                                    .insert(insertAppParams)
                                    .transacting(trx)
                                    .then(async(insertSummaryData)=>{
                                        return insertSummaryData;
                                    })
                                )
                            })
                            .then(async(result) => {
                                let updateSuccessStatus=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id);
                                console.log('Success status updated in org status table in '+orgCode+' instance - ',updateSuccessStatus);
                                return (updateSuccessStatus)?(updateSuccessStatus):'';
                            })
                            .catch(async(catchError) => {
                                console.log('Error in processing app details in summarizeDataBasedOnSource function .catch block -',catchError);
                                await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Failed',id,'Error while inserting '+inputSource+' details');
                                console.log('Failed status updated in org status table in '+orgCode+' instance');
                                throw 'Error while inserting app details';
                            })
                        )
                    }
                    else{
                        console.log('No app details exist');
                        let updateSuccessWithReason=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id,'No '+inputSource+' details exist');
                        console.log('Success status updated in organization status table in '+orgCode+' instance - ',updateSuccessWithReason);
                        return (updateSuccessWithReason)?(updateSuccessWithReason):'';
                    }
                }
                catch(error){
                    console.log('Error in getting the app details',error);
                    let reason=(error==='Error in getting app details')?'Error in getting app details':'Error while inserting app details';
                    await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Failed',id,reason);
                    console.log('Failed status updated in org status table in '+orgCode+' instance');
                    throw 'Error while retrieving/inserting app details';
                }
            }
            else if(inputSource==='appTitle')
            {
                try{
                    let resultAppArrayGroupByTitle=[];
                    // check whether custom group data exist
                    if(employeeGroupData.length>0){
                        console.log('Custom group level settings exist for some employees');
                        let orgLevelArrayGroupByTitle=[];
                        let customGroupArrayGroupByTitle=[];
                        // if organization level employeeIds exist then get the app details along with category
                        if(orgLevelEmployeeIds.length>0){
                            orgLevelArrayGroupByTitle=await getAppDetailsBasedOnInputAndGroupByTitle(orgLevelEmployeeIds,'orgLevel',minTime,maxTime);
                        }

                        if(customGroupEmployees.length>0){
                            // if custom group level employeeIds exist then get the app title  details
                            customGroupArrayGroupByTitle=await getAppDetailsBasedOnInputAndGroupByTitle(customGroupEmployees,'group',minTime,maxTime);
                            // combine custom group details with the app detail
                            customGroupArrayGroupByTitle = customGroupArrayGroupByTitle.map(obj => {
                                let data = employeeGroupData.find(item => item.employeeId === obj.employeeId);    
                                return {...obj, ...data}
                            });
                            // get the app settings for all the groupIds
                            let customGroupAppSettings=await organizationDbConnection(ehrTables.customGroupAppSettings)
                            .select('Application_Id as applicationId','Group_Id',
                            organizationDbConnection.raw('CASE WHEN Category THEN Category ELSE "Uncategorized" END as category'))
                            .whereIn('Group_Id',groupIdList)
                            
                            /** if the custom group based app settings exist then consider category based on that */
                            if(customGroupAppSettings.length>0){
                                // combine the app details with app settings data
                                customGroupArrayGroupByTitle = customGroupArrayGroupByTitle.map(obj => {
                                    let data = customGroupAppSettings.find(item => item.Group_Id === obj.Group_Id && item.applicationId===obj.applicationId);    
                                    return {...obj, ...data}
                                });
                            }
                            else{
                                console.log('No app categories exist for the custom groupIds',customGroupAppSettings);
                            }
                        }
                        // combine both organization and custom group app details
                        resultAppArrayGroupByTitle=[...orgLevelArrayGroupByTitle,...customGroupArrayGroupByTitle]
                    }
                    else{
                        console.log('Only Organization level app settings exist.');
                        resultAppArrayGroupByTitle= await getAppDetailsBasedOnInputAndGroupByTitle(employeeIdArray,'orgLevel',minTime,maxTime);
                    }
        
                    if(resultAppArrayGroupByTitle.length>0){
                        // form the input params for apps group by title
                        let insertAppParamsForGroupByTitles = resultAppArrayGroupByTitle.map(async field=>({
                            Employee_Id:field.employeeId,
                            App_Title_Summarization_Id:id,
                            WorkSchedule_Start_Time:minTime,
                            WorkSchedule_End_Time:maxTime,
                            Application_Id:field.applicationId,
                            Application_Name:field.applicationName,
                            Application_Title:field.applicationTitle,
                            Category:(field.category)?(field.category):'Uncategorized',
                            Total_Activity_Duration:field.duration,
                            Total_Activity_Duration_In_Minutes:await Math.round(parseInt(field.duration)/60),
                            Total_Activity_Duration_In_HHMMSS:await getTimeInHoursMinutesSeconds(field.duration),
                            Activity_Date:date,
                            Load_TimeStamp:updatedOn
                        }));
                        insertAppParamsForGroupByTitles = await Promise.all(insertAppParamsForGroupByTitles);
                                             
                        return (
                            organizationDbConnection
                            .transaction(function (trx) {
                                // insert the app summary data
                                return (
                                    organizationDbConnection(dailySummaryTable)
                                    .insert(insertAppParamsForGroupByTitles)
                                    .transacting(trx)
                                    .then(async(insertSummaryData)=>{
                                        return insertSummaryData;
                                    })
                                )
                            })
                            .then(async(result) => {
                                console.log('App titles summary data inserted successfully.',result);
                                let updateSuccessStatus=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id);
                                console.log('Success status updated in org status table in '+orgCode+' instance - ',updateSuccessStatus);
                                return (updateSuccessStatus)?(updateSuccessStatus):'';
                            })
                            .catch(async(catchError) => {
                                console.log('Error in processing app title details in summarizeDataBasedOnSource function .catch block -',catchError);
                                await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Failed',id,'Error while inserting '+inputSource+' details');
                                console.log('Failed status updated in org status table in '+orgCode+' instance');
                                throw 'Error while inserting app title details';
                            })
                        )
                    }
                    else{
                        console.log('No app title details exist');
                        let updateSuccessWithReason=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id,'No '+inputSource+' details exist');
                        console.log('Success status updated in organization status table in '+orgCode+' instance - ',updateSuccessWithReason);
                        return (updateSuccessWithReason)?(updateSuccessWithReason):'';
                    }
                }
                catch(error){
                    console.log('Error in getting the app title details',error);
                    let reason=(error==='Error in getting app title details')?'Error in getting app title details':'Error while inserting app title details';
                    await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Failed',id,reason);
                    console.log('Failed status updated in org status table in '+orgCode+' instance');
                    throw 'Error while retrieving/inserting app title details';
                }
            }
            else{
                /** input source will be url **/
                try{
                    let resultUrlArray=[];
                    // check whether custom group data exist
                    if(employeeGroupData.length>0){
                        let orgLevelArray=[];
                        let customGroupArray=[];
                        // if organization level employeeIds exist then get the url details along with category
                        if(orgLevelEmployeeIds.length>0){
                            console.log('Both organization level and custom group settings exist');
                            orgLevelArray=await getUrlDetailsBasedOnInput(orgLevelEmployeeIds,'orgLevel',minTime,maxTime);
                        }
                        if(customGroupEmployees.length>0){
                            console.log('Custom group settings exists');
                            // if custom group level employeeIds exist then get the url details
                            customGroupArray=await getUrlDetailsBasedOnInput(customGroupEmployees,'group',minTime,maxTime);
                            
                            // combine custom group details with the url details
                            customGroupArray = customGroupArray.map(obj => {
                                let data = employeeGroupData.find(item => item.employeeId === obj.employeeId);    
                                return {...obj, ...data}
                            });

                            // get the domain settings for all the groupIds
                            let customGroupUrlSettings=await organizationDbConnection(ehrTables.customGroupUrlSettings)
                            .select('Domain_Id as domainId','Group_Id',
                            organizationDbConnection.raw('CASE WHEN Category THEN Category ELSE "Uncategorized" END as category'))
                            .whereIn('Group_Id',groupIdList)
                            
                            if(customGroupUrlSettings.length>0){
                                // combine the domain details with domain settings data
                                customGroupArray = customGroupArray.map(obj => {
                                    let data = customGroupUrlSettings.find(item => item.Group_Id === obj.Group_Id && item.domainId===obj.domainId);    
                                    return {...obj, ...data}
                                });
                            }
                            else{
                                console.log('No Url categories exist for the custom groupIds',customGroupUrlSettings);
                            }
                        }
                        // combine both organization and custom group url details
                        resultUrlArray=[...orgLevelArray,...customGroupArray];

                        if(orgCode === 'globalfpo'){
                            console.log('url summary details for ', orgCode);
                            console.log("employeeGroupData ", employeeGroupData, customGroupEmployees);
                            console.log("orgLevelArray ", orgLevelArray);
                            console.log("customGroupArray", customGroupArray);
                        }
                    }
                    else{
                        console.log('Only Organization level url settings exist.');
                        resultUrlArray=await getUrlDetailsBasedOnInput(employeeIdArray,'orgLevel',minTime,maxTime);
                    }
        
                    if(resultUrlArray.length>0){
                        // form the input params
                        let insertDomainParams = resultUrlArray.map(async field=>({
                            Employee_Id:field.employeeId,
                            Url_Summarization_Id:id,
                            WorkSchedule_Start_Time:minTime,
                            WorkSchedule_End_Time:maxTime,
                            Domain_Id:field.domainId,
                            Domain_Name:field.domainName,
                            Category:(field.category)?(field.category):'Uncategorized',
                            Total_Activity_Duration:field.duration,
                            Total_Activity_Duration_In_Minutes:await Math.round(parseInt(field.duration)/60),
                            Total_Activity_Duration_In_HHMMSS:await getTimeInHoursMinutesSeconds(field.duration),
                            Activity_Date:date,
                            Load_TimeStamp:updatedOn
                        }));
                        insertDomainParams = await Promise.all(insertDomainParams);
                        
                        return (
                            organizationDbConnection
                            .transaction(function (trx) {
                                // insert the summary data
                                return (
                                    organizationDbConnection(dailySummaryTable)
                                    .insert(insertDomainParams)
                                    .transacting(trx)
                                    .then(async(insertSummaryData)=>{
                                        return insertSummaryData;
                                    })
                                )
                            })
                            .then(async(result) => {
                                console.log('Url summary data inserted successfully.',result);
                                let updateSuccessStatus=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id);
                                console.log('Success status updated in org status table in '+orgCode+' instance - ',updateSuccessStatus);
                                return (updateSuccessStatus)?(updateSuccessStatus):'';
                            })
                            .catch(async(catchError) => {
                                console.log('Error in processing url in summarizeDataBasedOnSource function .catch block -',catchError);
                                await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Failed',id,'Error while inserting '+inputSource+' details');
                                console.log('Failed status updated in org status table in '+orgCode+' instance');
                                throw 'Error while inserting url details';
                            })
                        )
                    }
                    else{
                        console.log('No url details exist');
                        let updateSuccessWithReason=await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Success',id,'No '+inputSource+' details exist');
                        console.log('Success status updated in organization status table in '+orgCode+' instance - ',updateSuccessWithReason);
                        return (updateSuccessWithReason)?(updateSuccessWithReason):'';
                    }
                }
                catch(error){
                    console.log('Error in getting the url details',error);
                    let reason=(error==='Error in getting url details')?'Error in getting url details':'Error while inserting url details';
                    await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Failed',id,reason);
                    console.log('Failed status updated in org status table in '+orgCode+' instance');
                    throw 'Error while inserting url details';
                }
            }
        }
    }
    catch(error){
        console.log('Error in summarizeDataBasedOnSource function main catch block -',error);
        let errorMessages=['Error while inserting activity details','Error while retrieving activity details','Error while calculating check in and check out time'];
        let failureReason=errorMessages.includes(error)?error:'Error while summarizing '+inputSource+' details';
        await updateSummaryStatusBasedOnId(organizationDbConnection,summarizationStatusTable,orgCode,inputSource,'Failed',id,failureReason);
        console.log('Failed status updated in org status table in '+orgCode+' instance');
        throw 'Error while summarizing '+inputSource+' details.Need to update in status table.';
    }
}

// function to update failed status in both organization and manager table
async function updateFailedStatus(reason){
    try{
        let updateParams={
            Transaction_Status:'Failed',
            Execution_Status:'Completed',
            Reason:(reason)?(reason):'Error in summarization process'
        }
        await updateStatusInOrganizationStatusTable(updateParams,summarizationStatusTable,orgCode,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,['Executing']);                              
        console.log('Failed status updated in org status table in '+orgCode+' instance');

        let failedParams={
            Status:"Failed"
        }
        await updateSummaryDataInMasterTable('',failedParams,orgCode,masterTable,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
        console.log('Failed status updated in manager table for '+orgCode+' instance');
        return 'success';
    }
    catch(error){
        console.log('Error in updateFailedStatus function main catch block -',error);
        return 'error';
    }
}

// function to get the app details based on input coverage and group by applicationid
async function getAppDetailsBasedOnInput(empIdArray,coverage,minTime,maxTime){
    try{
        let appSubQuery=organizationDbConnection(ehrTables.empActivityApps+ ' as EAA')
        .select('EAD.Employee_Id as employeeId','EAA.Application_Id as applicationId','AAM.Application_Name as applicationName','EAA.Time_Spent','EAA.Employee_Activity_Id','EAD.Employee_Id',
        organizationDbConnection.raw('SUM(EAA.Time_Spent)as duration'))
        .innerJoin(ehrTables.empActivityDetails+ ' as EAD','EAA.Employee_Activity_Id','EAD.Employee_Activity_Id')
        .innerJoin(ehrTables.activityAppMaster+ ' as AAM','EAA.Application_Id','AAM.Application_Id')
        .where('EAD.Activity_Start_Date_Time', '>=', minTime)
        .where(organizationDbConnection.raw("DATE_FORMAT(EAD.Activity_End_Date_Time, '%Y-%m-%d %H:%i')"), '<=', maxTime)
        .whereIn('EAD.Employee_Id', empIdArray)
        .groupBy('EAA.Application_Id','EAD.Employee_Id')
        
        // if the coverage is orglevel then get the category from org level settings
        if(coverage==='orgLevel'){
            appSubQuery=appSubQuery
            .select(organizationDbConnection.raw('CASE WHEN AS.Category THEN AS.Category ELSE "Uncategorized" END as category'))
            .leftJoin(ehrTables.organizationLevelAppSettings+ ' as AS','EAA.Application_Id','AS.Application_Id')
        }

        return(
            await appSubQuery
            .then(getApplicationDetails=>{
                return (getApplicationDetails.length>0)?getApplicationDetails:[];
            })
            .catch(catchError => {
                console.log('Error in getAppDetailsBasedOnInput function .catch block.',catchError);
                throw 'Error in getting app details';
            })
        )
    }
    catch(error){
        console.log('Error in getAppDetailsBasedOnInput function main catch block.',error);
        throw 'Error in getting app details';
    }
}

// function to get the app details based on input coverage and group by app title
async function getAppDetailsBasedOnInputAndGroupByTitle(empIdArray,coverage,minTime,maxTime){
    try{
        let appSubQuery=organizationDbConnection(ehrTables.empActivityApps+ ' as EAA')
        .select('EAD.Employee_Id as employeeId','EAA.Application_Id as applicationId','AAM.Application_Name as applicationName','EAA.App_Title as applicationTitle','EAA.Time_Spent','EAA.Employee_Activity_Id','EAD.Employee_Id',
        organizationDbConnection.raw('SUM(EAA.Time_Spent)as duration'))
        .innerJoin(ehrTables.empActivityDetails+ ' as EAD','EAA.Employee_Activity_Id','EAD.Employee_Activity_Id')
        .innerJoin(ehrTables.activityAppMaster+ ' as AAM','EAA.Application_Id','AAM.Application_Id')
        .where('EAD.Activity_Start_Date_Time', '>=', minTime)
        .where(organizationDbConnection.raw("DATE_FORMAT(EAD.Activity_End_Date_Time, '%Y-%m-%d %H:%i')"), '<=', maxTime)
        .whereIn('EAD.Employee_Id', empIdArray)
        .whereNotNull('EAA.App_Title')
        .groupBy('EAA.Application_Id','EAA.App_Title','EAD.Employee_Id')
        
        
        // if the coverage is orglevel then get the category from org level settings
        if(coverage==='orgLevel'){
            appSubQuery=appSubQuery
            .select(organizationDbConnection.raw('CASE WHEN AS.Category THEN AS.Category ELSE "Uncategorized" END as category'))
            .leftJoin(ehrTables.organizationLevelAppSettings+ ' as AS','EAA.Application_Id','AS.Application_Id')
        }

        return(
            await appSubQuery
            .then(getApplicationDetails=>{
                return (getApplicationDetails.length>0)?getApplicationDetails:[];
            })
            .catch(catchError => {
                console.log('Error in getAppDetailsBasedOnInputAndGroupByTitle function .catch block.',catchError);
                throw 'Error in getting app details';
            })
        )
    }
    catch(error){
        console.log('Error in getAppDetailsBasedOnInputAndGroupByTitle function main catch block.',error);
        throw 'Error in getting app details';
    }
}

// function to get the url details based on input coverage
async function getUrlDetailsBasedOnInput(empIdArray,coverage,minTime,maxTime){
    try{
        let domainSubQuery=organizationDbConnection(ehrTables.empActivityUrls+ ' as EAU')
        .select('EAD.Employee_Id as employeeId','EAU.Domain_Id as domainId','ADM.Domain_Name as domainName',
        organizationDbConnection.raw('SUM(EAU.Time_Spent)as duration'))
        .innerJoin(ehrTables.empActivityDetails+ ' as EAD','EAU.Employee_Activity_Id','EAD.Employee_Activity_Id')
        .innerJoin(ehrTables.activityDomainMaster+ ' as ADM','EAU.Domain_Id','ADM.Domain_Id')
        .where('EAD.Activity_Start_Date_Time', '>=', minTime)
        .where(organizationDbConnection.raw("DATE_FORMAT(EAD.Activity_End_Date_Time, '%Y-%m-%d %H:%i')"), '<=', maxTime)
        .whereIn('EAD.Employee_Id', empIdArray)
        .groupBy('EAU.Domain_Id','EAD.Employee_Id')
        
        // if the coverage is orglevel then get the category from org level settings
        if(coverage==='orgLevel'){
            domainSubQuery=domainSubQuery
            .select(organizationDbConnection.raw('CASE WHEN US.Category THEN US.Category ELSE "Uncategorized" END as category'))
            .leftJoin(ehrTables.organizationLevelUrlSettings+ ' as US','EAU.Domain_Id','US.Domain_Id')
        }

        return(
            await domainSubQuery
            .then(getDomainDetails=>{
                return (getDomainDetails.length>0)?getDomainDetails:[];
            })
            .catch(catchError => {
                console.log('Error in getUrlDetailsBasedOnInput function .catch block.',catchError);
                throw 'Error in getting url details';
            })
        )
    }
    catch(error){
        console.log('Error in getUrlDetailsBasedOnInput function main catch block.',error);
        throw 'Error in getting url details';
    }
}

async function processOrganizationUrlDomainWhitelisting(organizationDbConnection){
    try{
        console.log('Inside processOrganizationUrlDomainWhitelisting function');
        let domainIds = [];

        return(
            organizationDbConnection(ehrTables.organizationLevelUrlSettings+' as A')
            .select('A.Domain_Id','C.Category','C.Updated_By as Added_By')
            .innerJoin(ehrTables.activityDomainMaster+' as B','A.Domain_Id','B.Domain_Id')
            .innerJoin(ehrTables.organizationLevelUrlDomainWhiteListing+' as C', function(){
                this.on('B.Domain_Name', 'like', organizationDbConnection.raw("CONCAT('%', C.Whitelisted_Domain)"))
            })
            .groupBy('B.Domain_Id')
            .then(async(getData) =>{
                if(getData.length>0){
                    domainIds = getData.map(row => row.Domain_Id);
                    await deleteOrganizationUrlSettings(organizationDbConnection, domainIds);    
                }
                await insertOrganizationUrlSettings(organizationDbConnection);
            }).catch(function (catchError) {
                console.log('Error in processOrganizationUrlDomainWhitelisting function .catch block', catchError);
            })
        );
    } catch(err){
        console.log('Error in processOrganizationUrlDomainWhitelisting catch block', err);
    }
}
async function processOrganizationBlockedDomains(organizationDbConnection){
    try {
        console.log('Inside processOrganizationBlockedDomains function');

        organizationDbConnection(ehrTables.activityDomainMaster + ' as B')
        .select('B.Domain_Id', 'C.Added_By','C.Added_On','B.Domain_Name','C.WC_Blocked_Domains_Id')
        .innerJoin(ehrTables.organizationLevelWcBlockedDomains + ' as C', function () {
            this.on('B.Domain_Name', 'like', organizationDbConnection.raw("CONCAT('%', C.WC_Blocked_Domain)"))
        })
        .leftJoin(ehrTables.organizationLevelDomainsTobeBlocked + ' as A', function () {
            this.on('B.Domain_Id', '=', 'A.Domain_Id');
        })
        .whereNull('A.Domain_Id')
        .then(async (getData) => {
            if(getData && getData.length>0){
            let insertParams=getData.map((item)=>{
                return {
                    Domain_Id: item.Domain_Id,
                    WC_Blocked_Domains_Id: item.WC_Blocked_Domains_Id,
                    Added_For_WC_Conf: 1,
                    Added_By:item.Added_By,
                    Added_On:item.Added_On

                }
            })
            await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.organizationLevelDomainsTobeBlocked,insertParams,0); 
        }})
            .catch(function (catchError) {
                console.log('Error in processOrganizationBlockedDomains function .catch block', catchError);
            });
    } catch (err) {
        console.log('Error in processOrganizationBlockedDomains catch block', err);
    }
}
async function processEmployeeBlockedDomains(organizationDbConnection){
    try {
        console.log('Inside processOrganizationBlockedDomains function');

        organizationDbConnection(ehrTables.activityDomainMaster + ' as B')
        .select('B.Domain_Id', 'C.Added_By','C.Added_On','B.Domain_Name','C.WC_Blocked_Domains_Id','C.Employee_Id')
        .innerJoin(ehrTables.employeeLevelWcBlockedDomains + ' as C', function () {
            this.on('B.Domain_Name', 'like', organizationDbConnection.raw("CONCAT('%', C.WC_Blocked_Domain)"))
        })
        .leftJoin(ehrTables.employeeLevelDomainsTobeBlocked + ' as A', function () {
            this.on('B.Domain_Id', '=', 'A.Domain_Id');
        })        
        .whereNull('A.Domain_Id')
        .then(async (getData) => {
            if(getData && getData.length>0){
                let currentDateTime=moment().utc().format('YYYY-MM-DD HH:mm:ss');
            let insertParams=getData.map((item)=>{
                return {
                    Domain_Id: item.Domain_Id,
                    WC_Blocked_Domains_Id: item.WC_Blocked_Domains_Id,
                    Employee_Id:item.Employee_Id,
                    Added_For_WC_Conf: 1,
                    Added_By:item.Added_By,
                    Added_On:currentDateTime

                }
            })
            await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.employeeLevelDomainsTobeBlocked,insertParams,0); 
    }})
            .catch(function (catchError) {
                console.log('Error in processEmployeeBlockedDomains function .catch block', catchError);
            });
    } catch (err) {
        console.log('Error in processEmployeeBlockedDomains catch block', err);
    }
}


async function deleteOrganizationUrlSettings(organizationDbConnection, domainIds){
    try {
        console.log('Inside deleteOrganizationUrlSettings function');
        return(
            organizationDbConnection(ehrTables.organizationLevelUrlSettings)
            .del()
            .whereIn('Domain_Id', domainIds)
            .then(async() =>{
                return true;
            }).catch(function (catchError) {
                console.log('Error in deleteOrganizationUrlSettings function .catch block', catchError);  
            })
        );
    } catch (error) {
        console.log('Error in deleteOrganizationUrlSettings catch block', err);
    }
}

async function insertOrganizationUrlSettings(organizationDbConnection) {
    try{
        console.log("Inside insertOrganizationUrlSettings function", orgCode);
        let currentDateTime=moment().utc().format('YYYY-MM-DD HH:mm:ss');
        return(
            organizationDbConnection(ehrTables.organizationLevelUrlDomainWhiteListing+' as A')
                .select('B.Domain_Id','A.Category','A.Updated_By as Added_By')
                .innerJoin(ehrTables.activityDomainMaster+' as B', function(){
                    // this.on('A.Domain_Id = B.Domain_Id')
                    this.on('B.Domain_Name', 'like', organizationDbConnection.raw("CONCAT('%', A.Whitelisted_Domain, '%')"))
                })
                .groupBy('B.Domain_Id')
                .then(async(getSettingsData) =>{
                    if(getSettingsData.length>0){
                        let insertParams = getSettingsData.map(field => {
                            const params = {
                                Domain_Id: field.Domain_Id,
                                Category: field.Category,
                                Added_By: field.Added_By,
                                Added_On: currentDateTime
                            };
                            return params;
                        });
                        await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.organizationLevelUrlSettings,insertParams,0); 
                    }
                }).catch(function (catchError) {
                    console.log('Error in insertOrganizationUrlSettings function .catch block', catchError);
                })
        );

    } catch(err){
        console.log('Error in insertOrganizationUrlSettings function .catch block', err);
    }
}

async function processCustomGroupUrlDomainWhitelisting(organizationDbConnection){
    try{
        console.log('Inside processCustomGroupUrlDomainWhitelisting function');
        let domainIds = [];

        return(
            organizationDbConnection(ehrTables.customGroupUrlSettings+' as A')
            .select('A.Domain_Id','A.Group_Id')
            .innerJoin(ehrTables.activityDomainMaster+' as B','A.Domain_Id','B.Domain_Id')
            .innerJoin(ehrTables.customGroupUrlDomainWhiteListing+' as C', function(){
                this.on('B.Domain_Name', 'like', organizationDbConnection.raw("CONCAT('%', C.Whitelisted_Domain)"))
                this.on('A.Group_Id',"C.Custom_Group_Id")
            })
            .groupBy('A.Domain_Id','A.Group_Id')
            .then(async(getData) =>{
                if(getData.length>0){
                    // domainIds = getData.map(row => row.Domain_Id);
                    await deleteCustomGroupUrlSettings(organizationDbConnection, getData);    
                }
                await insertCustomGroupUrlSettings(organizationDbConnection);
            }).catch(function (catchError) {
                console.log('Error in processCustomGroupUrlDomainWhitelisting function .catch block', catchError);
            })
        );
    } catch(err){
        console.log('Error in processCustomGroupUrlDomainWhitelisting catch block', err);
    }
}

async function deleteCustomGroupUrlSettings(organizationDbConnection, inputData){
    try {
        console.log('Inside deleteCustomGroupUrlSettings function');
        return(
            organizationDbConnection(ehrTables.customGroupUrlSettings)
            .del()
            .whereIn(['Domain_Id', 'Group_Id'], inputData.map(({ Domain_Id, Group_Id }) => [Domain_Id, Group_Id]))
            .then(async() =>{
                return true;
            }).catch(function (catchError) {
                console.log('Error in deleteCustomGroupUrlSettings function .catch block', catchError);  
            })
        );
    } catch (error) {
        console.log('Error in deleteCustomGroupUrlSettings catch block', err);
    }
}

async function insertCustomGroupUrlSettings(organizationDbConnection) {
    try{
        console.log("Inside insertCustomGroupUrlSettings function", orgCode);
        let currentDateTime=moment().utc().format('YYYY-MM-DD HH:mm:ss');
        return(
            organizationDbConnection(ehrTables.customGroupUrlDomainWhiteListing+' as A')
                .select('B.Domain_Id','A.Category','A.Custom_Group_Id','A.Updated_By as Added_By')
                .innerJoin(ehrTables.activityDomainMaster+' as B', function(){
                    this.on('B.Domain_Name', 'like', organizationDbConnection.raw("CONCAT('%', A.Whitelisted_Domain, '%')"))
                })
                .groupBy('B.Domain_Id','A.Custom_Group_Id')
                .then(async(getSettingsData) =>{
                    if(getSettingsData.length>0){
                        let insertParams = getSettingsData.map(field => {
                            const params = {
                                Domain_Id: field.Domain_Id,
                                Group_Id: field.Custom_Group_Id,
                                Category: field.Category,
                                Added_By: field.Added_By,
                                Added_On: currentDateTime
                            };
                            return params;
                        });
                        await commonLib.func.insertIntoTable(organizationDbConnection,ehrTables.customGroupUrlSettings,insertParams,0); 
                    }
                }).catch(function (catchError) {
                    console.log('Error in insertCustomGroupUrlSettings function .catch block', catchError);
                })
        );

    } catch(err){
        console.log('Error in insertCustomGroupUrlSettings function .catch block', err);
    }
}

async function migrateToAttendanceMaster(organizationDbConnection, summaryDetailsArray, minDateTimeByZone, maxDateTimeByZone, orgCode){
    try{
        let pushAttendanceToHRMS = await organizationDbConnection(ehrTables.employeeMonitorSettings)
        .select('Push_Attendance_To_Hrms')
        .then(async(settings) =>{
            return settings && settings.length > 0 ? settings[0].Push_Attendance_To_Hrms : "No";
        }).catch(function (catchError) {
            console.log('Error while getting the employee monitor settings .catch block', catchError);
            return "No";
        });

        if(pushAttendanceToHRMS.toLowerCase() === "yes"){
            //Get the attendance which are already exist
            let attendanceData = await organizationDbConnection(ehrTables.attendance)
                    .select('Attendance_Id','Employee_Id')
                    .whereNot('Approval_Status', 'Rejected')
                    .where(qb => {
                        qb.whereBetween(organizationDbConnection.raw('CONCAT(PunchIn_Date, " ", PunchIn_Time)'), [minDateTimeByZone, maxDateTimeByZone])
                        qb.orWhereBetween(organizationDbConnection.raw('CONCAT(PunchOut_Date, " ", PunchOut_Time)'), [minDateTimeByZone, maxDateTimeByZone])
                    })
                    .then(async(attendanceData) =>{
                        return attendanceData;
                    }).catch(function (catchError) {
                        console.log('Error while getting the attendance data .catch block', catchError);
                    })
                
            let employeeIdsToExclude = [];

            if(attendanceData && attendanceData.length>0){
                employeeIdsToExclude = attendanceData.map(employee=>employee.Employee_Id);
            }

            // Filter data array to remove entries with the same EmployeeId as in data2 array
            let filteredData = summaryDetailsArray.filter(item => !employeeIdsToExclude.includes(item.Employee_Id));

            let updateBiometricIntegrationId = await organizationDbConnection(ehrTables.empJob)
            .update({ External_EmpId: organizationDbConnection.raw('Employee_Id') })
            .whereNull('External_EmpId')
            .orWhere('External_EmpId', '')
            .then(async(updateResult) =>{
                return updateResult;
            }).catch(function (catchError) {
                console.log('Error while updating the biometric integration id catch block', catchError);
            });

            let biometricIntegrationId = await organizationDbConnection(ehrTables.empJob)
            .select('External_EmpId','Employee_Id')
            .whereNotNull('External_EmpId')
            .whereNot('External_EmpId', '')
            .then(async(biometricIdResult) =>{
                let biometricIds = {};
                if(biometricIdResult && biometricIdResult.length > 0){
                    biometricIdResult.forEach(item => {
                        biometricIds[parseInt(item.Employee_Id)] = item.External_EmpId;
                    });
                }
                return biometricIds;
            }).catch(function (catchError) {
                console.log('Error while getting the attendance data .catch block', catchError);
            });

            let attendanceImportData = [];
            let attendanceImData = {};
            if(filteredData.length){
                for(let i=0; i<filteredData.length; i++){
                    if(biometricIntegrationId[filteredData[i].Employee_Id]){
                        if(filteredData[i].Activity_Start_Hour){
                            attendanceImData = {
                                MachineId: "Auto Migration",
                                EmployeeId:biometricIntegrationId[filteredData[i].Employee_Id],
                                PunchTime:filteredData[i].Activity_Start_Hour,
                                Status:91,
                                id: 1
                            }
                            attendanceImportData.push(attendanceImData);
                        }
                        if(filteredData[i].Activity_End_Hour){
                            attendanceImData = {
                                MachineId: "Auto Migration",
                                EmployeeId:biometricIntegrationId[filteredData[i].Employee_Id],
                                PunchTime: moment(filteredData[i].Activity_End_Hour).format('YYYY-MM-DD HH:mm:00'),
                                Status:92,
                                id: 1
                            }
                            attendanceImportData.push(attendanceImData);
                        }
                    }
                }
            }
            console.log(attendanceImportData);
            
            if(attendanceImportData.length > 0){
                let importResult = await callAttendanceImportAction(orgCode, attendanceImportData);
                // let processAttendanceImport = await processAttendanceImportAction(orgCode, minDateTimeByZone.format('YYYY-MM-DD'));
            }
        }

    } catch(err){
        console.log('Error in migrateToAttendanceMaster function .catch block', err);
    }
}

async function callAttendanceImportAction(orgCode, attendanceData){
    let attendanceImportURL=process.env.attendanceImportURL;
    const config = {
        method: 'POST',
        url: "https://"+orgCode+"."+attendanceImportURL,
        data: attendanceData,
        headers: {
            'Content-Type': 'application/json'
        }
      };
    try{
        const axios = require('axios');
        let attendanceImportResponse = await Promise.resolve(axios.request(config))
        .then(response=>{
            return response.data ? true : false;
        })
        .catch(e=>{
            console.log("error occured while calling Attendance import API",e);
            return false;
        })
    } catch(err){
        console.log('Error in callAttendanceImportAction function .catch block', err);
        return false;
    }
}

async function processAttendanceImportAction(orgCode, date){
    let attendanceImportProcessing=process.env.attendanceImportProcessing;
    const config = {
        method: 'POST',
        url: "https://"+orgCode+"."+attendanceImportProcessing,
        data: {"Process_Method": 2,
            "Process_From": date,
            "Process_To": date},
        headers: {
            'Content-Type': 'application/json'
        }
      };
    try{
        const axios = require('axios');
        let attendanceImportResponse = await Promise.resolve(axios.request(config))
        .then(response=>{
            return response.data ? true : false;
        })
        .catch(e=>{
            console.log("error occured while calling Attendance import processing API",e);
            return false;
        })
    } catch(err){
        console.log('Error in processAttendanceImportAction function .catch block', err);
        return false;
    }
}