'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const commonFunction = require('../common/initiateStepFunction');

// Function to initiate AppUrlPurgeStepFunction step function
module.exports.initiateAppUrlPurgeStepFunction  = async(event, context) =>{
    try{
        console.log('Inside initiateAppUrlPurgeStepFunction function',event);
        // based on event define the status
        // We will be triggering the step function in 2 different cases.
        let status='';
        if(event.status==='Open')
        {
            status='Open';
        }
        // based on status we will check weither to update the table for app and url purge in manage db.
        let triggerAppUrlPurge= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'appUrl',status);
        console.log('Response after triggering AppUrlPurgeStepFunction step function',triggerAppUrlPurge);


        return {errorCode:'',message: 'initiateAppUrlPurgeStepFunction initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateAppUrlPurgeStepFunction function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EM0061');
        return {errorCode:errResult.code,message: errResult.message};
    }
};