'use strict';
// require knex for database connection
const knex = require('knex');
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { defaultValues } = require('../common/appConstants');
// require common function
const { updateStatusInOrganizationStatusTable,updateSummaryDataInMasterTable,triggerSummarizationLambda }=require('./commonFunctions');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let summarizationStatusTable;
let masterTable;
let inputSource;
let inputStatus;
let appmanagerDbConnection='';
let orgCode='';

// function to get the summarization records based on condition process the daily summary activities
module.exports.processAppUrlActivitySummary  = async(event, context) =>{
    try{
        console.log('Inside processAppUrlActivitySummary function',event);
        // make app manager database connection
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether connection exist or not
        if(Object.keys(databaseConnection).length){
            // get input data
            let inputData=event.input;
            inputSource=inputData.process;
            inputStatus=inputData.status;
            // Same logic is used for different input source so we define the table names
            if(inputSource==='app'){
                summarizationStatusTable=ehrTables.appActivitySummarizationStatus;
                masterTable=appManagerTables.appActivitySummarizationManager;
            }
            else if(inputSource==='url'){
                summarizationStatusTable=ehrTables.urlActivitySummarizationStatus;
                masterTable=appManagerTables.urlActivitySummarizationManager;
            }
            else if(inputSource==='appTitle')
            {
                summarizationStatusTable=ehrTables.appTitleActivitySummarizationStatus;
                masterTable=appManagerTables.appTitleActivitySummarizationManager;  
            }
            else{
                summarizationStatusTable=ehrTables.employeeActivitySummarizationStatus;
                masterTable=appManagerTables.employeeActivitySummarizationManager;
            };
            // get app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            return(
                // get all the records with Open status from manager table
                appmanagerDbConnection(masterTable)
                .select('*')
                .where('Status','Open')
                .then(async (summaryData) =>{
                    // check data exist or not
                    if (summaryData.length>0){
                        // formation of params to update the inprogress status
                        let updateParams={
                            Transaction_Status : 'Inprogress',
                            Execution_Status:'Executing',
                            Summarization_Time:new Date()
                        }
                        // we are making async call for summarization process so here we are limiting the number of database connection
                        // based on condition form the number of instance to be processed
                        let instanceToBeProcessed=(summaryData.length>defaultValues.empMonitorUserCount)?(defaultValues.empMonitorUserCount):(summaryData.length);
                        // intially we are limiting the 20 user at the particular time
                        // number of records need to be processed configured as env variable - based on which event trigger time varies
                        for(let i = 0; i <instanceToBeProcessed; i++){
                            orgCode=summaryData[i].Org_Code;

                            // update the inprogress status in organization status table
                            let updateStatus= await updateStatusInOrganizationStatusTable(updateParams,summarizationStatusTable,orgCode,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,['Started'])                                
                            console.log('Update inprogress status in organization table for '+orgCode+' instance',updateStatus);

                            if(updateStatus){
                                let updateParams={
                                    Status : 'Inprogress'
                                }
                                // function to update inprogress status in manager table
                                let updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,masterTable,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                                if(updateMasterTableStatus){
                                    console.log('Update inprogress status in manager table for '+orgCode+' instance',updateMasterTableStatus);
                                    // formation of payload
                                    let payLoad={
                                        process:inputSource,
                                        status:inputStatus,
                                        orgCode:orgCode
                                    }
                                    const params = {
                                        FunctionName: process.env.summarizationProcessTrigger,
                                        InvocationType: "RequestResponse",
                                        Payload: JSON.stringify(payLoad)
                                    };
                                    // Invoke lambda to summarize the daily activities
                                    await triggerSummarizationLambda(params,orgCode);
                                }
                                else{
                                    console.log('Error while updating inprogress status in manager table for '+orgCode+' instance');
                                }
                            }
                            else{
                                console.log('Error in updating inprogress status in organization table for '+ orgCode +' instance');
                                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                                let response = 
                                {
                                    nextStep:'Error',
                                    input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error in updating status in organization table.'},
                                    message:'Error in step 2 .catch block'
                                };
                                return response;
                            }
                        }
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'process':inputSource,'status':inputStatus},
                            message: inputSource+' summarization process completed'
                        };
                        console.log(inputSource+' summarization process completed');
                        return response;
                    }
                    else{
                        console.log('No open record found in manager table. So quit the execution');
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'process':inputSource,'status':inputStatus},
                            message:'No open '+ inputSource +' records found.'
                        };
                        return response;
                    }
                })
                .catch(catchError=>{
                    console.log('Error in processAppUrlActivitySummary .catch block.', catchError);
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'Error',
                        input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error from step2'},
                        message:'Error in step 2 .catch block'
                    };
                    return response;
                })
            );
        }
        else{
            console.log('Error while creating app manager database connection in step2');
            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
            let response = 
            {
                nextStep:'Error',
                input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error creating app manager database connection from step2'},
                message:'Error in creating app manager database connection in step2.'
            };
            return response;
        }
    } catch (mainCatchError){
        console.log('Error in processAppUrlActivitySummary function main catch block.', mainCatchError);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response = 
        {
            nextStep:'Error',
            input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error from step2 main catch block.'},
            message:'Error in step 2 main catch block'
        };
        return response;
    }
};
