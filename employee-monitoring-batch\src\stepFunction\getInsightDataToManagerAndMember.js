'use strict';
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables } = require('../common/tableAlias');
// require common function
const commonFunction=require('./commonFunctions');
const {getTopFiveAppAndDomain}=require('../common/activityCommonFunctions');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let appManagerDbConnection='';
let orgDbConnection='';
let outputResponse={
    workedEmployeeCount:'',
    notWorkedEmployeeCount:'',
    activityData:[],
    lowActivity:[],
    highActivity:[],
    appData:[],
    urlData:[],
    activityDate:''
};

// function to send notification for managers
module.exports.getInsightDataToManagerAndMember  = async(event, context) =>{
    let source='';
    try{
        let orgCode='';
        let activityDate='';
        // variable declarations
        if(event.input){
            source=event.input.process;
            orgCode=event.input.orgCode;
            activityDate=event.input.activityDate;
        }
        else{
            let response = 
            {
                nextStep:'End',
                input:{'process':source},
                message:'Invalid input in step4'
            };
            return response;
        }
        
        let getAppDbConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // get app manager database connection
        appManagerDbConnection=knex(getAppDbConnection.AppManagerDb);

        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection,orgCode);

        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
              
            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
            if(Object.keys(databaseConnection).length){
                orgDbConnection=knex(databaseConnection.OrganizationDb);
                // check whether record with Open status
                return(
                    orgDbConnection
                    .pluck('Employee_Id')
                    .from(ehrTables.insightsNotificationStatusLog)
                    .where('Status','Open')
                    .then(async(getRecord)=>{
                        if(getRecord.length>0){
                            // Iterate for all the records
                            for(let i=0;i<getRecord.length;i++){
                                // update the initiated status in status log table
                                let updateParams={
                                    Status:'Initiated',
                                    Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
                                }
                                let updateStatus=await commonFunction.updateNotificationStatusLog(orgDbConnection,updateParams,[getRecord[i]]);
                                console.log('Response after updating initiated status in log table -',updateStatus);
                                let reporteesEmpId;
                                if(source==='manager')
                                {
                                    // Get the reportees employeeId
                                    reporteesEmpId=await commonFunction.getEmpIdBasedOnTheirManagerId(orgDbConnection,getRecord[i]);
                                }
                                else{
                                    reporteesEmpId=[getRecord[i]];
                                }
                                if(reporteesEmpId.length>0)
                                {
                                    // check whether summary data exist for the reportees
                                    let getSummaryData=await checkSummaryRecord(orgDbConnection,activityDate,reporteesEmpId);
                                    if(getSummaryData.length>0){
                                        outputResponse['activityDate']=activityDate;
                                        
                                        if(source==='manager'){
                                            let workedEmployeeCount=getSummaryData.length;
                                            let totalEmployeeCount=reporteesEmpId.length;
                                            // calculate the employee not worked count for presenting in template
                                            outputResponse['workedEmployeeCount']=workedEmployeeCount;
                                            outputResponse['notWorkedEmployeeCount']=(totalEmployeeCount)-(workedEmployeeCount);
                                            outputResponse['activityData']=await commonFunction.formActivityData(orgDbConnection,reporteesEmpId,activityDate);
                                            let lowHighAppUrlData=await commonFunction.insightsDetails(orgDbConnection,reporteesEmpId,activityDate,source);
                                            outputResponse['lowActivity']=lowHighAppUrlData.lowActivityEmployeeDetails;
                                            outputResponse['highActivity']=lowHighAppUrlData.highActivityEmployeeDetails;
                                            outputResponse['appData']=lowHighAppUrlData.appData;
                                            outputResponse['urlData']=lowHighAppUrlData.urlData;
                                            outputResponse['activeEmpIdArray']=reporteesEmpId;
                                        }
                                        else{
                                            outputResponse['activityData']=await commonFunction.getAttendanceSummaryDetails(orgDbConnection,reporteesEmpId,activityDate);
                                            outputResponse['checkIn']=(getSummaryData[0].Activity_Start_Hour)?(getSummaryData[0].Activity_Start_Hour.split(' ')[1]):null;
                                            outputResponse['checkOut']=(getSummaryData[0].Activity_End_Hour)?(getSummaryData[0].Activity_End_Hour.split(' ')[1]):null;
                                            // get the app and url data
                                            let appUrlData=await getTopFiveAppAndDomain(orgDbConnection,reporteesEmpId,activityDate);
                                            outputResponse['appData']=appUrlData.appData;
                                            outputResponse['urlData']=appUrlData.urlData;
                                            outputResponse['highActivity']=await commonFunction.getHighEfficiencyDetails(orgDbConnection,activityDate);
                                        }
                                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                        orgDbConnection?orgDbConnection.destroy():null;            
                                        let response = {
                                            nextStep:'Step3',
                                            input:{'process':source,'templateData':JSON.stringify(outputResponse),'orgCode':orgCode,'employeeId':getRecord[i]},
                                            message:'Trigger email notification.'
                                        };
                                        return response;
                                    }
                                    else{
                                        console.log('Summary record not exist so process the next record');
                                        let updateParams={
                                            Status:'Success',
                                            Reason:'Activity data not exist',
                                            Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
                                        }
                                        // function to update the status in status log table
                                        let updateStatus=await commonFunction.updateNotificationStatusLog(orgDbConnection,updateParams,[getRecord[i]]);
                                        console.log('Response after updating Success status in log table -',updateStatus);
                                    }
                                }
                                else{
                                    console.log('Error in getting the reportees employeeIds');
                                    let params={
                                        status:'Failure',
                                        Reason:'No reportees to share daily insights of the team',
                                        Updated_On:moment().format('YYYY-MM-DD HH:mm:ss')
                                    }
                                    let updateStatus=await commonFunction.updateNotificationStatusLog(orgDbConnection,params,[getRecord[i]]);
                                    console.log('Response after updating failure status in log table -',updateStatus);
                                }
                            }
                            await checkRecordExistence(orgDbConnection,orgCode,appManagerDbConnection);
                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                            orgDbConnection?orgDbConnection.destroy():null;
                            let response = 
                            {
                                nextStep:'Step2',
                                input:{'process':source},
                                message:'Process the step2.'
                            };
                            return response;
                        }
                        else{
                            console.log('No open records in '+ orgCode+' so process the next instances');
                            await checkRecordExistence(orgDbConnection,orgCode,appManagerDbConnection);
                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                            orgDbConnection?orgDbConnection.destroy():null;    
                            let response = 
                            {
                                nextStep:'Step2',
                                input:{'process':source},
                                message:'No open records so process the next instances.'
                            };
                            return response;
                        }
                    })
                    .catch(catchError=>{
                        console.log('Error in getInsightDataToManagerAndMember function .catch block.', catchError);
                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                        orgDbConnection?orgDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'Step2',
                            input:{'process':source},
                            message:'Process the step2.'
                        };
                        return response;
                    })
                )
            }
            else{
                console.log('Error while making database connection');
                appManagerDbConnection ? appManagerDbConnection.destroy():null;
                orgDbConnection?orgDbConnection.destroy():null;
                let response = 
                {
                    nextStep:'End',
                    input:{'process':source},
                    message:'Error while making database connection'
                };
                return response;
            }
        } else{
            console.log('Error while getting data region');
            appManagerDbConnection ? appManagerDbConnection.destroy():null;
            orgDbConnection?orgDbConnection.destroy():null;
            let response = 
            {
                nextStep:'End',
                input:{'process':source},
                message:'Error while getting data region'
            };
            return response;
        }
    }
    catch (mainCatchError){
        console.log('Error in getInsightDataToManagerAndMember function main catch block.', mainCatchError);
        appManagerDbConnection ? appManagerDbConnection.destroy():null;
        orgDbConnection?orgDbConnection.destroy():null;
        let response = 
        {
            nextStep:'End',
            input:{'process':source},
            message:'Error from step4 main catch block'
        };
        return response;
    }
};

// function to check whether the summary record exist
async function checkSummaryRecord(orgDbConnection,activityDate,employeeIdArray){
    try{
        return(
            orgDbConnection
            .select('*')
            .from(ehrTables.employeeActivityDailySummary)
            .where('Activity_Date',activityDate)
            .whereIn('Employee_Id',employeeIdArray)
            .then(async(getSummaryData)=>{
                return getSummaryData;
            })
            .catch(catchError=>{
                console.log('Error in checkSummaryRecord function main catch block.', catchError);
                return [];
            })
        );
    }
    catch(error){
        console.log('Error in checkSummaryRecord function main catch block.', error);
        return [];
    }
}

/** check whether all the record executed in this instance or not. If executed then update the status as success */
async function checkRecordExistence(orgDbConnection,orgCode,appManagerDbConnection){
    try{
        // calculate the total employee count for which email need to be send
        let totalCount=await orgDbConnection
            .count('Employee_Id as count')
            .from(ehrTables.insightsNotificationStatusLog)
            .then(async(summaryData)=>{
                return summaryData[0].count;
            })
        let executedRecordCount=await orgDbConnection
            .count('Employee_Id as count')
            .from(ehrTables.insightsNotificationStatusLog)
            .whereIn('Status',['Success','Failure'])
            .then(async(summaryData)=>{
                return summaryData[0].count;
            })
        // If all the records are executed then update the status in manager table
        if(totalCount===executedRecordCount){
            let updateParams={
                Status:'Success',
            }
            // update the success status in manager table
            let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,orgCode);
            console.log('Response after updating success status in manager table for '+orgCode+' instance - ',updateStatus);                            
        }
        return 'success'
    }
    catch(error){
        console.log('Error in checkRecordExistence function main catch block.', error);
        return 'error'
    }
};
