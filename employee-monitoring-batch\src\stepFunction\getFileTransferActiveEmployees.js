//get common functions
const{getDataFromMasterTableAccordingToStatus,updateInMasterTable}=require("./commonFunctions");
//get tablealias
const{appManagerTables,ehrTables}=require("../common/tableAlias")
//get form}
const{formIds,formName}=require('../common/appConstants')
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// Organization database connection
const knex = require('knex');
//Require moment
const moment = require('moment-timezone');

//variable declaration
let masterTable=appManagerTables.fileTransferSummarizationManager;
let inputParams;
let currentDate;
let appmanagerDbConnection;
let organizationDbConnection;
let inputStatus;
module.exports.getFileTransferActiveEmployees  = async(event,context) =>{
    try{
        console.log('Inside getFileTransferActiveEmployees', event);
        currentDate=moment.utc().format("YYYY-MM-DD");
        // get input data
        inputStatus=event.input.status;
        if(inputStatus && inputStatus.toLowerCase()==='failed')
        {
            inputStatus='Failed'
        }
        else{
            inputStatus='Open'
        }
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether data exist or not
        if(Object.keys(databaseConnection).length){
            // form app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            let openInstances= await getDataFromMasterTableAccordingToStatus(appmanagerDbConnection,masterTable,"Employee_List_Preparation",inputStatus);
            if(openInstances)
            {  
                console.log("openInstances.length: ", openInstances.length, currentDate);
                if(openInstances.length>0)
                {
                    for(let i=0;i<openInstances.length;i++)
                    {
                        let orgCode=openInstances[i]['Org_Code'];
                        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
                        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                            
                            //Get database connection
                            let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                            if(Object.keys(connection).length>0){
                                organizationDbConnection = knex(connection.OrganizationDb);
                                let superAdmins=await getSuperAdmin(organizationDbConnection);
                                if(superAdmins)
                                {
                                    if(superAdmins.length>0)
                                    {
                                        let superAdminDLPRights=false;
                                        let startIndexSuperAdmin=0;
                                        while(!superAdminDLPRights && startIndexSuperAdmin<superAdmins.length)
                                        {
                                            // check whether super admin have access to DLP form.
                                            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,superAdmins[startIndexSuperAdmin] , formName.dataLossPrevention, '','UI');
                                            if(checkRights["Role_View"])
                                            {
                                                superAdminDLPRights=true;
                                            }
                                            startIndexSuperAdmin=startIndexSuperAdmin+1;
                                        }
                                        if(superAdminDLPRights)
                                        {
                                            let purgeResponse=await purgeEmployees(organizationDbConnection);
                                            if(purgeResponse)
                                            {
                                                let fileTransferEnabledEmployees=await getEmployeesHavingEmployeeLevelFileTransferSettingsEnabled(organizationDbConnection);
                                                if(fileTransferEnabledEmployees)
                                                {   
                                                    if(fileTransferEnabledEmployees.length>0)
                                                    {
                                                        let processEmployees=[];
                                                        for(let j=0;j<fileTransferEnabledEmployees.length;j++)
                                                        {
                                                            processEmployees.push({
                                                                "Employee_Id":fileTransferEnabledEmployees[j],
                                                                "Status":'Open',
                                                                "Summarization_Date":currentDate
                                                            })     
                                                        }
                                                        let insertEmployeesResponse=await insertEmployees(organizationDbConnection,processEmployees);
                                                        if(insertEmployeesResponse)
                                                        {   
                                                            inputParams={
                                                                Employee_List_Preparation:"Success"
                                                            }
                                                            await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);  
                                                        }
                                                        else{
                                                            inputParams={
                                                                Employee_List_Preparation:"Failed"
                                                            }
                                                            await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                                                        }
                                                    }
                                                    else{
                                                        console.log("No employees found with file transfer enabled.")
                                                        inputParams={
                                                            Employee_List_Preparation:"Success"
                                                        }
                                                        await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);   
                                                    }
                                                }
                                                else{
                                                    console.log("Error Occurred while geting fileTransferEnabledEmployees Employees");
                                                    inputParams={
                                                        Employee_List_Preparation:"Failed"
                                                    }
                                                    await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                                                } 
                                            }
                                            else{
                                                console.log("Error Occurred while deleting Employees");
                                                inputParams={
                                                    Employee_List_Preparation:"Failed"
                                                }
                                                await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                                            }
                                        }
                                        else{
                                            console.log('SuperAdmin has not access to dlp form in step2');
                                            inputParams={
                                                Employee_List_Preparation:"Success"
                                            }
                                            await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                                        }
                                    }
                                    else{
                                        console.log('No superAdmin found in step2');
                                        inputParams={
                                            Employee_List_Preparation:"Success"
                                        }
                                        await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                                    }
                                }
                                else{
                                    console.log('Error while getting superAdmin in step2');
                                    inputParams={
                                        Employee_List_Preparation:"Failed"
                                    }
                                    await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                                } 
                            }
                            else{
                                console.log('Error while creating organization database connection in step1');
                                inputParams={
                                    Employee_List_Preparation:"Failed"
                                }
                                await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                            }
                            organizationDbConnection?organizationDbConnection.destroy():null;
                        }
                        else{
                            console.log("Error while getting orgRegionDetails")
                            inputParams={
                                Employee_List_Preparation:"Failed"
                            }
                            await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                        }
                    }
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response={
                        nextStep:'Step3',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next step.'          
                    }
                    return response;
                }
                else{
                    console.log("No Open instances found.");
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response={
                        nextStep:'Step3',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next step.'          
                    }
                    return response;
                }
               
            }
            else{
                console.log("Error Occured while calling  getOpenOrgCodeFromMasterTable.");
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response ={
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Occured while calling getOpenOrgCodeFromMasterTable.'
                };
                return response;
            }
        }
        else{
            console.log('Error while creating app manager database connection in step1');
            let response ={
                nextStep:'End',
                input:{'status':inputStatus},
                message:'Error Occured creating app manager database connection.'
            };
            return response;
        }
    }
    catch(e){
        console.log("Error in getFileTransferActiveEmployees function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        organizationDbConnection?organizationDbConnection.destroy():null; 
        let response ={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Error Occured in getFileTransferActiveEmployees.'
        };
        return response;
    }
}


//function to insert inactive employees in employeeLevelFileTransferSummarizationManager table
async function insertEmployees(organizationDbConnection,insertData)
{
    try{
        return(
            organizationDbConnection(ehrTables.employeeLevelFileTransferSummarizationManager)
            .insert(insertData)
            .then(data=>{
                console.log("Employees inserted successfully.",data)
                return true;
            })
            .catch(e=>{
                console.log("Error in insertEmployees function .catch block.",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in insertEmployees function main catch block.",e);
        return false;
    }
}

//function to purge employees from employeeLevelFileTransferSummarizationManager
async function purgeEmployees(organizationDbConnection)
{
    try{
        return(
        organizationDbConnection(ehrTables.employeeLevelFileTransferSummarizationManager)
        .del()
        .then(data=>{
            return true;
        })
        .catch(e=>{
            console.log("Error in purgeEmployees function .catch block.",e);
            return false;
        })
        )
    }
    catch(e)
    {
        console.log("Error in purgeEmployees function main catch block.",e);
        return false;
    }
}

//function to get superAdmin
async function getSuperAdmin(organizationDbConnection) {
    try {
        const [rolesEmployees, empAccessEmployees] = await Promise.all([
            organizationDbConnection(ehrTables.empJob + ' as EJ')
                .pluck('EJ.Employee_Id')
                .leftJoin(ehrTables.rolesBasedAccessControl + ' as RBAC', 'RBAC.Roles_Id', 'EJ.Roles_Id')
                .where('RBAC.Form_Id', formIds.superAdmin)
                .where('RBAC.Role_Optional_Choice', 1),

            organizationDbConnection(ehrTables.empAccessRights)
                .pluck('Employee_Id')
                .where('Form_Id', formIds.superAdmin)
                .where('Role_Optional_Choice', 1)
        ])
        const employeeIdsWithoutDuplicates = [...new Set(rolesEmployees.concat(empAccessEmployees))]

        return employeeIdsWithoutDuplicates

    }
    catch (e) {
        console.log("Error in getSuperAdmin function main catch block", e);
        return false;
    }
}

//function to get Employee Id having employee_level_file_transfer_settings enabled
async function getEmployeesHavingEmployeeLevelFileTransferSettingsEnabled(organizationDbConnection)
{
    try{
        return(
        organizationDbConnection(ehrTables.employeeLevelFileTransferSettings)
        .pluck("ELFTS.Employee_Id")
        .from(ehrTables.employeeLevelFileTransferSettings + ' as ELFTS')
        .leftJoin(ehrTables.teamMembers + ' as TM','TM.Employee_Id','ELFTS.Employee_Id')
        .where("ELFTS.Enable_File_Transfer","Yes")
        .whereNotNull("TM.Employee_Id")
        .where("TM.Member_Status","Active")
        .then(data=>{
            return data;
        })
        .catch(e=>{
            console.log("Error in getEmployeesHavingEmployeeLevelFileTransferSettingsEnabled function .catch block",e);
            return false;
        })
        )
    }
    catch(e)
    {
        console.log("Error in getEmployeesHavingEmployeeLevelFileTransferSettingsEnabled function main catch block",e);
        return false;
    }
}
