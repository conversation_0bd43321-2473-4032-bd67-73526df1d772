let emailValidation = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,24}$/; /**email id validation */
let nameValidation = /^[a-zA-Z\.\ ]+$/; /**name validation */
let numberValidation = /^[0-9]+$/; /**only numbers allowed */
let firstLastNameValidation = /^[a-z0-9'.-\s]+$/i; /** Alpha numeric,space,dot,single quotes and hyphen allowed */

module.exports = {
    checkLength: function (input, minLength, maxLength) {
        return (result = (input.length < minLength || input.length > maxLength) ? false : true);
    },
    emailValidation: function (input) {
        return (result = emailValidation.test(input) ? true : false);
    },
    nameValidation: function(input) {
        return (result = nameValidation.test(input) ? true : false);
    },
    numberValidation: function(input) {
        return (result = numberValidation.test(input) ? true : false);
    },
    firstLastNameValidation: function(input) {
        return (result = firstLastNameValidation.test(input) ? true : false);
    },
    booleanNumberValidation: function (input) {
        return (result = (input === 1 || input === 0) ? true : false);
    },
    trimSpaces: function(input){
        return input.trim();
    }
};