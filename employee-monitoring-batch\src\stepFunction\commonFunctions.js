// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone');
// require table alias function
const { ehrTables, appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { defaultValues, s3FileUpload } = require('../common/appConstants');
// require common function
const employeeMonitoringCommonFunction = require('../webApplication/employeeMonitoringCommonFunction');
const { getOrganizationModerateGoalSettings, getOrgLevelIdleTimeSettings } = require('../webApplication/settingsCommonFunction');
const { getTopFiveAppAndDomain } = require('../common/activityCommonFunctions');

module.exports = {
    // function to get database connection
    getConnection: async (stageName, dbPrefix, dbSecretName, region, orgCode) => {
        try {
            let connection = await commonLib.func.getDataBaseConnection(
                {
                    stageName: stageName, dbPrefix: dbPrefix, dbSecretName: dbSecretName,
                    region: region, orgCode: orgCode
                })
            return connection;
        } catch (catchError) {
            console.log('Error in getConnection function main catch block', catchError);
            return {};
        }
    },
    // function to update summary details in app manager database
    updateSummaryDataInMasterTable: async (appmanagerDbConnection, updateParams, orgCode, tableName, stageName = null, dbPrefix = null, dbSecretName = null, region = null) => {
        try {
            // if stage name exists then update the details else create database and then update the details
            if (!stageName) {
                return (
                    // based on orgcode update the details in manager table
                    appmanagerDbConnection(tableName)
                        .update(updateParams)
                        .where('Org_Code', orgCode)
                        .then(updateSummaryDetails => {
                            console.log('Status updated in manager table for ' + orgCode + ' instance', updateSummaryDetails);
                            return 'success';
                        })
                        .catch(catchError => {
                            console.log('Error in updateSummaryDataInMasterTable function .catch block.', catchError);
                            return '';
                        })
                );
            }
            else {
                // make database connection
                let databaseConnection = await commonLib.func.getDataBaseConnection({ stageName: stageName, dbPrefix: dbPrefix, dbSecretName: dbSecretName, region: region, orgCode: orgCode, isAppManager: 1 })
                if (Object.keys(databaseConnection).length) {
                    let appmanagerDbConnection = knex(databaseConnection.AppManagerDb);
                    return (
                        // based on orgcode update the details in manager table
                        appmanagerDbConnection(tableName)
                            .update(updateParams)
                            .where('Org_Code', orgCode)
                            .then(updateSummaryDetails => {
                                return 'success';
                            })
                            .catch(function (catchError) {
                                console.log('Error in updateSummaryDataInMasterTable function .catch block.', catchError);
                                return '';
                            })
                            .finally(() => {
                                // destroy db connection
                                appmanagerDbConnection.destroy();
                            })
                    );
                }
                else {
                    console.log('Error in creating appmanager database connection.');
                    return '';
                }
            }
        }
        catch (error) {
            console.log('Error in updateSummaryDataInMasterTable function main catch block', error);
            return '';
        }
    },
    // function to insert summary details in app manager database
    insertSummaryDataInMasterTable: async (appmanagerDbConnection, inputParams, tableName) => {
        try {
            return (
                appmanagerDbConnection(tableName)
                    .insert(inputParams)
                    .then(async (insertData) => {
                        return 'success';
                    })
                    .catch(catchError => {
                        console.log('Error in insertSummaryDataInMasterTable .catch block.', catchError);
                        return 'error';
                    })
            );
        }
        catch (error) {
            console.log('Error in insertSummaryDataInMasterTable function main catch block', error);
            return 'error';
        }
    },
    // function to update status in insights notification manager table based on input orgcode
    updateNotificationManagerTable: async (appManagerDbConnection, updateParams, orgCode) => {
        try {
            return (
                appManagerDbConnection(appManagerTables.insightsNotificationManager)
                    .update(updateParams)
                    .where('Org_Code', orgCode)
                    .then(() => {
                        return 'success';
                    })
                    .catch(catchError => {
                        console.log('Error in updateNotificationManagerTable function .catch block.', catchError);
                        return 'error';
                    })
            )
        }
        catch (error) {
            console.log('Error in updateNotificationManagerTable function main catch block.', error);
            return 'error';
        }
    },
    // function to update status in insights notification status log table
    updateNotificationStatusLog: async (orgDbConnection, updateParams, employeeIdArray) => {
        try {
            return (
                orgDbConnection
                    .update(updateParams)
                    .from(ehrTables.insightsNotificationStatusLog)
                    .whereIn('Employee_Id', employeeIdArray)
                    .then(async (getData) => {
                        return 'success';
                    })
                    .catch(catchError => {
                        console.log('Error in updateNotificationStatusLog function .catch block.', catchError);
                        return 'error';
                    })
            )
        }
        catch (error) {
            console.log('Error in updateNotificationStatusLog function main catch block.', error);
            return 'error';
        }
    },
    // based on the input source get the active employeeIds
    getEmpMonitoringActiveEmpIds: async (orgDbConnection, inputSource) => {
        try {
            let getIds = [];
            // if input is admin then get all the active employeeIds
            if (inputSource === 'admin') {
                getIds = await employeeMonitoringCommonFunction.getAdminEmployeeIds(orgDbConnection, 0, 0, '');
            }
            // If input is manager then get only the active managerId who are not a admin
            else if (inputSource === 'manager') {
                let getManagers = await employeeMonitoringCommonFunction.getAllManagerIds(orgDbConnection, 1);
                if (getManagers.length > 0) {
                    let allAdminIds = await employeeMonitoringCommonFunction.getAdminEmployeeIds(orgDbConnection, 0, 0, '');
                    // filter the manager which are manager and not a admin
                    getIds = getManagers.filter(d => !allAdminIds.includes(d));
                }
            }
            if (getIds.length > 0) {
                // check whether the employees are active members
                return new Promise(function (resolve, reject) {
                    let emMembersQry = orgDbConnection
                        .pluck('Employee_Id')
                        .from(ehrTables.teamMembers)
                        .whereIn('Employee_Id', getIds)
                        .where('Member_Status', 'Active');
                    //daily insights should be send for the users who are having admin role in em_members. 
                    /** In the hrms dashboard plan we are not having the Member_Role set as 1 for admin, so we can check the status alone */
                    // if(inputSource === 'admin'){
                    //     emMembersQry = emMembersQry.where('Member_Role',1);
                    // } 

                    emMembersQry.then(async (empIdArray) => {
                        resolve(empIdArray);
                    })
                        .catch(catchError => {
                            console.log('Error in getEmpMonitoringActiveEmpIds function .catch block.', catchError);
                            resolve([]);
                        })
                })

            }
            else {
                console.log('Error in getting admin/manager employee ids', getIds);
                return [];
            }
        }
        catch (error) {
            console.log('Error in getEmpMonitoringActiveEmpIds function main catch block.', error);
            return [];
        }
    },
    // get the reportees employeeId based on the input managerId
    getEmpIdBasedOnTheirManagerId: async (orgDbConnection, managerId) => {
        try {
            // get the employee Id array based on settings
            let employeeIdsArray = await employeeMonitoringCommonFunction.getManagerHierarchy(orgDbConnection, managerId, 0);
            // remove the duplicate values if exist
            employeeIdsArray = [...new Set(employeeIdsArray)];
            if (employeeIdsArray.length > 0) {
                return (
                    orgDbConnection(ehrTables.teamMembers)
                        .pluck('Employee_Id')
                        .where('Member_Status', 'Active')
                        .whereIn('Employee_Id', employeeIdsArray)
                        .then(async (empIdArray) => {
                            return empIdArray;
                        })
                        .catch(catchError => {
                            console.log('Error in getEmpIdBasedOnTheirManagerId function .catch block.', catchError);
                            return [];
                        })
                );
            }
            else {
                console.log('Employee Id does not exist');
                return [];
            }
        }
        catch (error) {
            console.log('Error in getEmpIdBasedOnTheirManagerId function main catch block.', error);
            return [];
        }
    },
    // function to form the activity data
    formActivityData: async (orgDbConnection, empIdArray, date) => {
        try {
            return (
                orgDbConnection
                    .select(
                        orgDbConnection.raw('CASE WHEN sum(Active_Duration_In_Minutes) > 0 then SUM(Active_Duration_In_Minutes) ELSE 0 END as totalTimeSpent'),
                        orgDbConnection.raw('CASE WHEN sum(Active_Duration_In_Minutes) > 0 then ROUND(100*((sum(Active_Duration_In_Minutes)*60)/(sum(Total_Activity_Duration_In_Minutes)*60))) ELSE "0" END as activePercentage'),
                        orgDbConnection.raw('CASE WHEN sum(Not_Active_Duration_In_Minutes) > 0 then ROUND(100*((sum(Not_Active_Duration_In_Minutes)*60)/(sum(Total_Activity_Duration_In_Minutes)*60))) ELSE "0" END as notActivePercentage'),
                        orgDbConnection.raw('CASE WHEN sum(Idle_Duration_In_Minutes) > 0 then ROUND(100*((sum(Idle_Duration_In_Minutes)*60)/(sum(Total_Activity_Duration_In_Minutes)*60))) ELSE "0" END as idlePercentage')
                    )
                    .from(ehrTables.employeeActivityDailySummary)
                    .whereIn('Employee_Id', empIdArray)
                    .where('Activity_Date', date)
                    .then(async (activityData) => {
                        let totalTimeSpent = await convertMinutesToHHMMSS(activityData[0].totalTimeSpent);
                        activityData[0].totalTimeSpent = totalTimeSpent;
                        return activityData;
                    })
                    .catch(catchError => {
                        console.log('Error in formActivityData function .catch block.', catchError);
                        return [];
                    })
            );
        }
        catch (error) {
            console.log('Error in formActivityData function main catch block.', error);
            return [];
        }
    },
    // function to get insights data from summary table
    insightsDetails: async (orgDbConnection, empIdArray, date, source) => {
        let response = {
            lowActivityEmployeeDetails: [],
            highActivityEmployeeDetails: [],
            appData: [],
            urlData: []
        }
        try {
            // get the organization level consider not active and idle time settings
            let considerNotActiveIdleTime = await getOrgLevelIdleTimeSettings(orgDbConnection);
            // In case of error we will consider it as Never by default
            (considerNotActiveIdleTime) ? (considerNotActiveIdleTime) : 'Never';

            // for admin we get the 5 records for each low and high activity and for manager each 10 records
            let noOfRecords = (source === 'admin') ? (defaultValues.numberOfActivityDataForAdmin) : (defaultValues.numberOfActivityDataForManager);

            let subQuery = orgDbConnection
                .select('Employee_Id', 'Activity_Start_Hour as startHour', 'Activity_End_Hour as endHour', 'Productive_Activity_Duration_In_Minutes',
                    orgDbConnection.raw('CASE WHEN sum(Active_Duration_In_Minutes) > 0 then TIME_FORMAT(SEC_TO_TIME(SUM(Active_Duration_In_Minutes)*60),"%H:%i:%s")  ELSE "00:00:00" END as totalActivityDuration')
                )
                .from(ehrTables.employeeActivityDailySummary)
                .whereIn('Employee_Id', empIdArray)
                .where('Activity_Date', date)
                .groupBy('Employee_Id')
                .orderBy('EfficiencyPro', 'Desc')
                .orderBy('Productive_Activity_Duration_In_Minutes', 'Desc')

            /** if settings is never then consider percentage without idle time else consider the percentage with idle time 
              *The users having high Productive_Activity_Duration_In_Minutes and activityPercentage will be considered as highly efficient */
            if (considerNotActiveIdleTime === 'Never') {
                subQuery = subQuery
                    .select('Sys_Productivity_Percentage_Without_IdleTime as activityPercentage',
                        orgDbConnection.raw('ROUND((Productive_Activity_Duration_In_Minutes*Sys_Productivity_Percentage_Without_IdleTime)) AS EfficiencyPro'))
            }
            else if (considerNotActiveIdleTime === 'Always') {
                subQuery = subQuery
                    .select('Sys_Productivity_Percentage_With_IdleTime as activityPercentage',
                        orgDbConnection.raw('ROUND((Productive_Activity_Duration_In_Minutes*Sys_Productivity_Percentage_With_IdleTime)) AS EfficiencyPro'))
            }
            else {
                subQuery = subQuery
                    .select('Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours as activityPercentage',
                        orgDbConnection.raw('ROUND((Productive_Activity_Duration_In_Minutes*Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours)) AS EfficiencyPro'))
            }

            return (
                await subQuery
                    .then(async (activityData) => {
                        if (activityData.length > 0) {
                            // get the first 5/10 records based on input source as high activity and last 5/10 as low activity
                            let highActivityEmployees = activityData.slice(0, noOfRecords);
                            let lowActivityEmployees = activityData.slice(Math.max(activityData.length - noOfRecords, 0));
                            // concat both low and high activity record by removing the duplication
                            activityData = Object.values(highActivityEmployees.concat(lowActivityEmployees).reduce((object1, object2) => {
                                object1[object2.Employee_Id] = object2;
                                return object1;
                            }, {}));
                            // get the organization level moderate activity goal settings. In case if error consider default as 40
                            let orgLevelModerateGoalSettings = await getOrganizationModerateGoalSettings(orgDbConnection);
                            (orgLevelModerateGoalSettings) ? orgLevelModerateGoalSettings : 40;
                            // get the activity employees as low if the activity percentage is less than the moderate activity level
                            let lowActivityEmployeeDetails = activityData.filter(data => data.activityPercentage < orgLevelModerateGoalSettings);
                            /** get the activity employees as high if the activity percentage is more than or equal the moderate activity level.
                            * The user should have Productive_Activity_Duration_In_Minutes greater than 120 mins */
                            let highActivityEmployeeDetails = activityData.filter(data => data.activityPercentage >= orgLevelModerateGoalSettings && data.Productive_Activity_Duration_In_Minutes > 120);

                            if (Object.keys(lowActivityEmployeeDetails).length > 0) {
                                // sort the list in asc order based on activity percentage
                                lowActivityEmployeeDetails.sort(function (a, b) {
                                    return a.activityPercentage - b.activityPercentage;
                                });
                                // get the last 5/10 records for low activity
                                lowActivityEmployeeDetails = lowActivityEmployeeDetails.slice(Math.max(Object.keys(lowActivityEmployeeDetails).length - noOfRecords, 0));
                                // Iterate and get the low activity employee details
                                for (let key of lowActivityEmployeeDetails) {
                                    key['employeeName'] = await employeeMonitoringCommonFunction.getEmployeeName(orgDbConnection, key.Employee_Id);
                                    key['checkIn'] = (key.startHour) ? ((key.startHour).split(' ')[1]) : '-'; // startHour - 2021-05-05 10:00:00 get 10:00:00 for presentation
                                    key['checkOut'] = (key.endHour) ? ((key.endHour).split(' ')[1]) : '-';
                                }
                            }
                            if (Object.keys(highActivityEmployeeDetails).length > 0) {
                                // sort the list in desc order based on EfficiencyPro and Productive_Activity_Duration_In_Minutes
                                highActivityEmployeeDetails.sort(function (a, b) {
                                    return b.EfficiencyPro - a.EfficiencyPro || b.Productive_Activity_Duration_In_Minutes - a.Productive_Activity_Duration_In_Minutes;
                                });
                                // get the top 5/10 records for high activity
                                highActivityEmployeeDetails = highActivityEmployeeDetails.slice(0, noOfRecords);
                                for (let key of highActivityEmployeeDetails) {
                                    key['employeeName'] = await employeeMonitoringCommonFunction.getEmployeeName(orgDbConnection, key.Employee_Id);
                                    key['checkIn'] = (key.startHour) ? ((key.startHour).split(' ')[1]) : '-';
                                    key['checkOut'] = (key.endHour) ? ((key.endHour).split(' ')[1]) : '-';
                                }
                            }
                            /** get the top 5 apps and urls used */
                            let appUrlData = await getTopFiveAppAndDomain(orgDbConnection, empIdArray, date);
                            response.lowActivityEmployeeDetails = lowActivityEmployeeDetails;
                            response.highActivityEmployeeDetails = highActivityEmployeeDetails;
                            response.appData = appUrlData.appData;
                            response.urlData = appUrlData.urlData;
                            return response;
                        }
                        else {
                            console.log('No activity data exist.')
                            return response;
                        }
                    })
                    .catch(error => {
                        console.log('Error in insightsDetails function .catch block', error);
                        return response;
                    })
            );
        }
        catch (error) {
            console.log('Error in insightsDetails function main catch block', catchError);
            return response;
        }
    },
    /** function to get the employeeId based on notification settings */
    getSettingsEnabledEmployeeIds: async (orgDbConnection) => {
        try {
            return (
                orgDbConnection
                    .pluck('EIN.Employee_Id')
                    .from(ehrTables.employeeLevelInsightsNotificationSettings + ' as EIN')
                    .innerJoin(ehrTables.teamMembers + ' as EMTM', 'EIN.Employee_Id', 'EMTM.Employee_Id')
                    .where('EMTM.Member_Status', 'Active')
                    .where('EIN.Insights_Notification', "Enable")
                    .then(async (getSettingData) => {
                        return getSettingData;
                    })
                    .catch(error => {
                        console.log('Error in getSettingsEnabledEmployeeIds function .catch block', error);
                        return [];
                    })
            );
        }
        catch (error) {
            console.log('Error in getSettingsEnabledEmployeeIds function main catch block', error);
            return [];
        }
    },
    /** function to get the top 5 high activity employees in that activity date */
    getHighEfficiencyDetails: async (orgDbConnection, date) => {
        try {
            // get the organization level consider not active and idle time settings
            let considerNotActiveIdleTime = await getOrgLevelIdleTimeSettings(orgDbConnection);
            // In case of error we will consider it as Never by default
            (considerNotActiveIdleTime) ? (considerNotActiveIdleTime) : 'Never';
            // get the organization level moderate activity goal settings. In case if error consider default as 40
            let orgLevelModerateGoalSettings = await getOrganizationModerateGoalSettings(orgDbConnection);
            (orgLevelModerateGoalSettings) ? (orgLevelModerateGoalSettings) : 40;
            /** get the activity employees as high if the activity percentage is more than or equal the moderate activity level and 
                * The user should have Productive_Activity_Duration_In_Minutes greater than 120 mins */
            let subQuery = orgDbConnection
                .select('EDS.Productive_Activity_Duration_In_Minutes', orgDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"))
                .from(ehrTables.employeeActivityDailySummary + ' as EDS')
                .innerJoin(ehrTables.empPersonalInfo + ' as EPI', 'EDS.Employee_Id', 'EPI.Employee_Id')
                .where('EDS.Productive_Activity_Duration_In_Minutes', '>', 120)
                .where('EDS.Activity_Date', date)
                .orderBy('EfficiencyPro', 'Desc')
                .orderBy('EDS.Productive_Activity_Duration_In_Minutes', 'Desc')

            /** if settings is never then consider percentage without idle time else consider the percentage with idle time
             *The users having high Productive_Activity_Duration_In_Minutes and activityPercentage will be considered as highly efficient */
            if (considerNotActiveIdleTime === 'Never') {
                subQuery = subQuery
                    .select(orgDbConnection.raw('ROUND((EDS.Productive_Activity_Duration_In_Minutes*EDS.Sys_Productivity_Percentage_Without_IdleTime)) AS EfficiencyPro'))
                    .where('EDS.Sys_Productivity_Percentage_Without_IdleTime', '>=', orgLevelModerateGoalSettings)
            }
            else if (considerNotActiveIdleTime === 'Always') {
                subQuery = subQuery
                    .select(orgDbConnection.raw('ROUND((EDS.Productive_Activity_Duration_In_Minutes*EDS.Sys_Productivity_Percentage_With_IdleTime)) AS EfficiencyPro'))
                    .where('EDS.Sys_Productivity_Percentage_With_IdleTime', '>=', orgLevelModerateGoalSettings)
            }
            else {
                subQuery = subQuery
                    .select(orgDbConnection.raw('ROUND((EDS.Productive_Activity_Duration_In_Minutes*EDS.Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours)) AS EfficiencyPro'))
                    .where('EDS.Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours', '>=', orgLevelModerateGoalSettings)
            }

            return (
                await subQuery
                    .then(async (activityData) => {
                        return activityData.length > 0 ? activityData : [];
                    })
                    .catch(error => {
                        console.log('Error in getHighEfficiencyDetails function .catch block', error);
                        return [];
                    })
            );
        }
        catch (error) {
            console.log('Error in getHighEfficiencyDetails function main catch block', error);
            return [];
        }
    },
    // function to update the data integration log
    updateDataIntegrationLog: (orgDbConnection, updateParams, logId) => {
        try {
            return (
                orgDbConnection(ehrTables.dataIntegrationLog)
                    .update(updateParams)
                    .where('Log_Id', logId)
                    .then(() => {
                        return 'success';
                    })
                    .catch(catchError => {
                        console.log('Error in updateDataIntegrationLog function .catch block.', catchError);
                        return 'error';
                    })
            );
        }
        catch (error) {
            console.log('Error in updateDataIntegrationLog function main catch block', error);
            return 'error';
        }
    },
    // function to update the schedule status based on jobId
    updateIntegrationScheduleStatus: (appManagerDbConnection, updateParams, jobId) => {
        try {
            return (
                appManagerDbConnection(appManagerTables.dataIntegrationSchedule)
                    .update(updateParams)
                    .where('Job_Id', jobId)
                    .then(() => {
                        return 'success';
                    })
                    .catch(catchError => {
                        console.log('Error in updateIntegrationScheduleStatus function .catch block.', catchError);
                        return 'error';
                    })
            );
        }
        catch (error) {
            console.log('Error in updateIntegrationScheduleStatus function main catch block', error);
            return 'error';
        }
    },
    // function to form start and end date based on input frequency
    formDateRangeBaseOnFrequency: (currentDate, frequency) => {
        try {
            /** We will trigger offline process only on the next day of activity. 
             * So end date will be less than 1 day from the current input date  */
            let endDate = moment(currentDate).subtract(1, 'd').format('YYYY-MM-DD');
            let startDate = '';
            let response = {
                startDate: startDate,
                endDate: endDate
            }
            // if frequency is daily then start date will be same as the end date
            if (frequency === 'daily') {
                response.startDate = endDate;
            }
            // if frequency is weekly then start date will be 7 days less than the end date
            else if (frequency === 'weekly') {
                response.startDate = moment(endDate).subtract(7, 'd').format('YYYY-MM-DD');
            }
            // else frequency is monthly then start date will be less than 1 month from the end date
            else {
                response.startDate = moment(endDate).subtract(1, 'month').format('YYYY-MM-DD');
            }
            return response;
        }
        catch (error) {
            console.log('Error in formDateRangeBaseOnFrequency function main catch block', error);
            return {};
        }
    },
    // function to get data integration schedule details based on jobId
    getIntegrationDetails: (appManagerDbConnection, jobId) => {
        try {
            return (
                appManagerDbConnection
                    .select('*')
                    .from(appManagerTables.dataIntegrationSchedule)
                    .where('Job_Id', jobId)
                    .then(getDetails => {
                        if (getDetails.length > 0) {
                            return getDetails[0];
                        }
                        else {
                            console.log('Schedule details not found.');
                            return {};
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in getIntegrationDetails function .catch block.', catchError);
                        return {};
                    })
            );
        }
        catch (error) {
            console.log('Error in getIntegrationDetails function main catch block', error);
            return {};
        }
    },
    // function to get the log details based on logId
    getLogDetails: (orgDbConnection, logId) => {
        try {
            return (
                orgDbConnection
                    .select('Start_Date', 'End_Date', 'S3_File_Name', 'Status')
                    .from(ehrTables.dataIntegrationLog)
                    .where('Log_Id', logId)
                    .then(getLogData => {
                        if (getLogData.length > 0) {
                            return getLogData[0];
                        }
                        else {
                            console.log('No log data found.');
                            return {};
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in getLogDetails function .catch block.', catchError);
                        return {};
                    })
            );
        }
        catch (error) {
            console.log('Error in getLogDetails function main catch block', error);
            return {};
        }
    },
    /** function to get secret details */
    getSecretDetails: (region, orgCode, appManagerDbConnection) => {
        try {
            // get the secret details based on input orgcode
            return (
                appManagerDbConnection
                    .select('Secret_Details')
                    .first()
                    .from(appManagerTables.orgSecretDetails)
                    .where('Org_Code', orgCode)
                    .then(async (getData) => {
                        if (getData && Object.keys(getData).length > 0) {
                            let secretName = getData.Secret_Details;
                            const AWS = require('aws-sdk');
                            // Create client for secrets manager
                            let client = new AWS.SecretsManager({
                                region: region
                            });
                            // Get secrets from aws secrets manager
                            let secretKeys = await client.getSecretValue({ SecretId: secretName }).promise();
                            secretKeys = JSON.parse(secretKeys.SecretString);
                            return secretKeys;
                        }
                        else {
                            throw 'No secret details exist.';
                        }
                    })
                    .catch(catchError => {
                        console.log('Error in getSecretDetails function .catch block', catchError);
                        throw (catchError === 'No secret details exist.') ? ('No secret details exist.') : ('Error in getting secret details.');
                    })
            )
        }
        catch (error) {
            console.log('Error in getSecretDetails function main catch block', error);
            throw (error === 'No secret details exist.') ? ('No secret details exist.') : ('Error in getting secret details.');
        }
    },
    uploadFileInS3: async (exportFileName, bucketName, filePath) => {
        try {
            const aws = require('aws-sdk');
            const fs = require('fs');
            let s3 = new aws.S3();
            let data = fs.createReadStream(exportFileName);
            let params = {
                Bucket: bucketName,
                Key: filePath,
                Body: data,
                ContentType: s3FileUpload.binaryFile,
                ServerSideEncryption: s3FileUpload.defaultEncryption
            };
            let uploadResponse = await s3.upload(params).promise();
            return 'success';
        }
        catch (error) {
            console.log('Error in uploadFileInS3 function catch block.', error);
            return 'error'
        }
    }
};

// get the summarization date based on timezone
async function getSummarizationDateBasedOnZone(timeZone) {
    try {
        let currentDate = moment.tz(moment(), timeZone).format('YYYY-MM-DD');
        let summarizationDate = moment(currentDate).subtract(1, 'd').format('YYYY-MM-DD');
        return summarizationDate;
    }
    catch (error) {
        console.log('Error in getSummarizationDateBasedOnZone function catch block', error)
        return '';
    }
}

async function convertMinutesToHHMMSS(minutes) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    const hoursString = hours.toString().padStart(2, '0');
    const minutesString = remainingMinutes.toString().padStart(2, '0');
    const secondsString = '00';

    return `${hoursString}:${minutesString}:${secondsString}`;
}

// function to update summary details based on execution status in organization table
async function updateStatusInOrganizationStatusTable(updateParams, tableName, orgCode, stageName, dbPrefix, dbSecretName, region, executionStatus) {
    try {
        let getAppDbConnection = await commonLib.stepFunctions.getConnection(stageName, dbPrefix, dbSecretName, region, '', 1);
        let appManagerDbConnection = knex(getAppDbConnection.AppManagerDb);
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection, orgCode);
        if (orgRegionDetails && Object.keys(orgRegionDetails).length > 0) {
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);


            // make database connection
            let databaseConnection = await commonLib.stepFunctions.getConnection(stageName, dbPrefix, dbSecretName, region, orgCode, 0, additionalHeaders);
            if (Object.keys(databaseConnection).length) {
                let orgDbConnection = knex(databaseConnection.OrganizationDb);
                return (
                    orgDbConnection(tableName)
                        .update(updateParams)
                        .whereIn('Execution_Status', executionStatus)
                        .then(async (updateData) => {
                            orgDbConnection ? orgDbConnection.destroy() : null;
                            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                            return 'success';
                        })
                        .catch(function (catchError) {
                            console.log('Error in updateStatusInOrganizationStatusTable function .catch block.', catchError);
                            orgDbConnection ? orgDbConnection.destroy() : null;
                            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                            return '';
                        })
                );
            }
            else {
                console.log('Error while creating database connection for ' + orgCode + 'instance');
                appManagerDbConnection ? appManagerDbConnection.destroy() : null;
                return '';
            }
        } else {
            console.log('Error while getting the data region for ' + orgCode + 'instance');
            appManagerDbConnection ? appManagerDbConnection.destroy() : null;
            return '';
        }
    }
    catch (error) {
        console.log('Error in updateStatusInOrganizationStatusTable function main catch block', error);
        orgDbConnection ? orgDbConnection.destroy() : null;
        return '';
    }
}

// function to update the summary record based on id
async function updateSummaryStatusBasedOnId(orgDbConnection, tableName, orgCode, processName, status, id, reason = null) {
    try {
        let updateParams = {};
        if (processName && processName === 'productivity') {
            updateParams = {
                Transaction_Status: status,
                Reason: (reason) ? (reason) : null,
                Summarization_Time: new Date()
            };
        }
        else {
            updateParams = {
                Summarization_Time: new Date(),
                Execution_Status: 'Completed',
                Transaction_Status: status,
                Reason: (reason) ? (reason) : null
            };
        }
        // formation of query
        let subQuery = orgDbConnection(tableName)

        // based on input process name form the where condition
        if (processName === 'app') {
            subQuery
                .where('App_Summarization_Id', id)
        }
        else if (processName === 'appTitle') {
            subQuery
                .where('App_Title_Summarization_Id', id)
        }
        else if (processName === 'url') {
            subQuery
                .where('Url_Summarization_Id', id)
        }
        else if (processName === 'productivity') {
            subQuery
                .where('AppUrl_Summarization_Id', id)
        }
        else {
            subQuery
                .where('Activity_Summarization_Id', id)
        }
        return (
            subQuery
                .update(updateParams)
                .then(async (updateData) => {
                    return 'success';
                })
                .catch(function (catchError) {
                    console.log('Error in updateSummaryStatusBasedOnId function .catch block.', catchError);
                    return '';
                })
        );
    }
    catch (error) {
        console.log('Error in updateSummaryStatusBasedOnId function main catch block', error);
        return '';
    }
}

// function to insert new record for the failed work schedule records
async function insertRecordForFailedWSData(summaryDate, tableName, processName, orgCode, stageName, dbPrefix, dbSecretName, region, appmanagerDbConnection) {
    try {
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection, orgCode);
        if (orgRegionDetails && Object.keys(orgRegionDetails).length > 0) {
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);


            // make database connection
            let databaseConnection = await commonLib.func.getDataBaseConnection({ stageName: stageName, dbPrefix: dbPrefix, dbSecretName: dbSecretName, region: region, orgCode: orgCode, additionalHeaders: additionalHeaders })
            if (Object.keys(databaseConnection).length) {
                let organizationDbConnection = knex(databaseConnection.OrganizationDb);
                return (
                    organizationDbConnection
                        .transaction(function (trx) {
                            // get the records with failed status
                            let subQuery = organizationDbConnection(tableName)
                                .select('WorkSchedule_Id', 'Regular_Work_Start_Time', 'Regular_Work_End_Time', 'Shift_Margin_From', 'Shift_Margin_To', 'Twodays_Flag', 'TimeZone')
                                .where('Transaction_Status', 'Failed')

                            // based on input process name differentiate select and where condition
                            if (processName === 'app') {
                                subQuery = subQuery
                                    .select('App_Summarization_Date')
                                    .where('App_Summarization_Date', '>=', summaryDate)
                            }
                            else if (processName === 'url') {
                                subQuery = subQuery
                                    .select('Url_Summarization_Date')
                                    .where('Url_Summarization_Date', '>=', summaryDate)
                            }
                            else if (processName === 'appTitle') {
                                subQuery = subQuery
                                    .select('App_Title_Summarization_Date')
                                    .where('App_Title_Summarization_Date', '>=', summaryDate)
                            }
                            else {
                                subQuery = subQuery
                                    .select('Activity_Summarization_Date')
                                    .where('Activity_Summarization_Date', '>=', summaryDate)
                            }
                            return (
                                subQuery
                                    .transacting(trx)
                                    .then(async (getSummaryData) => {
                                        if (getSummaryData.length > 0) {
                                            let inputData = getSummaryData.map(field => ({
                                                WorkSchedule_Id: field.WorkSchedule_Id,
                                                Shift_Margin_From: field.Check_In_Consideration_Time,
                                                Shift_Margin_To: field.Check_Out_Consideration_Time,
                                                Transaction_Status: 'Open',
                                                Execution_Status: 'Started',
                                                Summarization_Time: new Date()
                                            }))

                                            // combine 2 array of json as a single array of json based on WorkSchedule_Id
                                            inputData = inputData.map(obj => {
                                                let data = getSummaryData.find(item => item.WorkSchedule_Id === obj.WorkSchedule_Id);
                                                return { ...obj, ...data }
                                            });
                                            // insert record in status table
                                            return (
                                                organizationDbConnection(tableName)
                                                    .insert(inputData)
                                                    .transacting(trx)
                                                    .then(insertData => {
                                                        return 'success';
                                                    })
                                            );
                                        }
                                        else {
                                            console.log('No failed records found');
                                            return 'No failed records found.';
                                        }
                                    })
                            )
                        })
                        .then(function (result) {
                            return result;
                        })
                        .catch(function (catchError) {
                            console.log('Error in insertRecordForFailedWSData function .catch block.', catchError);
                            return '';
                        })
                        .finally(() => {
                            organizationDbConnection.destroy();
                        })
                );
            }
            else {
                console.log('Error while creating database connection for ' + orgCode + ' instance');
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return '';
            }
        } else {
            console.log('Error while getting the data region for ' + orgCode + ' instance');
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return '';
        }
    }
    catch (error) {
        console.log('Error in insertRecordForFailedWSData function catch block', error);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return '';
    }
}

// function to insert work schedule data for the input work scheduleId array
async function insertNewWorkScheduleData(organizationDbConnection, tableName, inputSource, workScheduleIdArray) {
    try {
        // get the work schedule details for the input ids
        let workScheduleData = await getWorkScheduleData(organizationDbConnection, workScheduleIdArray);
        if (workScheduleData && workScheduleData.length > 0) {
            let subQuery = organizationDbConnection(tableName)

            // based on input process name differentiate the condition
            if (inputSource === 'app') {
                subQuery = subQuery
                    .max('App_Summarization_Date as date')
            }
            else if (inputSource === 'appTitle') {
                subQuery = subQuery
                    .max('App_Title_Summarization_Date as date')
            }
            else if (inputSource === 'url') {
                subQuery = subQuery
                    .max('Url_Summarization_Date as date')
            }
            else {
                subQuery = subQuery
                    .max('Activity_Summarization_Date as date')
            }
            // get the last processed summary data
            let getWSLastProcessedData = await subQuery
                .select('WorkSchedule_Id')
                .whereIn('WorkSchedule_Id', workScheduleIdArray)
                .groupBy('WorkSchedule_Id')
                .then(async (getSummaryData) => {
                    return getSummaryData;
                })
            // combine 2 array of json as a single array of json based on WorkSchedule_Id
            workScheduleData = workScheduleData.map(obj => {
                let data = getWSLastProcessedData.find(item => item.WorkSchedule_Id === obj.WorkSchedule_Id);
                return { ...obj, ...data }
            });

            let inputData = workScheduleData.map(field => ({
                WorkSchedule_Id: field.WorkSchedule_Id,
                Regular_Work_Start_Time: field.Regular_Work_Start_Time,
                Regular_Work_End_Time: field.Regular_Work_End_Time,
                Shift_Margin_From: (field.Check_In_Consideration_Time) ? (field.Check_In_Consideration_Time) : 0,
                Shift_Margin_To: (field.Check_Out_Consideration_Time) ? (field.Check_Out_Consideration_Time) : 0,
                Twodays_Flag: field.Twodays_Flag,
                Transaction_Status: 'Open',
                Execution_Status: 'Started',
                date: field.date,
                Summarization_Time: new Date()
            }))

            await Promise.all(inputData.map(async field => {
                if (inputSource === 'app') {
                    field.App_Summarization_Date = moment(field.date).add(1, 'd').format('YYYY-MM-DD');
                }
                else if (inputSource === 'appTitle') {
                    field.App_Title_Summarization_Date = moment(field.date).add(1, 'd').format('YYYY-MM-DD');
                }
                else if (inputSource === 'url') {
                    field.Url_Summarization_Date = moment(field.date).add(1, 'd').format('YYYY-MM-DD');
                }
                else {
                    field.Activity_Summarization_Date = moment(field.date).add(1, 'd').format('YYYY-MM-DD');
                }
                delete field.date;
            }));

            // insert record in status table
            return (
                organizationDbConnection(tableName)
                    .insert(inputData)
                    .then(insertData => {
                        return 'success';
                    })
                    .catch(function (catchError) {
                        console.log('Error in insertNewWorkScheduleData function .catch block', catchError);
                        return 'error';
                    })
            );
        }
        else {
            console.log('Work schedule record does not exist or error while getting the work schedule details.');
            return 'error';
        }
    }
    catch (error) {
        console.log('Error in insertNewWorkScheduleData function catch block', error);
        return 'error';
    }
}

// function to retrieve the work schedule details for the ids
async function getWorkScheduleData(organizationDbConnection, workScheduleIdArray) {
    try {
        let subQuery = organizationDbConnection(ehrTables.workSchedule + ' as WS')
            .select('WS.WorkSchedule_Id', 'WS.Regular_Work_Start_Time', 'WS.Regular_Work_End_Time', 'WS.Twodays_Flag', 'WS.Check_In_Consideration_Time', 'WS.Check_Out_Consideration_Time', 'T.TimeZone_Id')
            .innerJoin(ehrTables.timezone + ' as T', 'WS.Zone_Id', 'T.Zone_Id')
            .whereNotNull('WS.Regular_Work_Start_Time')
            .whereNotNull('WS.Regular_Work_End_Time')

        if (workScheduleIdArray.length > 0) {
            subQuery = subQuery
                .whereIn('WS.WorkSchedule_Id', workScheduleIdArray)
        }
        return (
            subQuery
                .then(async (getWorkScheduleData) => {
                    return getWorkScheduleData;
                })
                .catch(function (catchError) {
                    console.log('Error in getWorkScheduleData function catch block', catchError);
                    return '';
                })
        )
    }
    catch (error) {
        console.log('Error in getWorkScheduleData function catch block', error);
        return '';
    }
}

// function to remove user from master summary table
async function removeUserFromAppManager(appmanagerDbConnection, orgCode, masterTable) {
    try {
        return (
            // based on orgcode delete the user details in manager table
            appmanagerDbConnection(masterTable)
                .del()
                .where('Org_Code', orgCode)
                .then(deleteUser => {
                    return 'success';
                })
                .catch(catchError => {
                    console.log('Error in removeUserFromAppManager function .catch block.', catchError);
                    return '';
                })
        );
    }
    catch (error) {
        console.log('Error in removeUserFromAppManager function main catch block.', error);
        return '';
    }
};

// function to trigger summary process for each instances
function triggerSummarizationLambda(params, orgCode) {
    try {
        // require aws-sdk
        const AWS = require("aws-sdk");
        // Create lambda object to invoke lambda
        const lambda = new AWS.Lambda({
            region: process.env.region,
            httpOptions: {
                timeout: 240000 //sets the socket to timeout after timeout milliseconds of inactivity on the socket. Defaults to two minutes (120000).
            }
        });
        return new Promise((resolve, reject) => {
            lambda.invoke(params, function (error, result) {
                if (error) {
                    console.log('Error occurred while triggering summarization process for ' + orgCode + ' instance', error);
                    resolve('');
                } else {
                    console.log('Summarization process triggered successfully for ' + orgCode + ' instance', result);
                    resolve('success');
                }
            })
        })
    }
    catch (error) {
        console.log('Error in triggerSummarizationLambda function main catch block', error);
        return '';
    }
};

// function to calculate the checkin/checkout time and activeDuration based on the active activity status
async function calculateActivityBasedOnActiveStatus(organizationDbConnection, employeeIdList, minDateTime, maxDateTime) {
    try {
        return (
            organizationDbConnection(ehrTables.empActivityDetails)
                .select('Employee_Id')
                .min('Activity_Start_Date_Time as checkInStartTime')
                .max('Activity_End_Date_Time as checkOutEndTime')
                .sum('Total_Activity_Duration as activeDuration')
                .where('Activity_Status', 'Active')
                .whereIn('Employee_Id', employeeIdList)
                .where('Activity_Start_Date_Time', '>=', minDateTime)
                .where(organizationDbConnection.raw("DATE_FORMAT(`Activity_End_Date_Time`, '%Y-%m-%d %H:%i')"), '<=', maxDateTime)
                .groupBy('Employee_Id')
                .then(getDetails => {
                    if (getDetails.length > 0) {
                        return getDetails;
                    }
                    else {
                        console.log('CheckIn and checkOut does not exist for all the employees', getDetails);
                        return [];
                    }
                })
                .catch(catchError => {
                    console.log('Error in calculateActivityBasedOnActiveStatus function .catch block.', catchError);
                    throw 'Error while calculating check in and check out time.';
                })
        );
    }
    catch (error) {
        console.log('Error in calculateActivityBasedOnActiveStatus function main catch block.', error);
        throw 'Error while calculating check in and check out time.';
    }
};

// function to calculate the activity date based on the work schedule
async function calculateActivityDateBasedOnZone(organizationDbConnection, schedule, logicType = 1) {
    let currentDateTime = moment(moment.tz(moment(), defaultValues.defaultTimeZone)).subtract(1, 'days').format('YYYY-MM-DD');

    try {
        return (
            // get the timezone based on the work schedule
            organizationDbConnection(ehrTables.workSchedule + ' as WS')
                .pluck('T.TimeZone_Id')
                .innerJoin(ehrTables.timezone + ' as T', 'WS.Zone_Id', 'T.Zone_Id')
                .innerJoin(ehrTables.empJob + ' as EJ', 'WS.WorkSchedule_Id', 'EJ.Work_Schedule')
                .whereNotNull('WS.Regular_Work_Start_Time')
                .whereNotNull('WS.Regular_Work_End_Time')
                .groupBy('WS.WorkSchedule_Id')
                .then(async (getWorkScheduleData) => {
                    if (getWorkScheduleData.length > 0) {
                        let count = 0;
                        /** if any timezone is less than UTC+5.30 then we need to consider 2 days less than the current date else
                         * consider date which is less than 1 from the current date
                         */
                        for (let record of getWorkScheduleData) {
                            let timeZoneOffset = moment.tz(moment.utc(), record).utcOffset();
                            if (timeZoneOffset > 0 && timeZoneOffset >= 330) {
                                count++
                            }
                            else {
                                console.log('Organization contains timezone less than Asia/kolkata');
                                break;
                            }

                            // let summarizationDate= moment(moment.tz(moment(),record)).subtract(1, 'days').format('YYYY-MM-DD');

                            // // calculate the date difference between current date and the last summary date(IST)
                            // let dateDifference = moment(currentDateTime).diff(moment(summarizationDate), 'days');
                            // console.log('dateDifference', record, currentDateTime, summarizationDate, dateDifference)

                            // if(dateDifference <= 0){
                            //     count++;
                            // } else {
                            //     console.log('Organization contains timezone less than Asia/kolkata.');
                            //     break;
                            // }
                        }

                        if (logicType && logicType === 2) {
                            if (schedule == 2 && getWorkScheduleData.length === count) { /** Schedule is morning and all the workschedule timezone offset >= IST */
                                return true;
                            } else if (schedule == 3 && getWorkScheduleData.length !== count) { /** Schedule is evening and if any one of the workschedule timezone offset < IST */
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            if (schedule == 1 && getWorkScheduleData.length === count) { /** Schedule is morning and all the workschedule timezone offset >= IST */
                                return currentDateTime;
                            } else if (schedule == 2 && getWorkScheduleData.length !== count) { /** Schedule is evening and if any one of the workschedule timezone offset < IST */
                                return currentDateTime;
                            } else {
                                return false;
                            }
                        }
                        // /** If the orgnaization is having timezone less a than indian timezone then do not send the insights email in the moring schedule */
                        // if(getWorkScheduleData.length!==count && schedule == 1){ 0 && 0
                        //     // currentDateTime=moment.tz(moment(), defaultValues.defaultTimeZone).subtract(2, 'days');
                        //     return false;
                        // } 
                        // return currentDateTime;
                    }
                    else {
                        console.log('No work schedule details exist');
                        // return currentDateTime;
                        return false;
                    }
                })
                .catch(catchError => {
                    console.log('Error in calculateActivityDateBasedOnZone function .catch block', catchError);
                    // return currentDateTime;
                    return false;
                })
        )
    }
    catch (error) {
        console.log('Error in calculateActivityDateBasedOnZone function catch block', error);
        // return currentDateTime;
        return false;
    }
}


// function to delete the file created in temp folder
async function deleteTempFolderFiles(fileName) {
    try {
        if (fileName) {
            const fs = require('fs');
            fs.unlink(fileName, (error) => {
                if (error) {
                    console.log('Error in deleting file in tmp folder', error);
                }
                else {
                    console.log('File deleted successfully');
                }
            });
        }
        return 'success'; // In case of error also we return as success
    }
    catch (error) {
        console.log('Error in deleteTempFolderFiles function catch block.', error);
        return 'error';
    }
};

// function to remove user from master summary table where  master summary orgcode not exist in orgCodeList
async function removeBulkInstancesFromAppManager(appmanagerDbConnection, orgCodeList, masterTable) {
    try {
        return (
            // based on orgcode delete the user details in manager table
            appmanagerDbConnection(masterTable)
                .del()
                .whereNotIn('Org_Code', orgCodeList)
                .then(deleteUser => {
                    console.log('Instances deleted from app manager table', deleteUser);
                    return 'success';
                })
                .catch(catchError => {
                    console.log('Error in removeBulkInstancesFromAppManager function .catch block.', catchError);
                    return '';
                })
        );
    }
    catch (error) {
        console.log('Error in removeBulkInstancesFromAppManager function main catch block.', error);
        return '';
    }
};

//function to get open status from master table
async function getDataFromMasterTableAccordingToStatus(appmanagerDbConnection, table, columnName, status, checkListPrepration) {
    try {
        let subQuery = appmanagerDbConnection(table)
            .select('*')
            .where(columnName, status);
        if (checkListPrepration) {
            subQuery = subQuery
                .where("Employee_List_Preparation", "Success")
        }
        return (
            await subQuery
                .then(data => {
                    return data;
                })
                .catch(e => {
                    console.log('Error in getOpenStatusDataFromMasterTable function .catch block.', e);
                    return false;
                })
        )
    }
    catch (e) {
        console.log('Error in getOpenStatusDataFromMasterTable function main catch block.', e);
        return false;
    }
}

// function to update in master table
async function updateInMasterTable(appmanagerDbConnection, inputParams, tableName, orgCode) {
    try {
        return (
            appmanagerDbConnection(tableName)
                .update(inputParams)
                .where('Org_Code', orgCode)
                .then((updatedData) => {
                    return 'success';
                })
                .catch(catchError => {
                    console.log('Error in updatedInMasterTable .catch block.', catchError);
                    return 'error';
                })
        );
    }
    catch (error) {
        console.log('Error in updateInMasterTable function main catch block', error);
        return 'error';
    }
}

async function getAttendanceSummaryDetails(organizationDbConnection, employeeIds, activityDate) {
    try {
        return (
            organizationDbConnection(ehrTables.employeeActivityDailySummary + ' as EADS')
                .select('EADS.Activity_Date', 'EADS.Employee_Id', 'EADS.Total_Activity_Duration_In_Minutes',
                    organizationDbConnection.raw('TIME_FORMAT(EADS.Total_Activity_Duration,"%H:%i") as Total_Activity_Duration'),
                    organizationDbConnection.raw('TIME_FORMAT(EADS.Active_Duration,"%H:%i") as Active_Duration'),
                    organizationDbConnection.raw('(IF(EADS.Not_Active_Duration_In_Minutes>0, EADS.Not_Active_Duration_In_Minutes, 0)+IF(EADS.Idle_Duration_In_Minutes>0, EADS.Idle_Duration_In_Minutes, 0)) as timeSpentOnIdleAndNotActiveInMinutes'),
                    organizationDbConnection.raw('TIME_FORMAT(SEC_TO_TIME((IF(time_to_sec( EADS.Not_Active_Duration)>0, time_to_sec( EADS.Not_Active_Duration), 0)+IF(time_to_sec(EADS.Idle_Duration)>0, time_to_sec(EADS.Idle_Duration), 0))),"%H:%i") as timeSpentOnIdleAndNotActive'),
                    organizationDbConnection.raw('TIME_FORMAT(EADS.Productive_Activity_Duration,"%H:%i") as Productive_Activity_Duration'),
                    organizationDbConnection.raw('TIME_FORMAT(PDS.Productive_Duration_In_HHMMSS,"%H:%i") as Productive_Duration_In_HHMM'),
                    organizationDbConnection.raw('TIME_FORMAT(PDS.Unproductive_Duration_In_HHMMSS,"%H:%i") as Unproductive_Duration_In_HHMM'),
                    organizationDbConnection.raw('TIME_FORMAT(PDS.Neutral_Duration_In_HHMMSS,"%H:%i") as Neutral_Duration_In_HHMM'),
                    organizationDbConnection.raw('CASE WHEN EADS.Activity_Start_Hour IS NOT NULL THEN TIME_FORMAT(EADS.Activity_Start_Hour,"%H:%i") ELSE "" END as Activity_Start_Hour'),
                    organizationDbConnection.raw('CASE WHEN EADS.Activity_End_Hour IS NOT NULL THEN TIME_FORMAT(EADS.Activity_End_Hour,"%H:%i") ELSE "" END as Activity_End_Hour'),
                    'EADS.Active_Duration_In_Minutes', 'EADS.Productive_Activity_Duration_In_Minutes', 'PDS.Productive_Duration_In_Minutes',
                    organizationDbConnection.raw('ROUND(EADS.Sys_Productivity_Percentage_Without_IdleTime) as systemProductivityPercentageWithoutIdleTime'), 'PDS.User_Productivity_Percentage',
                    organizationDbConnection.raw('ROUND(EADS.Sys_Productivity_Percentage_With_IdleTime) as systemProductivityPercentageWithIdleTime'),
                    organizationDbConnection.raw('ROUND(EADS.Sys_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours) as systemProductivityPercentageBasedOnFDWH'),
                    'PDS.User_Productivity_Percentage_Based_On_Fixed_Daily_Work_Hours as User_Productivity_Percentage_FDWH')
                .from(ehrTables.employeeActivityDailySummary + ' as EADS')
                .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EADS.Employee_Id')
                .leftJoin(ehrTables.appurlActivityDailySummary + ' as PDS', function () {
                    this.on('EADS.Employee_Id', 'PDS.Employee_Id')
                        .andOn(function () {
                            this.on('EADS.Activity_Date', 'PDS.Activity_Date');
                        })
                })
                .where('EADS.Activity_Date', activityDate)
                .whereIn('EADS.Employee_Id', employeeIds)
                .then(async (getSummaryData) => {
                    return getSummaryData;
                })
                .catch(getSummaryError => {
                    console.log('Error while retrieving the employee activity daily summary details.', getSummaryError);
                    return [];
                })
        )
    } catch (getAttendanceSummaryDetailsError) {
        console.log('Error in getAttendanceSummaryDetails() function main catch block', getAttendanceSummaryDetailsError);
        return [];
    }
}

function convertObjectKeyToAnotherKeyValue(data, keyMapping) {
    const keyMap = Array.isArray(keyMapping) ?
        Object.fromEntries(keyMapping.map(({ key, value }) => [key, value])) : keyMapping;

    if (Array.isArray(data)) {
        return data.map(item => convertObjectKeyToAnotherKeyValue(item, keyMap));
    } else if (typeof data === "object" && data !== null) {
        return Object.keys(data).reduce((newObj, key) => {
            const newKey = keyMap[key]; 
            newKey && data[key] ? newObj[newKey] = data[key] : null;
            return newObj;
        }, {});
    }
    return data;
}


async function updateContactInfo(organizationDbConnection, trx, contactDetail, employeeId) {

    try {

        return (
            organizationDbConnection(ehrTables.contactDetails).transacting(trx)
            .update(contactDetail).where('Employee_Id', employeeId)
            .then(async (updateContact) => {
                if (updateContact) {
                    return 'success'
                } else {
                    console.error('Error while updating the contact details', contactDetail)
                    throw 'An error occurred while updating the contact details. Please try again.'
                }
            })
        )
    } catch (error) {
        console.error("Error while contact details main catch block => ", error);
        throw error;
    }                
}


async function updatePersonalInfo(organizationDbConnection, trx, personalInfo, employeeId) {

    try {

        if(personalInfo && Object.entries(personalInfo).length) {

            if(personalInfo.PAN){
                let existPersonalInfo = await organizationDbConnection(ehrTables.empPersonalInfo)
                .transacting(trx).select('PAN', 'Personal_Email', 'Statutory_Insurance_Number', 'PRAN_No', 'UAN')
                .modify((queryBuilder) => {
                    queryBuilder.where((qb) => {
                        if (personalInfo.PAN) {
                            qb.orWhere('PAN', personalInfo.PAN);
                        }
                        if (personalInfo.Personal_Email) {
                            qb.orWhere('Personal_Email', personalInfo.Personal_Email);
                        }
                        if (personalInfo.Statutory_Insurance_Number) {
                            qb.orWhere('Statutory_Insurance_Number', personalInfo.Statutory_Insurance_Number);
                        }
                        if (personalInfo.PRAN_No) {
                            qb.orWhere('PRAN_No', personalInfo.PRAN_No);
                        }
                        if (personalInfo.UAN) {
                            qb.orWhere('UAN', personalInfo.UAN);
                        }
                    });
                    queryBuilder.whereNot('Employee_Id', employeeId)
                   
                })

                if(existPersonalInfo && existPersonalInfo.length){
                    throw `The provided Tax Identification Number / Personal MailId / Phil HealthId / HDMF ID / SSS Number is any one of the employee already associated with another employee.`;
                }
            }
          
            return organizationDbConnection(ehrTables.empPersonalInfo)
            .transacting(trx).update(personalInfo)
            .where('Employee_Id', employeeId)
        }

    } catch (error) {
        console.error("Error updatePersonalInfo function catch block => ", error);
        throw error;
    }
}

async function initiateCustomGroupRefresh(orgCode, customGroupRefreshEmployees, dateOfJoinChangedEmployees) {
    try {
      if (customGroupRefreshEmployees.length || dateOfJoinChangedEmployees.length) {
        if (customGroupRefreshEmployees.length > 0) {
          let customGroupInputParams = {
            orgCode: orgCode,
            employeeId: customGroupRefreshEmployees,
            logInEmpId: 1,
            isCustomGroupRefresh: 1,
          }
          await commonLib.stepFunctions.triggerStepFunction(process.env.refreshCustomGroupStateMachineArn, 'refreshCustomGroup', '', customGroupInputParams);
        }
  
        if (dateOfJoinChangedEmployees.length > 0) {
          let dateOfJoinInputParams = {
            orgCode: orgCode,
            employeeId: dateOfJoinChangedEmployees,
            logInEmpId: 1,
            isDOJUpdated: true,
          }
          await commonLib.stepFunctions.triggerStepFunction(process.env.refreshCustomGroupStateMachineArn, 'refreshCustomGroup', '', dateOfJoinInputParams);
        }
      }
    }
    catch (error) {
      console.log('Error in initiateCustomGroupRefresh function catch block.', error);
      throw error;
    }
}


module.exports.getSummarizationDateBasedOnZone = getSummarizationDateBasedOnZone;
module.exports.updateStatusInOrganizationStatusTable = updateStatusInOrganizationStatusTable;
module.exports.updateSummaryStatusBasedOnId = updateSummaryStatusBasedOnId;
module.exports.insertRecordForFailedWSData = insertRecordForFailedWSData;
module.exports.insertNewWorkScheduleData = insertNewWorkScheduleData;
module.exports.getWorkScheduleData = getWorkScheduleData;
module.exports.getAttendanceSummaryDetails = getAttendanceSummaryDetails;
module.exports.removeUserFromAppManager = removeUserFromAppManager;
module.exports.triggerSummarizationLambda = triggerSummarizationLambda;
module.exports.calculateActivityBasedOnActiveStatus = calculateActivityBasedOnActiveStatus;
module.exports.calculateActivityDateBasedOnZone = calculateActivityDateBasedOnZone;
module.exports.deleteTempFolderFiles = deleteTempFolderFiles;
module.exports.removeBulkInstancesFromAppManager = removeBulkInstancesFromAppManager;
module.exports.getDataFromMasterTableAccordingToStatus = getDataFromMasterTableAccordingToStatus;
module.exports.updateInMasterTable = updateInMasterTable;
module.exports.updatePersonalInfo=updatePersonalInfo;
module.exports.updateContactInfo=updateContactInfo;
module.exports.convertObjectKeyToAnotherKeyValue=convertObjectKeyToAnotherKeyValue;
module.exports.initiateCustomGroupRefresh = initiateCustomGroupRefresh;
