'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const commonFunction = require('../common/initiateStepFunction');

// Function to initiateFileTransferDailySummary step function
module.exports.initiateFileTransferDailySummary  = async(event, context) =>{
    try{
        console.log('Inside initiateFileTransferDailySummary function',event);
        // based on event define the status
        // We will be triggering the step function in 2 different cases.
        let status;
        if(!event.status)
        {
            status='';
        }
        else if(event.status==='Open')
        {
            status='Open';
        }
        else{
            status='Failed';
        }
        // based on status we will check weither to update the table for app and url purge in manage db.
        let triggerFileTransferDailySummaryStepFunction= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'fileTransfer',status);
        console.log('Response after triggering FileTransferDailySummaryStepFunction step function',triggerFileTransferDailySummaryStepFunction);


        return {errorCode:'',message: 'initiateFileTransferDailySummary initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateFileTransferDailySummary function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EM0061');
        return {errorCode:errResult.code,message: errResult.message};
    }
};