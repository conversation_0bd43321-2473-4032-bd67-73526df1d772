'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const commonFunction = require('../common/initiateStepFunction');

// Function to initiate daily summary step function
module.exports.initiateStateMachineStepFunction  = async(event, context) =>{
    try{

        console.log('Inside initiateStateMachineStepFunction function', event);
       
        if(event.schedule === 1){

            let inputParams = {
                source: 'Master',
                frequency: 'Daily',
                status: 'Open'
            }
            // based on status we will check weither to update the table for app and url purge in manage db.
            let triggerActivity= await commonFunction.triggerStepFunction(process.env.stateMachineArn, 'Master', 'Open', inputParams);
            console.log('Response after triggering getPagtIntegration daily Sync master and employee step function', triggerActivity);
            return response;

        } else if(event.schedule === 2){

            let inputParams = {
                source: 'OnboardStatus',
                frequency: 'Weekly',
                status: 'Open'
            }
            let triggerActivity= await commonFunction.triggerStepFunction(process.env.stateMachineArn, 'OnboardStatus', 'Open', inputParams);
            console.log('Response after triggering getPagtIntegration weekly push OnboardStatus step function',triggerActivity);
        }  else if(event.schedule === 3){

            let inputParams = {
                source: 'AirTicket',
                frequency: 'Monthly',
                status: 'Open'
            }
            let triggerActivity= await commonFunction.triggerStepFunction(process.env.airTicketStateMachineArn, 'AirTicket', 'Open', inputParams);
            console.log('Response after triggering generate monthly air ticket summary step function',triggerActivity);
        }

        return {errorCode:'',message: 'triggered step function initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateStateMachineStepFunction function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EM0061');
        return {errorCode:errResult.code,message: errResult.message};
    }
};

