// require table alias
const { ehrTables } = require('./tableAlias');
const reportCommonFunction = require('../common/reportCommonFunction');

// Function to form report data based on input report type
async function getReportData(orgDbConnection,startDate,endDate,reportType,appManagerDbConnection=null,orgCode=null) {
    try{
        let reportData=[];

        if(reportType.toUpperCase()==='EMPSR' || reportType.toUpperCase()==='EMPUSR' ||
        reportType.toUpperCase()==='EMCAUR' || reportType.toUpperCase()==='EMAR') {
            // get the active employeeId
            let employeeIdsArray=await getActiveEmployeeIds(orgDbConnection);
            // check employeeId array exist or not
            if(employeeIdsArray.length<0){
                throw 'No active employees exist.';
            }
            else{
                /** If report type is EMPSR then get the productivity summary report */
                if(reportType.toUpperCase()==='EMPSR'){
                    let reportDetailsArgs = {employeeIdsArray,startDate,endDate,source:'dataintegration'};
                    reportData=await reportCommonFunction.productivitySummaryReport(orgDbConnection,reportDetailsArgs);
                }
                /** If report type is EMPUSR then get the productivity and unproductivity summary report */
                else if(reportType.toUpperCase()==='EMPUSR'){
                    let reportDetailsArgs = {employeeIdsArray,startDate,endDate,source:'dataintegration'};
                    reportData=await reportCommonFunction.getproductiveUnproductiveSummaryReport(orgDbConnection,appManagerDbConnection,reportDetailsArgs,orgCode);
                }
                /** If report type is EMCAUR then get the Consolidated Apps and URLs report */
                else if(reportType.toUpperCase()==='EMCAUR'){
                    let reportDetailsArgs = {employeeIdsArray,startDate,endDate,source:'dataintegration'};
                    reportData=await reportCommonFunction.getConsolidatedAppsUrlsReport(orgDbConnection,reportDetailsArgs);
                }
                /** If report type is EMAR then get the Asset report */
                else if(reportType.toUpperCase()==='EMAR'){
                    let reportDetailsArgs = {employeeIdsArray,startDate,endDate,source:'dataintegration'};
                    reportData=await reportCommonFunction.getAssetReport(orgDbConnection,reportDetailsArgs);
                }
                else{
                    throw 'Invalid report type.';
                }
            }
        } /** If the report type is HREAR then get the employee record which are added between the start and end date */
        else if(reportType.toUpperCase()==='HREAR'){
            reportData=await reportCommonFunction.getEmployeesReport(orgDbConnection, reportType, startDate, endDate, 'Add');
        }/** If the report type is HREUR then get the employee record which are updated between the start and end date */
        else if(reportType.toUpperCase()==='HREUR'){
            reportData=await reportCommonFunction.getEmployeesReport(orgDbConnection, reportType, startDate, endDate, 'Update');
        }
        /** If the report type is HRLR then get the employee leave record which are approved/cancelled between the start and end date */
        else if(reportType.toUpperCase()==='HRLR'){
            reportData=await reportCommonFunction.getEmployeeLeaveReport(orgDbConnection, reportType, startDate, endDate);
        }
        /** If the report type is HRRR then get the employee resignation record which are added(resignation date) between the start and end date */
        else if(reportType.toUpperCase()==='HRRR'){
            reportData=await reportCommonFunction.getEmployeeResignationReport(orgDbConnection, reportType, startDate, endDate);
        }
        else if(reportType.toUpperCase()==='BID'){
            reportData=await reportCommonFunction.rmsDataExportEntomoDashboard(orgDbConnection, reportType, startDate, endDate);
        }
        else if(reportType.toUpperCase()==='MPP'){
            reportData=await reportCommonFunction.manPowerPlanningDataExport(orgDbConnection, reportType, startDate, endDate);
        }
        else{
            throw 'Invalid report type.';
        }
        return reportData;
    }
    catch(catchError){
        console.log('Error in getReportData function main catch block',catchError);
        let errorMessageArray=['No active employees exist.','Error in getting active employeeId.','No activity data exists.','Error in calculating report data.','Invalid report type.'];
        let errorResponse=(errorMessageArray.includes(catchError))?(catchError):('Error in forming report data.');
        throw errorResponse;
    }
};

// function to get active employeeId
async function getActiveEmployeeIds(orgDbConnection){
    try{
        return(
            orgDbConnection(ehrTables.teamMembers)
            .pluck('Employee_Id')
            .where('Member_Status', 'Active')
            .then(getId =>{
                return getId;
            })
            .catch(error => {
                console.log('Error in getActiveEmployeeIds function .catch block.',error);
                throw 'Error in getting active employeeId.';
            })
        );
    }
    catch (catchError) {
        console.log('Error in getActiveEmployeeIds function main catch block', catchError);
        throw 'Error in getting active employeeId';
    }
};

module.exports.getReportData = getReportData;