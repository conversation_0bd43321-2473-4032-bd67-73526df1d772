// Organization database connection
const knex = require('knex');
// require table alias
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { defaultValues } = require('../common/appConstants');
const moment = require('moment-timezone');
const { updateSummaryDataInMasterTable }=require('./commonFunctions');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//main function to do app purge which are not used from 3 months
module.exports.appPurge  = async(event, context) =>{
    let appmanagerDbConnection;
    try{
        let purgeMinDate=moment().subtract(3, "months").startOf("month").format("YYYY-MM-DD");
        let instance;
        let connection = await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        if(Object.keys(connection).length){
            // form app manager database connection
            appmanagerDbConnection=knex(connection.AppManagerDb);
            instance=await getOpenInstances(appmanagerDbConnection);
        }
        if(instance.length>0)
        {
            let instanceToBeProcessed=(instance.length>defaultValues.empMonitorInstanceCount)?(defaultValues.empMonitorInstanceCount):(instance.length);
            for(let i=0;i<instanceToBeProcessed;i++)
            {   
                let updateMasterTableStatus;
                let orgCode=instance[i]['Org_Code'];
                let updateParams={
                    Purge_Status : 'InProgress'
                }
                await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.appPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                
                let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
                if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                    let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                    

                    let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                    
                    if(Object.keys(connection).length)
                    {
                        let check=await purgeAppData(connection,purgeMinDate);
                        if(check)
                        {
                            updateParams={
                                Purge_Status : 'Success'
                            }
                            updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.appPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                        }
                        else
                        {
                            updateParams={
                                Purge_Status : 'Failed'
                            }
                            updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.appPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                        }
                        
                    }
                    else{
                        updateParams={
                            Purge_Status : 'Failed'
                        }
                        updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.appPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                    }
                    if(updateMasterTableStatus!=="success"){
                        console.log("Error Occured in updateSummaryDataInMasterTable while updating app_purge_manager table")
                    }
                } else {
                    console.log("Could not get the data region details");
                    updateParams={
                        Purge_Status : 'Failed'
                    }
                    updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.appPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                    if(updateMasterTableStatus!=="success"){
                        console.log("Error Occured in updateSummaryDataInMasterTable while updating app_purge_manager table")
                    }
                }
            }
            let response = 
            {
                nextStep:'End',
                message:'Purge process Completed.'
            };
            appmanagerDbConnection?appmanagerDbConnection.destroy():null;
            return response;
        }
        else{
            console.log("No instances Found");
            let response = 
            {
                nextStep:'End',
                message:'Purge process Completed.'
            };
            appmanagerDbConnection?appmanagerDbConnection.destroy():null;
            return response;
        }
        
 }
catch(e){
    console.log("error occure while doing the app purge",e);
    let response = {
                nextStep:'End',
                message:'Error Occured while doing App Purge .'
            };
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        return response;
    }
};

//function to get all open instances from app_purge_manager table
async function getOpenInstances(appmanagerDbConnection)
{
    try{
        return(     
        appmanagerDbConnection(appManagerTables.appPurgeManager)
        .select('Org_Code')
        .where('Purge_Status','open')
        .then(async (summaryData) =>{
            return summaryData;
        })
        .catch((e)=>{
            console.log("error while geting Open Instanaces",e);
            return "";
        })
        )
    }
    catch(e){
        console.log("error in get Open Instances",e);
        return "";
    }
}


//function to purge app data which are not use from past 3 months
async function purgeAppData(connection,purgeMinDate)
{
    let organizationDbConnection;
    try{
        organizationDbConnection = knex(connection.OrganizationDb);
        return(
            organizationDbConnection.select('EAA.Application_Id as applicationId',
            organizationDbConnection.raw('CASE WHEN CGAS.Category THEN CGAS.Category ELSE "Uncategorized" END as category1'),
            organizationDbConnection.raw('CASE WHEN OLAS.Category THEN OLAS.Category ELSE "Uncategorized" END as category2'))
            .max('EAA.Load_TimeStamp',{as:'timeStamp'})
            .from(ehrTables.empActivityApps +' as EAA')
            .leftJoin(ehrTables.customGroupAppSettings+" as CGAS","EAA.Application_Id","CGAS.Application_Id")
            .leftJoin(ehrTables.organizationLevelAppSettings+" as OLAS","EAA.Application_Id","OLAS.Application_Id")
            .groupBy("EAA.Application_Id")
            .having('category1','=','Uncategorized')
            .having('category2','=','Uncategorized')
            .having('timeStamp','<',purgeMinDate + ' 00:00:00')
            .then(async(data)=>
            {
                let applicationId=[];
                if(data.length>0)
                {
                    data.forEach(i=>{
                        if(i['applicationId']!=null)
                        {   
                            applicationId.push(i['applicationId']);
                        }
                    })
                }
                if(applicationId.length>0)
                {
                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            return(
                                organizationDbConnection(ehrTables.empActivityApps)
                                .del()
                                .whereIn('Application_Id', applicationId)
                                .transacting(trx)
                                .then(()=>
                                    {
                                        return(
                                            organizationDbConnection(ehrTables.appActivityDailySummary)
                                            .whereIn('Application_Id', applicationId)
                                            .del()
                                            .transacting(trx)
                                            .then(()=>{
                                                return(
                                                    organizationDbConnection(ehrTables.activityAppMaster)
                                                    .whereIn('Application_Id', applicationId)
                                                    .del()
                                                    .transacting(trx)
                                                    .then(()=>{
                                                        organizationDbConnection?organizationDbConnection.destroy():null;
                                                        return true;
                                                    })
                                                )
                                            })
                                        )
                                    }
                                )
                            )
                            
                    })
                    .then(()=>{
                        organizationDbConnection?organizationDbConnection.destroy():null;
                        return true;
                    })
                    .catch((e)=>{
                        console.log("error occurd while doing the app purge",e);
                        organizationDbConnection?organizationDbConnection.destroy():null;
                        return false;
                    })
                    )
                }
                else
                {
                    organizationDbConnection?organizationDbConnection.destroy():null;
                    return true;
                }
            
            })
            .catch(e=>{
                    console.log("error while getting app id to be purged",e);
                    organizationDbConnection?organizationDbConnection.destroy():null;
                    return false;
            })
        )
    }
    catch(e){
        console.log("Error Occured while doing app_Purge",e);
        organizationDbConnection?organizationDbConnection.destroy():null;
        return false;
    }
}

