const handlebars = require('handlebars');
const path = require('path');
const fs = require('fs');
const moment = require('moment-timezone');

async function getTemplateData(inputParams) {
    try {
        const { decode } = require('html-entities');
        const templateFilePath = path.join(__dirname, '/notificationEmail.html');
        const templatedata = await fs.promises.readFile(templateFilePath, 'utf8');
        const template = handlebars.compile(templatedata);
        let htmlToSend = template(inputParams);
        htmlToSend = decode(htmlToSend);

        let attachmentsDetails = []
        if(inputParams.pdfData){
            attachmentsDetails = [{
                filename: inputParams.filename,
                content: Buffer.from(inputParams.pdfData, 'base64')
            }];
        }

        return { htmlToSend, attachmentsDetails };
    } catch (error) {
        console.log('Error in getTemplateData function main catch block.', error);
        return {};
    }
}

async function sendEmailWithReportAttachment(transporter, mailOptions) {
    try {
        const info = await transporter.sendMail(mailOptions);
        console.log('Mail was sent successfully', info);
        return 'success';
    } catch (error) {
        console.log('Error in sendEmailWithReportAttachment function catch block.', error);
        return false
    }
}

module.exports.getTemplateData=getTemplateData;
module.exports.sendEmailWithReportAttachment=sendEmailWithReportAttachment;
