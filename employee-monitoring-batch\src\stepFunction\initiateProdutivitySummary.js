'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require constant values
const { formIds,defaultValues } = require('../common/appConstants');
// require common function
const { getSummarizationDateBasedOnZone,removeUserFromAppManager,insertSummaryDataInMasterTable,updateSummaryDataInMasterTable,removeBulkInstancesFromAppManager }=require('./commonFunctions');

// variable declarations
let inputStatus;
let appmanagerDbConnection='';
let orgCode='';
let summarizationDate;
let inputSource;

// function to get the subscribed users for which need to summarize the user productivity activities
module.exports.initiateProdutivitySummary  = async(event, context) =>{
    try{
        console.log('Inside initiateProdutivitySummary function',event);
        // get input data
        inputStatus=event.status;
        inputSource=event.source;
        // if input status is open then proceed to step2
        if(inputStatus && inputStatus.toLowerCase()==='open')
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.
            Incase of input status as 'Open' proceed to the summarization process */
            console.log('Event triggered to process remaining records so move to step2');
            let response={
                nextStep:'Step2',
                input:{'process':inputSource,'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            if(Object.keys(databaseConnection).length>0){
                // form app manager database connection
                appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                /** When input status is empty.Then get the emp monitoring active users and insert into status and manager table  */
                if(!inputStatus)
                {
                    // get employee monitoring plan subscribed active users
                    let empMonitoringSubscribedUsers= await commonLib.func.getInstanceSubscribedToFormId(formIds.activityTracker,appmanagerDbConnection,defaultValues.activeStatus);
                    console.log('Employee monitoring subscribed user count',empMonitoringSubscribedUsers.length);
                    //Delete the instances from mastertable where the orgcode is not in active employee monitoring plan
                    await removeBulkInstancesFromAppManager(appmanagerDbConnection,empMonitoringSubscribedUsers,appManagerTables.appurlActivitySummarizationManager);
                    if(empMonitoringSubscribedUsers.length>0)
                    {
                        // get the orgcode and date from the manager table
                        let getRecord=await appmanagerDbConnection(appManagerTables.appurlActivitySummarizationManager)
                        .select('Org_Code','AppUrl_Summarization_Date as date')
                        .whereIn('Org_Code',empMonitoringSubscribedUsers)
                        .then(async (orgcodeExist) =>{
                            return orgcodeExist;
                        })
                        // iterate the loop for all the subscribed users
                        for(let i = 0; i < empMonitoringSubscribedUsers.length; i++){
                            orgCode=empMonitoringSubscribedUsers[i];
                            console.log('Processing the instance - ',orgCode);
                            // get the record which is associated with the orgcode
                            let getInstanceRecord=getRecord.find(i=>i.Org_Code===orgCode);
                            // check whether orgcode already exist in manager table
                            let isOrgCodeExist=(getInstanceRecord && Object.keys(getInstanceRecord).length);
                            /** form the summarization date */
                            if(isOrgCodeExist){
                                // summarizationDate=getInstanceRecord.date;
                                // summarizationDate=moment(summarizationDate).add(1,'d').format('YYYY-MM-DD');
                                summarizationDate = moment.tz(moment(),defaultValues.defaultTimeZone).format('YYYY-MM-DD');
                            }
                            else{
                                // form the summarization date based on the default timezone
                                summarizationDate=await getSummarizationDateBasedOnZone(defaultValues.defaultTimeZone);
                            }
                            // insert record in status table and insert/update the data in manager table
                            let insertSummaryData=await checkActiveMemberAndInsertSummaryData(appmanagerDbConnection,orgCode,isOrgCodeExist,summarizationDate);
                            console.log('Response from checkActiveMemberAndInsertSummaryData function - ',insertSummaryData);
                        }
                        // return to the step2 to process the summary process
                        let response={
                            nextStep:'Step2',
                            input:{'process':inputSource,'status':inputStatus},
                            message:'Record inserted successfully proceed the productivity summarization process.'          
                        }
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        return response;
                    }
                    else{
                        console.log('There is no employee monitoring subscribed active user exist.So quit the execution.');
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'process':inputSource,'status':inputStatus},
                            message:'There is no employee monitoring subscribed active users.'
                        };
                        return response;
                    }        
                }
            }
            else{
                console.log('Error while creating app manager database connection in step1');
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response = 
                {
                    nextStep:'Error',
                    input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error creating app manager database connection from step1.'},
                    message:'Error in creating app manager database connection.'
                };
                return response;
            }
        }
    }
    catch (mainCatchError){
        console.log('Error in initiateProdutivitySummary function main catch block.', mainCatchError);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response = 
        {
            nextStep:'Error',
            input:{'process':inputSource,'status':inputStatus,'orgCode':orgCode,'reason':'Error from step1 main catch block.'},
            message:'Error from step1 main catch block'
        };
        return response;
    }
}

// function to check whether active member exist and insert new summary record
async function checkActiveMemberAndInsertSummaryData(appmanagerDbConnection,orgCode,isOrgCodeExist,summarizationDate){
    console.log("checkActiveMemberAndInsertSummaryData: ",orgCode,isOrgCodeExist,summarizationDate);
    let orgDbConnection='';
    try{
        //Form additional headers
        let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
        if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
            let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
            

            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
            if(Object.keys(databaseConnection).length){
                orgDbConnection = knex(databaseConnection.OrganizationDb);
                return(
                    orgDbConnection
                    .transaction(function(trx){
                        // Check whether any active members exist in organization or not
                        return(
                            orgDbConnection(ehrTables.teamMembers)
                            .count('Employee_Id as count')
                            .where('Member_Status','Active')
                            .transacting(trx)
                            .then(async (memberCount) =>{
                                // if members exist then insert the record in status table and update the status in master table
                                if (memberCount[0].count>0){
                                    console.log('Active members exist in '+ orgCode+ ' instance');
                                    /** Process the record till the current date. So validate the date based on current date */
                                    let currentISTDate= moment.tz(moment(),defaultValues.defaultTimeZone).format('YYYY-MM-DD');
                                    // calculate the date difference between current date and the last summary date(IST)
                                    let dateDifference = moment(currentISTDate).diff(moment(summarizationDate), 'days');
                                    console.log('dateDifference',dateDifference)
                                    if(dateDifference>=0){
                                        return(
                                            // check whether any records are in either open or inprogress status
                                            orgDbConnection(ehrTables.appurlActivitySummarizationStatus)
                                            .count('AppUrl_Summarization_Id as summaryRecordCount')
                                            .whereIn('Transaction_Status',['Open','Inprogress'])
                                            .transacting(trx)
                                            .then(async (getData) =>{
                                                /** If record does not exist then insert the new open record in status table */
                                                if(getData[0].summaryRecordCount===0){

                                                    let summaryExists = await 
                                                        // check whether any records are in either open or inprogress status
                                                        orgDbConnection(ehrTables.appurlActivitySummarizationStatus)
                                                        .pluck('AppUrl_Summarization_Id')
                                                        .whereIn('Transaction_Status',['Success','Failed'])
                                                        .where('AppUrl_Summarization_Date', summarizationDate)
                                                        .transacting(trx)
                                                        .then(async (summaryData) =>{
                                                            console.log("SummaryData: ", summaryData);
                                                            return summaryData && summaryData.length > 0 ? true : false;
                                                        });

                                                    console.log('summaryExists: ', summaryExists);
                                                    let updateSummaryDetails = false;
                                                    if(summaryExists){
                                                        let updateParams={
                                                            Transaction_Status:'Open',
                                                            AppUrl_Summarization_Date:summarizationDate
                                                        }
                                                        updateSummaryDetails = await commonLib.func.updateTableBasedOnCondition(orgDbConnection,ehrTables.appurlActivitySummarizationStatus,updateParams,'AppUrl_Summarization_Date',summarizationDate);
                                                        
                                                    }
                                                    else{
                                                        let insertParams={
                                                            Transaction_Status:'Open',
                                                            AppUrl_Summarization_Date:summarizationDate,
                                                            Summarization_Time:new Date()
                                                        }
                                                        updateSummaryDetails=await insertSummaryDataInMasterTable(orgDbConnection,insertParams,ehrTables.appurlActivitySummarizationStatus);
                                                    }

                                                    console.log("updateSummaryDetails: ", updateSummaryDetails);

                                                    // return(
                                                    //     // insert record in summarization status table
                                                    //     orgDbConnection(ehrTables.appurlActivitySummarizationStatus)
                                                    //     .insert({
                                                    //         AppUrl_Summarization_Date:summarizationDate,
                                                    //         Transaction_Status:'Open',
                                                    //         Summarization_Time:new Date()
                                                    //     })
                                                    //     .transacting(trx)
                                                    //     .then(async (insertSummary) =>{
                                                            if(updateSummaryDetails){
                                                                console.log('record inserted in appurl status table for -',summarizationDate);
                                                                /** If the orgcode is already exist then update the date and status in manager table. 
                                                                 * If record does not exist then insert the new record for thr orgcode in manager table
                                                                 */
                                                                if(isOrgCodeExist){
                                                                    let updateParams={
                                                                        Status:'Open',
                                                                        AppUrl_Summarization_Date:summarizationDate
                                                                    }
                                                                    let updateStatus=await updateSummaryDataInMasterTable(appmanagerDbConnection,updateParams,orgCode,appManagerTables.appurlActivitySummarizationManager);
                                                                    return updateStatus;
                                                                }
                                                                else{
                                                                    let insertParams={
                                                                        Org_Code:orgCode,
                                                                        Status:'Open',
                                                                        AppUrl_Summarization_Date:summarizationDate
                                                                    }
                                                                    let insertRecord=await insertSummaryDataInMasterTable(appmanagerDbConnection,insertParams,appManagerTables.appurlActivitySummarizationManager);
                                                                    return insertRecord;
                                                                }
                                                            }
                                                            else{
                                                                console.log('Error in updating/inserting the summarization status table');
                                                                return 'error'
                                                            }
                                                    //     })
                                                    // )
                                                }
                                                else{
                                                    console.log('There are some records in either open or inprogress status. So no need to insert new record.',getData);
                                                    return 'success';            
                                                }
                                            })
                                        )
                                    }
                                    else{
                                        console.log('Summarization is already completed',dateDifference);
                                        return 'success';
                                    }
                                }
                                else{
                                    console.log('There is no active members in '+orgCode+ ' instance.So remove the record in manager table and process the next record.');
                                    // function to remove user from the manager table
                                    await removeUserFromAppManager(appmanagerDbConnection,orgCode,appManagerTables.appurlActivitySummarizationManager);
                                    return 'No active members found.';
                                }
                            })
                        )
                    })
                    .then(function (result) {
                        return result;
                    }) 
                    .catch(function (catchError) {
                        console.log('Error in checkActiveMemberAndInsertSummaryData function .catch block',catchError);
                        return 'error';
                    })
                    .finally(() => {
                        orgDbConnection.destroy();
                    })
                );
            }
            else{
                console.log('Error while connecting organization database '+orgCode+' instance');
                return 'error';
            }
        } else{
            console.log('Error while getting the data region for '+orgCode+' instance');
            return 'error';
        }
    }
    catch(error){
        console.log('Error in checkActiveMemberAndInsertSummaryData function main catch block',error);
        orgDbConnection?orgDbConnection.destroy():null;
        return 'error';
    }
};
    