'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const commonFunction = require('../common/initiateStepFunction');

// Function to initiate maintainWeekOffDate step function
module.exports.initiateMaintainWeekOff  = async(event, context) =>{
    try{
        console.log('Inside initiateMaintainWeekOff',event);
        // based on event define the status
        // We will be triggering the step function in 2 different cases.
        // We will trigger maintainWeekOff step function and separate event for retrying the failed instance so based on input differentiate the parms
        let status=(!event.status)?'':(event.status==='Open')?'Open':'Failed';
        let triggerMaintainWeekOffDate= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'weekoffdate',status);
        console.log('Response after triggering triggerMaintainWeekOffDate step function',triggerMaintainWeekOffDate);

        return {errorCode:'',message: 'MaintainWeekOffDate step function initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateMaintainWeekOffDateStepFunction function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'EM0061');
        return {errorCode:errResult.code,message: errResult.message};
    }
};


