// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const {getDataRetentionSettings}=require('./activityCommonFunctions');
// require validation file
const { validateInputDate }=require('./reportsInputValidation');
const commonValidation = require('./commonValidation');

/** Function to validate activity summary, app usage summary, and 
 * url summary inputs.
 * @param {JSON} args - INPUT JSON
 * @param {Date} date - Activity date
 * @param {Date} callFromMyTeamActivityForm - If 1, it is called from
 * my team activity tab. Otherwise it is called from my activity tab.
 * @param {String} employeeId - Employee Id
 * @param {String} zone - Time zone 
 * @param {Number} workScheduleId - WorkSchedule Id
 * @returns {JSON} - Returns a validation error JSON
 * @throws {JSON} - Throws an error JSON if an error occurred
 */
async function validateSummaryInputs(organizationDbConnection,args){
    try{
        let validationError =  {};
        // validate the input date
        if(!args.date){
            validationError['IVE0044'] = commonLib.func.getError('', 'IVE0044').message;
        }
        else{
            // get data retention period from settings
            let period=await getDataRetentionSettings(organizationDbConnection);
            // validate the input date
            validationError=await validateInputDate(args.date,period);
        }
        /** If request from my team activity form then employeeId is mandatory */
        if (args.callFromMyTeamActivityForm){
            if(!args.employeeId){
                validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
            }
        }
        //Validate time zone and work schedule
        validationError = await validateTimezoneWorkSchedule(args,validationError);

        return validationError;
    }catch(summaryInputsCatchError){
        console.log('Error in the validateSummaryInputs() function main catch block.',summaryInputsCatchError);
        throw summaryInputsCatchError;
    }
}

/** Function to validate time zone and work schedule
 * @param {JSON} args - INPUT JSON
 * @param {String} timeZone - Time zone 
 * @param {Number} workScheduleId - WorkSchedule Id
 * @param { Number } validateZoneId - Decide whether input timezone is string or Int
 * @returns {JSON} - Returns a validation error JSON
 * @throws {JSON} - Throws an error JSON if an error occurred
 */
async function validateTimezoneWorkSchedule(args,validationError,validateZoneId=null){
    try{
        if(validateZoneId){
            //validate timezone
            if(!args.timeZoneId){
                validationError['IVE0194'] = commonLib.func.getError('', 'IVE0194').message2;
            }
        }
        else{
            //validate timezone
            if(!args.timeZone){
                validationError['IVE0194'] = commonLib.func.getError('', 'IVE0194').message3;
            }
        }
        //validate work schedule id
        if(args.workScheduleId){
            if (commonValidation.numberValidation(args.workScheduleId) === false){
                validationError['IVE0150'] = commonLib.func.getError('', 'IVE0150').message;
            }
        }
        return validationError;
    }catch(inputsCatchError){
        console.log('Error in the validateTimezoneWorkSchedule() function main catch block.',inputsCatchError);
        throw inputsCatchError;
    }
}

/** Function to validate dashboard endpoints common inputs
 * @param {JSON} args - INPUT JSON
 * @param {String} timeZone - Time zone 
 * @param {Number} workScheduleId - WorkSchedule Id
 * @returns {JSON} - Returns a validation error JSON
 * @throws {JSON} - Throws an error JSON if an error occurred
 */
 async function validateDashboardCommonInputs(args){
    try{
        let validationError =  {};
        //Validate call from team dashboard value
        if(!commonValidation.booleanNumberValidation(args.callFromTeamDashboard)){
            validationError['IVE0207'] = commonLib.func.getError('', 'IVE0207').message;
        }
        //Validate time zone and work schedule
        validationError = await validateTimezoneWorkSchedule(args,validationError);
        return validationError;
    }catch(inputsCatchError){
        console.log('Error in the validateDashboardCommonInputs() function main catch block.',inputsCatchError);
        throw inputsCatchError;
    }
}

module.exports={
    validateSummaryInputs,
    validateTimezoneWorkSchedule,
    validateDashboardCommonInputs
};