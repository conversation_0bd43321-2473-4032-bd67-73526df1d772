// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require moment-timezone
const moment = require('moment-timezone'); 
// require constant values
const { defaultValues } = require('../common/appConstants');

// variable declaration
let defaultRetention=defaultValues.defaultRetentionPeriod;

module.exports = {
    inputValidation : async(args,period) =>{
        // variable declaration
        let validationError = {};
        try{
            // validate start date
            if (!args.startDate) {
                validationError['IVE0045'] = commonLib.func.getError('', 'IVE0045').message;
            }
            // validate end date
            if (!args.endDate) {
                validationError['IVE0046'] = commonLib.func.getError('', 'IVE0046').message1;
            }
            // Check whether both start date and end date exists
            if(args.startDate && args.endDate){
                // end date should be greater than start date
                if (args.endDate < args.startDate) {
                    validationError['IVE0046'] = commonLib.func.getError('', 'IVE0046').message2;
                }
                else{
                    let noOfMonth=(period)?(period>1)?(period):(defaultRetention):(defaultRetention);
                    // we allow user to fetch the data upto the months configure in settings and if it is more than this we throw error
                    let minStartDate=moment().subtract(noOfMonth-1, "months").startOf("month").format("YYYY-MM-DD");
                    if(moment(args.startDate, 'YYYY-MM-DD') < minStartDate){
                        validationError['IVE0152'] = commonLib.func.getError('', 'IVE0152').message;
                    }
                }
            }
            // validate teamReport field (to differentiate team/individual report)
            if (!(args.teamReport===0 || args.teamReport===1)) {
                validationError['IVE0118'] = commonLib.func.getError('', 'IVE0118').message;
            }
            // employee id required for individual reports
            else{
                if(!args.teamReport){
                    if(!args.employeeId){
                        validationError['IVE0083'] = commonLib.func.getError('', 'IVE0083').message;
                    }
                }
            }
            return validationError;
        }
        catch(error){
            console.log('Error in inputValidation catch block',error);
            throw error;
        }
    },
    // function to validate start and end date
    validateStartAndEndDate: async(args,period) =>{
        // variable declaration
        let validationError = {};
        try{
            // validate start date
            if (!args.startDate) {
                validationError['IVE0045'] = commonLib.func.getError('', 'IVE0045').message;
            }
            // validate end date
            if (!args.endDate) {
                validationError['IVE0046'] = commonLib.func.getError('', 'IVE0046').message1;
            }
            // Check whether both start date and end date exists
            if(args.startDate && args.endDate){
                // end date should be greater than start date
                if (args.endDate < args.startDate) {
                    validationError['IVE0046'] = commonLib.func.getError('', 'IVE0046').message2;
                }
                else{
                    let noOfMonth=(period)?(period>1)?(period):(defaultRetention):(defaultRetention);
                    // we allow user to fetch the data upto the months configure in settings and if it is more than this we throw error
                    let minStartDate=moment().subtract(noOfMonth-1, "months").startOf("month").format("YYYY-MM-DD");
                    if(moment(args.startDate,'YYYY-MM-DD')<minStartDate){
                        validationError['IVE0152'] = commonLib.func.getError('', 'IVE0152').message;
                    }
                }
            }            
            return validationError;
        }
        catch(error){
            console.log('Error in validateStartAndEndDate catch block',error);
            throw error;
        }
    },
    /**
     * Function to validate the input date field
     * @param {date} date - Input date field
     * @param {Int} period - period the number of months
     * @param {String} source - Source which is non- mandatory. It will send only from insights form
     * @returns {Object} - Return validationError
     * @throws - Throws an error object if an error occured
     */
    validateInputDate: async(date,period,source='') =>{
        // variable declaration
        let validationError = {};
        try{
            if(date){
                let noOfMonth=(period)?(period>1)?(period):defaultRetention:defaultRetention;
                // we allow user to fetch the data upto the months configure in settings and if it is more than this we throw error
                let minStartDate=moment().subtract(noOfMonth-1, "months").startOf("month").format("YYYY-MM-DD");
                if(moment(date,'YYYY-MM-DD')<minStartDate){
                    validationError['IVE0152'] = commonLib.func.getError('', 'IVE0152').message;
                }
            }
            else{
                validationError['IVE0044'] = commonLib.func.getError('', 'IVE0044').message;
            }
            return validationError;
        }
        catch(error){
            console.log('Error in validateInputDate function catch block',error);
            throw error;
        }
    }
};