// Organization database connection
const knex = require('knex');
// require table alias
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require file to access constant values
const { defaultValues } = require('../common/appConstants');
const moment = require('moment-timezone');
const { updateSummaryDataInMasterTable }=require('./commonFunctions');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//main function to do purge url of uncategorized not used from 3 months.
module.exports.urlPurge  = async(event, context) =>{
    let appmanagerDbConnection
    try{
        console.log('Inside url purge function');
        let purgeMinDate=moment().subtract(3, "months").startOf("month").format("YYYY-MM-DD");
        let instance;
        let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        if(Object.keys(connection).length){
            // form app manager database connection
            appmanagerDbConnection=knex(connection.AppManagerDb);
            instance=await getOpenInstances(appmanagerDbConnection);
        }
        if(instance.length>0)
        {
            let instanceToBeProcessed=(instance.length>defaultValues.empMonitorInstanceCount)?(defaultValues.empMonitorInstanceCount):(instance.length);
            for(let i=0;i<instanceToBeProcessed;i++)
            {   
                let updateMasterTableStatus;
                let orgCode=instance[i]['Org_Code']
                let updateParams={
                    Purge_Status : 'InProgress'
                }
                await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.domainPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);  
                let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appmanagerDbConnection,orgCode);
                if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                    let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                    let connection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode,0,additionalHeaders);
                    if(Object.keys(connection).length)
                    {
                        let check=await purgeUrlData(connection,purgeMinDate);
                        if(check)
                        {
                            updateParams={
                                Purge_Status : 'Success'
                            }
                            updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.domainPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                        }
                        else
                        {
                            updateParams={
                                Purge_Status : 'Failed'
                            }
                            updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.domainPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                        }
                        
                    }
                    else{
                        updateParams={
                            Purge_Status : 'Failed'
                        }
                        updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.domainPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                    }
                    if(updateMasterTableStatus==="success")
                    {
                        console.log("url_purge_manager table updated.")
                    }
                    else{
                        console.log("Error Occured in updateSummaryDataInMasterTable while updating url_purge_manager table")
                    }
                } else {
                    console.log('Error while getting the data region');
                    updateParams={
                        Purge_Status : 'Failed'
                    }
                    updateMasterTableStatus=await updateSummaryDataInMasterTable('',updateParams,orgCode,appManagerTables.domainPurgeManager,process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
                    if(updateMasterTableStatus==="success")
                    {
                        console.log("url_purge_manager table updated.")
                    }
                    else{
                        console.log("Error Occured in updateSummaryDataInMasterTable while updating url_purge_manager table")
                    }
                }
            }
            let response = 
            {
                nextStep:'End',
                message:'Purge process Completed.'
            };
            appmanagerDbConnection?appmanagerDbConnection.destroy():null;
            return response;
        }
        else{
            console.log("No instances Found");
            let response = 
            {
                nextStep:'End',
                message:'Purge process Completed.'
            };
            appmanagerDbConnection?appmanagerDbConnection.destroy():null;
            return response;
        }
        
 }
catch(e){
    console.log("error occure while doing the domain purge",e);
    let response = {
                nextStep:'End',
                message:'Error occured while doing Url purge.'
            };
        return response;
    }
};

//function to get open instances from domain_purge_manager table
async function getOpenInstances(appmanagerDbConnection)
{
    try{
        return(     
        appmanagerDbConnection(appManagerTables.domainPurgeManager)
        .select('Org_Code')
        .where('Purge_Status','open')
        .then(async (summaryData) =>{
            return summaryData;
        })
        .catch((e)=>{
            console.log("error while geting Open Instanaces",e);
            return "";
        })
        )
    }
    catch(e){
        console.log("error in get Open Instances",e);
        return "";
    }
}


//function to purge Url Data
async function purgeUrlData(connection,purgeMinDate)
{
    let organizationDbConnection;
    try{
        organizationDbConnection = knex(connection.OrganizationDb);
        return(
            organizationDbConnection(ehrTables.activityDomainMaster)
            .select('Domain_Id')
            .whereNotIn('Domain_Id', function() {
                this.select('Domain_Id').from(ehrTables.organizationLevelUrlSettings)
                .union([
                    organizationDbConnection.select('Domain_Id').from(ehrTables.customGroupUrlSettings),
                    organizationDbConnection.select('Domain_Id').from(ehrTables.empActivityUrls)
                ])
            })
            .then(async(data)=>
            {
                let domainId=[];
                if(data.length>0)
                {
                    data.forEach(i=>{
                        if(i['domainId']!=null)
                        {   
                            domainId.push(i['domainId']);
                        }
                    })
                }
                if(domainId.length>0)
                {
                    return(
                        organizationDbConnection
                        .transaction(function(trx){
                            return(
                                organizationDbConnection(ehrTables.empActivityUrls)
                                .del()
                                .whereIn('Domain_Id', domainId)
                                .transacting(trx)
                                .then(()=>
                                    {
                                        return(
                                            organizationDbConnection(ehrTables.urlActivityDailySummary)
                                            .whereIn('Domain_Id', domainId)
                                            .del()
                                            .transacting(trx)
                                            .then(()=>{
                                                return(
                                                    organizationDbConnection(ehrTables.activityDomainMaster)
                                                    .whereIn('Domain_Id', domainId)
                                                    .del()
                                                    .transacting(trx)
                                                    .then(()=>{
                                                        organizationDbConnection?organizationDbConnection.destroy():null;
                                                        return true;
            
                                                    })
                                                )
                                            })
                                        )
                                    }
                                )
                            )
                            
                    })
                    .then(()=>{
                        organizationDbConnection?organizationDbConnection.destroy():null;
                        return true;
                    })
                    .catch((e)=>{
                        console.log("error occurd while doing the domain purge",e);
                        organizationDbConnection?organizationDbConnection.destroy():null;
                        return false;
                    })
                    )
                }
                else{
                    organizationDbConnection?organizationDbConnection.destroy():null;
                    return true;
                }
            })
            .catch(e=>
                {
                    console.log("error while getting domain id to be purged",e);
                    organizationDbConnection?organizationDbConnection.destroy():null;
                    return false;
                })
        )
    }
    catch(e){
        console.log("Error Occured while doing Domain_Purge main catch block",e);
        organizationDbConnection?organizationDbConnection.destroy():null;
        return false;
    }
}

