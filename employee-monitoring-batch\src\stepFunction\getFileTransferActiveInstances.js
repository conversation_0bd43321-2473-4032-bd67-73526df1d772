// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//get common functions
const{insertSummaryDataInMasterTable}=require("./commonFunctions");
//get tablealias
const{appManagerTables}=require("../common/tableAlias")
// Organization database connection
const knex = require('knex');
//Require moment
const moment = require('moment-timezone');
const{defaultValues}=require('../common/appConstants')
// Function to get the active subscribed user for DLP file transfer.
module.exports.getFileTransferActiveInstances  = async(event,context) =>{
    let appmanagerDbConnection;
    try{
        // get input data
        let inputStatus=event.status;
        let masterTable=appManagerTables.fileTransferSummarizationManager;
        let inputParams;
        let currentDate=moment.utc().format("YYYY-MM-DD");
        if(inputStatus && (inputStatus.toLowerCase()==='open' || inputStatus.toLowerCase()==='failed'))
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.*/
            console.log('Event triggered to process remaining records so move to step2');
            let response={
                nextStep:'Step2',
                input:{'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            // make database connection
            let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length){
                // form app manager database connection
                appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                //get all the active instances
                let activeSubscribedUsers= await commonLib.func.getInstancesBasedOnPlanStatus(appmanagerDbConnection,defaultValues.activeStatus);
                //remove inactive instances from master table
                await removeInstancesFromAppManager(appmanagerDbConnection,masterTable);
                let inputData=[];
                for(let i=0;i<activeSubscribedUsers.length;i++)
                {
                    inputParams={
                        Org_Code: activeSubscribedUsers[i],
                        Employee_List_Preparation: "Open",
                        Summarization_Status:"Open",
                        Summarization_Date: currentDate
                    }   
                    inputData.push(inputParams);
                }
                let insertStatus=await insertSummaryDataInMasterTable(appmanagerDbConnection,inputData,masterTable);
                appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                if(insertStatus==='success')
                {
                    let response={
                        nextStep:'Step2',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next set of instances.'          
                    }
                    return response;  
                }
                else
                {
                    let response ={
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Error Occured while Updating the table.'
                    };
                    return response;
                }
              
            }
            else{
                console.log('Error while creating app manager database connection in step1');
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response ={
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Occured while Updating the table.'
                };
                return response;
            }
        }
    }
    catch(e)
    {
        console.log("Error in getFileTransferActiveInstances function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response ={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Error Occured while Updating the table.'
        };
        return response;
    }
}

// function to remove user from master table where  master orgcode not exist in orgCodeList
async function removeInstancesFromAppManager(appmanagerDbConnection,masterTable){
    try{
        return(
            appmanagerDbConnection(masterTable)
            .del()
            .then(deleteUser =>{
                return true;
            })
            .catch(catchError=>{
                console.log('Error in removeInstancesFromAppManager function .catch block.', catchError);
                return false;
            })
        );
    }
    catch(error){
        console.log('Error in removeInstancesFromAppManager function main catch block.', error);
        return false;
    }
};
