'use strict';
// require knex for database connection
const knex = require('knex');
// require moment-timezone
const moment = require('moment-timezone'); 
// require table alias function
const { ehrTables,appManagerTables } = require('../common/tableAlias');
// require common function
const commonFunction=require('./commonFunctions');
const employeeMonitoringCommonFunction=require('../webApplication/employeeMonitoringCommonFunction');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

// variable declarations
let appManagerDbConnection='';
let orgDbConnection='';

// function to form the email notification data for sending the template
module.exports.formInsightsNotificationData  = async(event, context) =>{
    let source='';
    try{
        console.log('Inside formInsightsNotificationData function');
        source=(event.input)?(event.input.process):'';
        let workedEmployeeCount;
        // make database connection
        let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,'',1);
        // check whether data exist or not
        if(Object.keys(databaseConnection).length){
            // get app manager database connection
            appManagerDbConnection=knex(databaseConnection.AppManagerDb);
            // check whether records with Open status exist or not
            return(
                appManagerDbConnection
                .select('Org_Code','Activity_Date')
                .from(appManagerTables.insightsNotificationManager)
                .where('Status','Open')
                .then(async(openRecordList)=>{
                    if(openRecordList.length>0){
                        console.log("openRecordList: ", openRecordList)
                        // iterate for all the instances
                        for(let id of openRecordList){
                            console.log('Processing for '+ id.Org_Code+' instances');
                            let orgRegionDetails = await commonLib.func.getOrgRegionDetails(appManagerDbConnection,id.Org_Code);
                            if(orgRegionDetails && Object.keys(orgRegionDetails).length > 0){
                                let additionalHeaders = await commonLib.stepFunctions.formAdditionalHeaders(orgRegionDetails);
                                
                                //Get database connection
                                let databaseConnection=await commonLib.stepFunctions.getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,id.Org_Code,0,additionalHeaders);

                                orgDbConnection=knex(databaseConnection.OrganizationDb);
                                // check whether summary record exist for the input date
                                return(
                                    orgDbConnection
                                    .select('Employee_Id')
                                    .from(ehrTables.employeeActivityDailySummary)
                                    .where('Activity_Date',id.Activity_Date)
                                    .then(async(getSummaryData)=>{
                                        let workedEmployeeIds = getSummaryData.map(el => el.Employee_Id);  
                                        if(getSummaryData.length>0){
                                            workedEmployeeCount=getSummaryData.length;
                                            // update the status as initiated in manager table
                                            let updateParams={
                                                Status:'Initiated'
                                            }
                                            let updateStatus=await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,id.Org_Code);
                                            
                                            // delete the status log table
                                            return (
                                                orgDbConnection(ehrTables.insightsNotificationStatusLog)
                                                .del()
                                                .then(async(deleteUser) =>{
                                                    let idArray=[];
                                                    /** If source is employee then get the employeeIds based on settings */
                                                    if(source==='employee'){
                                                        idArray=await commonFunction.getSettingsEnabledEmployeeIds(orgDbConnection);
                                                        try{
                                                            if(id.Org_Code == 'imc'){
                                                                console.log("idArray: ",idArray);
                                                            }
                                                        } catch(err){
                                                            console.log("Error in testlog", err);
                                                        }
                                                    }
                                                    /** when source is either admin or manager */
                                                    else{
                                                        /** if source is admin then get all admin empIds. In case of manager get the manager id who are only the manager and not a admin */
                                                        idArray=await commonFunction.getEmpMonitoringActiveEmpIds(orgDbConnection,source);
                                                    }
                                                    if(idArray.length>0){
                                                        // get the email address for all the employee-ids
                                                        let getEmpEmailDetails=await employeeMonitoringCommonFunction.getEmployeeEmail(idArray,orgDbConnection);        
                                                        let insertParams = getEmpEmailDetails.map(field => ({
                                                            Employee_Id: field.Employee_Id,
                                                            Status:(field.Emp_Email)?('Open'):('Failure'), // if emailId is not exist then update as failure
                                                            Emp_Email:field.Emp_Email,
                                                            Reason:(field.Emp_Email)?'':'Email not exist', // if emailId is not exist then update the reason
                                                            Activity_Date: id.Activity_Date,
                                                            Updated_On: moment().format('YYYY-MM-DD HH:mm:ss')
                                                        }))
                                                        try{
                                                            if(id.Org_Code == 'imc'){
                                                                console.log("insertParams: ",insertParams);
                                                            }
                                                        } catch(err){
                                                            console.log("Error in testlog", err);
                                                        }
                                                        return(
                                                            orgDbConnection
                                                            .insert(insertParams)
                                                            .from(ehrTables.insightsNotificationStatusLog)
                                                            .then(async(getData)=>{
                                                                if(source==='admin'){
                                                                    // get the details that need to pass to notification template
                                                                    let notificationData=await getNotificationData(orgDbConnection,source,id.Activity_Date,workedEmployeeIds);
                                                                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                                    orgDbConnection?orgDbConnection.destroy():null;
                                                                    let response = {
                                                                        nextStep:'Step3',
                                                                        input:{'process':source,'templateData':JSON.stringify(notificationData),'orgCode':id.Org_Code,'employeeId':''},
                                                                        message:'Trigger email notification.'
                                                                    };
                                                                    return response;
                                                                }
                                                                else if(source==='manager'){
                                                                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                                    orgDbConnection?orgDbConnection.destroy():null;
                                                                    let response = {
                                                                        nextStep:'Step4',
                                                                        input:{'process':source,'orgCode':id.Org_Code,'activityDate':id.Activity_Date},
                                                                        message:'Form insights notification data for managers.'
                                                                    };
                                                                    return response;
                                                                }
                                                                else{
                                                                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                                    orgDbConnection?orgDbConnection.destroy():null;
                                                                    let response = {
                                                                        nextStep:'Step4',
                                                                        input:{'process':source,'orgCode':id.Org_Code,'activityDate':id.Activity_Date},
                                                                        message:'Form insights notification data for team members.'
                                                                    };
                                                                    return response;
                                                                }
                                                            })
                                                        );
                                                    }
                                                    else{
                                                        console.log('There is no active admin/manager exist or error while getting manager/admin employeeIds');
                                                        let updateParams={
                                                            Status:'Failure'
                                                        }
                                                        await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,id.Org_Code);
                                                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                                        orgDbConnection?orgDbConnection.destroy():null;
                                                        let response = {
                                                            nextStep:'Step2',
                                                            input:{'process':source},
                                                            message:'Process the next record'
                                                        };
                                                        return response;
                                                    }
                                                })
                                            );
                                        }
                                        /** If there is no tracking for the input date then process the next record.
                                        And update the status as success-NA(No activity) */
                                        else{
                                            console.log('No activity details found. So process the next record.');
                                            let updateParams={
                                                Status:'Success'
                                            }
                                            await commonFunction.updateNotificationManagerTable(appManagerDbConnection,updateParams,id.Org_Code);
                                            appManagerDbConnection ? appManagerDbConnection.destroy():null;
                                            orgDbConnection?orgDbConnection.destroy():null;
                                            let response = {
                                                nextStep:'Step2',
                                                input:{'process':source},
                                                message:'Process the next record'
                                            };
                                            return response;
                                        }
                                    })
                                );
                            } else{
                                console.log('Error while getting the data region for '+ id.Org_Code+' instances');
                            }
                        }
                    }
                    else{
                        console.log('No open record exist in manager table. So stop the process execution');
                        appManagerDbConnection ? appManagerDbConnection.destroy():null;
                        orgDbConnection?orgDbConnection.destroy():null;
                        let response = 
                        {
                            nextStep:'End',
                            input:{'process':source},
                            message:'No open records found in app manager table.'
                        };
                        return response;
                    }
                })
                .catch(catchError=>{
                    console.log('Error in formInsightsNotificationData function .catch block.', catchError);
                    appManagerDbConnection ? appManagerDbConnection.destroy():null;
                    orgDbConnection?orgDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'End',
                        input:{'process':source},
                        message:'Error in step2 function .catch block.'
                    };
                    return response;
                })
            );
        }
        else{
            console.log('Error while making database connection');
            appManagerDbConnection ? appManagerDbConnection.destroy():null;
            orgDbConnection?orgDbConnection.destroy():null;
            let response = 
            {
                nextStep:'End',
                input:{'process':source},
                message:'Error while making database connection'
            };
            return response;
        }
    }
    catch (mainCatchError){
        console.log('Error in formInsightsNotificationData function main catch block.', mainCatchError);
        appManagerDbConnection ? appManagerDbConnection.destroy():null;
        orgDbConnection?orgDbConnection.destroy():null;
        let response = 
        {
            nextStep:'End',
            input:{'process':source},
            message:'Error from step2 main catch block'
        };
        return response;
    }
};

// function to form the notification data
async function getNotificationData(orgDbConnection,source,date,workedEmployeeIds){
    let outputResponse={
        workedEmployeeCount:'',
        notWorkedEmployeeCount:'',
        activeEmpIdArray:[],
        activityData:[],
        lowActivity:[],
        highActivity:[],
        appData:[],
        urlData:[],
        activityDate:date
    };
    try{
        let monitoringMode=await employeeMonitoringCommonFunction.getOrganizationMonitoringMode(orgDbConnection);
        let totalEmployeeCount;
        let notWorkedEmployees;
        // get the total active employees list
        let activeEmpIdArray=await employeeMonitoringCommonFunction.getEmployeeIds(orgDbConnection,['Active']);
        // if the mode is stealth then get the employees only mapped with the assets
        if(monitoringMode && monitoringMode.toLowerCase()==="stealth mode"){
            // get the active employeecount
            let totalActiveEmpIds=await employeeMonitoringCommonFunction.getEmployeeIds(orgDbConnection,['Active'],monitoringMode);
            
            totalEmployeeCount=totalActiveEmpIds.length;
            notWorkedEmployees = totalActiveEmpIds.filter(x => !workedEmployeeIds.includes(x));
        }
        else{
            totalEmployeeCount=activeEmpIdArray.length;
            notWorkedEmployees = activeEmpIdArray.filter(x => !workedEmployeeIds.includes(x)); 
        }

        if(totalEmployeeCount>0){
            // calculate the employee not worked count for presenting in template
            outputResponse['workedEmployeeCount']=workedEmployeeIds.length;
            outputResponse['notWorkedEmployeeCount']=notWorkedEmployees.length;
            outputResponse['activeEmpIdArray']=workedEmployeeIds;
            outputResponse['activityData']=await commonFunction.formActivityData(orgDbConnection,workedEmployeeIds,date);
            let lowHighAppUrlData=await commonFunction.insightsDetails(orgDbConnection,workedEmployeeIds,date,source);
            outputResponse['lowActivity']=lowHighAppUrlData.lowActivityEmployeeDetails;
            outputResponse['highActivity']=lowHighAppUrlData.highActivityEmployeeDetails;
            outputResponse['appData']=lowHighAppUrlData.appData;
            outputResponse['urlData']=lowHighAppUrlData.urlData;
        }
        return outputResponse;
    }
    catch (catchError){
        console.log('Error in getNotificationData function main catch block',catchError);
        return outputResponse;
    }
};
